package moonstone.web.core.image.slice;

import org.springframework.util.DigestUtils;

/**
 * <AUTHOR>
 */
public interface EncryptIdSlice {
    /**
     * 生成校验的Str
     *
     * @param head 头
     * @param salt 盐
     * @return str
     */
    static String generateVerifyId(String head, String salt) {
        return String.format("%s-%s", head, DigestUtils.md5DigestAsHex((salt + head).getBytes()));
    }

    /**
     * 校验数据
     *
     * @param name 名字
     * @param salt 盐
     * @return 是否匹配
     */
    static boolean match(String name, String salt) {
        String[] part = name.split("-");
        return generateVerifyId(part[0], salt).equals(name);
    }
}
