package moonstone.web.front.promotions.convert;

import lombok.extern.slf4j.Slf4j;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.promotion.component.SubStoreOnlyManager;
import moonstone.promotion.dto.RichPromotion;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.model.ServiceProvider;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PromotionDecorator {

    @Resource
    private ItemReadService itemReadService;

    @Resource
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private SubStoreOnlyManager subStoreOnlyManager;

    private static final String KEY_ACTIVITY_ITEM_NAME = "activityItemName";
    private static final String KEY_ACTIVITY_SERVICE_PROVIDER_NAME = "activityServiceProviderName";
    private static final String KEY_ITEM_NAME = "itemName";

    public void decorate(List<RichPromotion> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return;
        }

        var promotionType = PromotionType.from(sourceList.get(0).getPromotion().getType());
        if (promotionType == null) {
            return;
        }

        switch (promotionType) {
            case SUB_STORE_ONLY -> decorateSubStoreOnly(sourceList);
            default -> {
            }
        }
    }

    private void decorateSubStoreOnly(List<RichPromotion> sourceList) {
        // 活动商品名称
        appendActivityItemName(sourceList);

        // 服务商名称
        appendServiceProviderName(sourceList);

        // 限定商品名称
        appendLimitedItemName(sourceList);
    }

    private void appendLimitedItemName(List<RichPromotion> sourceList) {
        var itemIds = sourceList.stream()
                .map(RichPromotion::getPromotion)
                .filter(Objects::nonNull)
                .map(promotion -> subStoreOnlyManager.getLimitedItemId(promotion))
                .flatMap(Collection::stream)
                .toList();

        var itemMap = findItemMap(itemIds);
        if (CollectionUtils.isEmpty(itemMap)) {
            return;
        }

        sourceList.forEach(source -> appendLimitedItemName(source, RichPromotion::getPromotion, itemMap));
    }

    public <T> void appendLimitedItemName(T t, Function<T, Promotion> getPromotion, Map<Long, Item> itemMap) {
        var promotion = getPromotion.apply(t);

        var itemIds = subStoreOnlyManager.getLimitedItemId(promotion);
        if (CollectionUtils.isEmpty(itemIds)) {
            return;
        }

        if (CollectionUtils.isEmpty(promotion.getSkuScopeParams())) {
            promotion.setSkuScopeParams(new HashMap<>());
        }
        promotion.getSkuScopeParams().put(KEY_ITEM_NAME, itemIds.stream()
                .map(itemMap::get).filter(Objects::nonNull).map(Item::getName).collect(Collectors.joining(",")));
    }

    private void appendServiceProviderName(List<RichPromotion> sourceList) {
        var serviceProviderIds = sourceList.stream()
                .map(RichPromotion::getPromotion)
                .filter(Objects::nonNull)
                .map(promotion -> subStoreOnlyManager.getServiceProviderId(promotion))
                .flatMap(Collection::stream)
                .toList();

        var serviceProviderMap = findServiceProviderMap(serviceProviderIds);
        if (CollectionUtils.isEmpty(serviceProviderMap)) {
            return;
        }

        sourceList.forEach(source -> appendServiceProviderName(source, serviceProviderMap));
    }

    private void appendServiceProviderName(RichPromotion source, Map<String, ServiceProvider> serviceProviderMap) {
        var promotion = source.getPromotion();

        var serviceProviderIds = subStoreOnlyManager.getServiceProviderId(promotion);
        if (CollectionUtils.isEmpty(serviceProviderIds)) {
            return;
        }

        if (CollectionUtils.isEmpty(promotion.getUserScopeParams())) {
            promotion.setUserScopeParams(new HashMap<>());
        }
        promotion.getUserScopeParams().put(KEY_ACTIVITY_SERVICE_PROVIDER_NAME, serviceProviderIds.stream()
                .map(serviceProviderMap::get).filter(Objects::nonNull).map(ServiceProvider::getName).collect(Collectors.joining(",")));
    }

    private Map<String, ServiceProvider> findServiceProviderMap(List<String> serviceProviderIds) {
        if (CollectionUtils.isEmpty(serviceProviderIds)) {
            return Collections.emptyMap();
        }

        var list = serviceProviderCache.findByIds(serviceProviderIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(ServiceProvider::getId, o -> o, (k1, k2) -> k1));
    }

    private void appendActivityItemName(List<RichPromotion> sourceList) {
        var activityItemIds = sourceList.stream()
                .map(RichPromotion::getPromotion)
                .filter(Objects::nonNull)
                .map(promotion -> subStoreOnlyManager.getActivityItemId(promotion))
                .flatMap(Collection::stream)
                .toList();

        var itemMap = findItemMap(activityItemIds);
        if (CollectionUtils.isEmpty(itemMap)) {
            return;
        }

        sourceList.forEach(source -> appendActivityItemName(source, itemMap));
    }

    private void appendActivityItemName(RichPromotion source, Map<Long, Item> itemMap) {
        var promotion = source.getPromotion();

        var itemIds = subStoreOnlyManager.getActivityItemId(promotion);
        if (CollectionUtils.isEmpty(itemIds)) {
            return;
        }

        if (CollectionUtils.isEmpty(promotion.getExtra())) {
            promotion.setExtra(new HashMap<>());
        }
        promotion.getExtra().put(KEY_ACTIVITY_ITEM_NAME, itemIds.stream()
                .map(itemMap::get).filter(Objects::nonNull).map(Item::getName).collect(Collectors.joining(",")));
    }

    public Map<Long, Item> findItemMap(List<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyMap();
        }

        var list = itemReadService.findByIds(itemIds).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(Item::getId, o -> o, (k1, k2) -> k1));
    }
}
