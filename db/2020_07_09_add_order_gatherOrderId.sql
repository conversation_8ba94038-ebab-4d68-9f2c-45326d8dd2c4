alter table parana.parana_shop_orders add column gather_order_id bigint;
alter table parana.parana_sku_orders add column gather_order_id bigint;
CREATE INDEX parana_shop_orders_gather_order_id_IDX USING BTREE ON parana.parana_shop_orders (gather_order_id);
CREATE INDEX parana_sku_orders_gather_order_id_IDX USING BTREE ON parana.parana_sku_orders (gather_order_id);

CREATE INDEX parana_shop_orders_out_from_IDX USING BTREE ON parana.parana_shop_orders (out_from);
CREATE INDEX parana_sku_orders_out_from_IDX USING BTREE ON parana.parana_sku_orders (out_from);

ALTER TABLE parana.we_shop_skus MODIFY COLUMN tax_seller_bear int(11) DEFAULT false NOT NULL;
CREATE TABLE parana.parana_gather_order (
	id BIGINT auto_increment NULL,
	shop_id BIGINT NOT NULL,
	buyer_id BIGINT NOT NULL,
	fee BIGINT DEFAULT 0 NOT NULL,
	status INT DEFAULT 1 NOT NULL,
	promotion_id BIGINT DEFAULT -1 NULL,
	out_from varchar(100) NULL,
	extra_json varchar(254) NULL,
	tags_json varchar(254) NULL,
	out_shop_id varchar(254) NULL,
	`type` INT DEFAULT 1 NOT NULL,
	origin_fee BIGINT DEFAULT 0 NOT NULL,
	auth_status int DEFAULT 0 NOT NULL,
	auth_at TIMESTAMP NULL,
	declare_id varchar(254) NULL,
	created_at TIMESTAMP NULL,
	updated_at TIMESTAMP NULL,
	CONSTRAINT parana_gather_order_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8
COLLATE=utf8_general_ci;
CREATE INDEX parana_gather_order_shop_id_IDX USING BTREE ON parana.parana_gather_order (shop_id);
CREATE INDEX parana_gather_order_buyer_id_IDX USING BTREE ON parana.parana_gather_order (buyer_id,out_shop_id);
CREATE INDEX parana_gather_order_declare_id_IDX USING BTREE ON parana.parana_gather_order (declare_id);
