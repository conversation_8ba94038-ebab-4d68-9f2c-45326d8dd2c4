package moonstone.web.front.shop.substore.component;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.service.ShopOrderReadService;
import moonstone.shop.dto.SubStoreTStoreGuiderCriteria;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.enums.DataExportTaskTypeEnum;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.model.parameter.UserRelationEntityQueryParameter;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.util.BaseExporter;
import moonstone.web.front.shop.substore.convert.GuiderExportConvertor;
import moonstone.web.front.shop.substore.vo.GuiderExcelExportView;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 导购信息-导出
 */
@Slf4j
@Component
public class GuiderExporter extends BaseExporter<GuiderExcelExportView, GuiderExporter.ExportParameter> {

    @Resource
    private SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;

    @Resource
    private SubStoreReadService subStoreReadService;

    @Resource
    private UserRelationEntityReadService userRelationEntityReadService;

    @Resource
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Override
    protected DataExportTaskTypeEnum getCurrentTaskType() {
        return DataExportTaskTypeEnum.GUIDER_INFORMATION;
    }

    @Override
    protected List<GuiderExcelExportView> findPageDataList(ExportParameter queryParameter, int pageNo, int pageSize) {
        //导购基本信息查询
        var list = findList(queryParameter, pageNo, pageSize);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        //填充门店信息
        appendSubStoreInfo(list);

        //填充服务商信息
        appendServiceProviderInfo(list);

        //填充订单信息
        appendShopOrderInfo(list);

        return list;
    }

    /**
     * 填充订单信息
     *
     * @param list
     */
    private void appendShopOrderInfo(List<GuiderExcelExportView> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        var statisticsMap = shopOrderReadService.findGuiderStatistics(
                list.get(0).getShopId(), list.stream().map(GuiderExcelExportView::getUserId).toList());
        if(CollectionUtils.isEmpty(statisticsMap)){
            return;
        }

        list.forEach(target -> GuiderExportConvertor.appendOrderInfo(target, statisticsMap.get(target.getUserId())));
    }

    /**
     * 填充服务商信息
     *
     * @param list
     */
    private void appendServiceProviderInfo(List<GuiderExcelExportView> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        //查询门店的上级关系信息
        var relationMap = userRelationEntityReadService.findUserRelationEntity(
                list.stream().map(GuiderExcelExportView::getSubStoreUserId).toList(), list.get(0).getShopId());
        if (CollectionUtils.isEmpty(relationMap)) {
            return;
        }

        //填充
        list.forEach(target -> {
            var relation = relationMap.get(target.getSubStoreUserId());
            if (relation == null) {
                return;
            }

            var serviceProvider = serviceProviderCache.findServiceProviderByUserIdAndShopId(
                    relation.getRelationId(), relation.getAdditionRelationA());
            if (serviceProvider == null) {
                return;
            }

            GuiderExportConvertor.appendServiceProviderInfo(target, serviceProvider);
        });
    }

    /**
     * 填充门店信息
     *
     * @param list
     */
    private void appendSubStoreInfo(List<GuiderExcelExportView> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        var storeMap = subStoreReadService.findMapByIds(
                list.stream().map(GuiderExcelExportView::getSubStoreId).toList());
        if (CollectionUtils.isEmpty(storeMap)) {
            return;
        }

        list.forEach(target -> GuiderExportConvertor.appendSubStoreInfo(target, storeMap.get(target.getSubStoreId())));
    }

    /**
     * 导购基本信息查询
     *
     * @param queryParameter
     * @param pageNo
     * @param pageSize
     * @return
     */
    private List<GuiderExcelExportView> findList(ExportParameter queryParameter, int pageNo, int pageSize) {
        var criteria = new SubStoreTStoreGuiderCriteria();

        criteria.setShopId(queryParameter.getShopId());
        criteria.setStoreGuiderNickname(queryParameter.getGuiderName());
        criteria.setStoreGuiderMobile(queryParameter.getGuiderMobile());
        criteria.setSubStoreIds(queryParameter.getSubStoreIds());

        criteria.setPageNo(pageNo);
        criteria.setPageSize(pageSize);

        var list = subStoreTStoreGuiderReadService.pageList(criteria).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return GuiderExportConvertor.covert(list);
    }

    @Data
    public static class ExportParameter {
        private Long shopId;
        private List<Long> subStoreIds;
        private String guiderName;
        private String guiderMobile;
    }
}
