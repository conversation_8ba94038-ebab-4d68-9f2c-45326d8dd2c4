package moonstone.weCart.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.model.Indexable;
import io.terminus.common.utils.JsonMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/12/19.
 */
@ToString
@EqualsAndHashCode(of = {"buyerId", "weShopId", "skuId"})
public class WeCartItem implements Serializable, Indexable {
    private static final long serialVersionUID = -624676591986668553L;

    private static final ObjectMapper objectMapper = JsonMapper.JSON_NON_EMPTY_MAPPER.getMapper();
    private static final TypeReference<HashMap<String, Object>> EXTRA_MAP = new TypeReference<HashMap<String, Object>>() {
    };

    /**
     * 主键id
     */
    @Getter
    @Setter
    private Long id;

    /**
     * 买家id
     */
    @Getter
    @Setter
    private Long buyerId;

    /**
     * 微分销店铺id
     */
    @Getter
    @Setter
    private Long weShopId;

    /**
     * 商家id
     */
    @Getter
    @Setter
    private Long shopId;

    /**
     * sku id
     */
    @Getter
    @Setter
    private Long skuId;

    /**
     * 在购物车中本sku的 数目
     */
    @Getter
    @Setter
    private Integer quantity;

    /**
     * 加入购物车时本商品的价格
     */
    @Getter
    @Setter
    private Integer snapshotPrice;

    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 修改时间
     */
    @Getter
    @Setter
    private Date updatedAt;

    @Getter
    @JsonIgnore
    private String extraJson;

    @Getter
    private Map<String, Object> extraMap;

    public void setExtraJson(String extraJson) throws IOException {
        this.extraJson = extraJson;
        if (Strings.isNullOrEmpty(extraJson)) {
            this.extraMap = Collections.emptyMap();
        } else {
            this.extraMap = objectMapper.readValue(extraJson, EXTRA_MAP);
        }
    }

    public void setExtraMap(Map<String, Object> extraMap) throws Exception {
        this.extraMap = extraMap;

        if (CollectionUtils.isEmpty(extraMap)) {
            this.extraJson = null;
        } else {
            this.extraJson = objectMapper.writeValueAsString(extraMap);
        }
    }
}
