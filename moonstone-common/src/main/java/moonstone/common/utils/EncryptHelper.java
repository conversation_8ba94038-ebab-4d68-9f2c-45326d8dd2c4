package moonstone.common.utils;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.KeyStore;
import java.util.Base64;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * 加密工具
 */
@Slf4j
public enum EncryptHelper {
    /**
     * 加密实例
     */
    instance;

    final Key TIMEOUT_KEY = getTimeoutKey();
    private final CompletableFuture<String> secret = new CompletableFuture<>();
    private final CompletableFuture<KeyStore> keyStore = new CompletableFuture<>();
    private final ConcurrentHashMap<String, Key> keyConcurrentHashMap = new ConcurrentHashMap<>();

    public static void main(String[] args) {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
            keyGenerator.init(256);
            SecretKey secretKey = keyGenerator.generateKey();
            String key = Base64.getEncoder().encodeToString(secretKey.getEncoded());
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            String data = "OK";
            byte[] en = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            log.debug("{}", key);
            SecretKeySpec keySpec = new SecretKeySpec(Base64.getDecoder().decode(key.getBytes(StandardCharsets.UTF_8)), "AES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            log.info("OUT -> {}", new String(cipher.doFinal(en)));
        } catch (Exception e) {
            log.error("{} fail to generate", LogUtil.getClassMethodName(), e);
        }
    }

    private Key getTimeoutKey() {
        try {
            return new SecretKeySpec(B3Util.b3sum("TIME_OUT".getBytes()), "AES");
        } catch (Exception exception) {
            return null;
        }
    }

    /**
     * 置入密钥
     *
     * @param secret 密钥由Environment获取
     * @return 自身
     */
    public EncryptHelper secret(String secret) {
        this.secret.complete(secret);
        return this;
    }

    /**
     * 置入已经打开的密钥管理器
     *
     * @param keyStore 已经load的密钥管理器
     * @return 自身
     */
    public EncryptHelper keyStore(KeyStore keyStore) {
        this.keyStore.complete(keyStore);
        return instance;
    }

    /**
     * 堵塞式获取密钥
     *
     * @return 密钥
     */
    private char[] secret() {
        try {
            return secret.get(3, TimeUnit.MINUTES).toCharArray();
        } catch (Exception e) {
            log.error("{} secret load fail", LogUtil.getClassMethodName(), e);
            return new char[0];
        }
    }

    public Key exportKey(KeyEnu keyEnu) {
        if (keyEnu == KeyEnu.TimeOut) {
            return TIMEOUT_KEY;
        }
        return getKey(keyEnu);
    }

    private Key getKey(KeyEnu keyEnu) {
        char[] secret = secret();
        Key key = Optional.ofNullable(keyConcurrentHashMap.get(keyEnu.name()))
                .orElseGet(() -> {
                    KeyStore store;
                    try {
                        store = keyStore.get(3, TimeUnit.MINUTES);
                    } catch (Exception exception) {
                        log.error("{} keystore load fail, using TIME_OUT key", LogUtil.getClassMethodName(), exception);
                        return TIMEOUT_KEY;
                    }
                    try {
                        return store.getKey(keyEnu.name(), secret);
                    } catch (Exception ignore) {
                        return null;
                    }
                });
        if (key != TIMEOUT_KEY) {
            keyConcurrentHashMap.putIfAbsent(keyEnu.name(), key);
        }
        return key;
    }

    public Either<ByteArrayOutputStream> decryptBaseEncoded(KeyEnu keyEnu, String encodeData) {
        return decrypt(keyEnu, Base64.getDecoder().decode(encodeData.getBytes()));
    }

    public Either<ByteArrayOutputStream> decrypt(KeyEnu keyEnu, byte[] data) {
        try {
            Key key = getKey(keyEnu);
            Cipher cipher = Cipher.getInstance(key.getAlgorithm());
            cipher.init(Cipher.DECRYPT_MODE, key);
            ByteArrayOutputStream buff = new ByteArrayOutputStream();
            buff.write(cipher.doFinal(data));
            return Either.ok(buff);
        } catch (Exception e) {
            log.error("fail to decode data with key[{}] data[length=>{}]", keyEnu.name(), data.length, e);
            return Either.error(e);
        }
    }

    /**
     * encrypt data with key
     *
     * @param keyEnu prepared key
     * @param data   data
     * @return Either<Success, Throwable> base64 encoded data
     */
    public Either<String> encrypt(KeyEnu keyEnu, byte[] data) {
        try {
            return Either.ok(Base64.getEncoder().encodeToString(encryptToByte(keyEnu, data).take().toByteArray()));
        } catch (Exception exception) {
            log.error("fail to encrypt data of key[{}] data[length=>{}]", keyEnu.name(), data.length, exception);
            return Either.error(exception);
        }
    }

    public Either<ByteArrayOutputStream> encryptToByte(KeyEnu keyEnu, byte[] data) {
        try {
            Key key = getKey(keyEnu);
            Cipher cipher = Cipher.getInstance(key.getAlgorithm());
            cipher.init(Cipher.ENCRYPT_MODE, key);
            ByteArrayOutputStream buff = new ByteArrayOutputStream();
            buff.write(cipher.doFinal(data));
            return Either.ok(buff);
        } catch (Exception exception) {
            log.error("fail to encrypt data of key[{}] data[length=>{}]", keyEnu.name(), data.length, exception);
            return Either.error(exception);
        }
    }

    /**
     * keystore内的密钥枚举, 不要错误使用, 新增前请确保密钥正常存在
     */
    @AllArgsConstructor
    @Getter
    public enum KeyEnu {
        /**
         * 通常使用的密钥
         */
        CommonKey, TimeOut
    }
}
