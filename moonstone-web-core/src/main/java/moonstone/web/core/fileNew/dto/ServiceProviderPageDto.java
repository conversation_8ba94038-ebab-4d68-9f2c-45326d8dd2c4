package moonstone.web.core.fileNew.dto;

import lombok.Getter;
import lombok.Setter;
import moonstone.common.model.dto.PageDto;

import java.util.List;

/**
 * author：书生
 */
@Getter
@Setter
public class ServiceProviderPageDto extends PageDto {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 服务商名称
     */
    private String name;

    /**
     * 服务商手机号
     */
   private String mobile;

   /**
     * 服务商地址
     */
    private String address;

    /**
     * 服务商认证地址
     */
    private String authAddress;

    /**
     * 服务商id列表
     */
    private List<String> idList;

}
