package moonstone.order.model;

import lombok.Data;

import java.util.Date;

/**
  * <AUTHOR>
  */
@Data
public class RefundProcessRecord {

    /**
     * id
     */
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 售后单id
     */
    private Long refundId;

    /**
     * 售后单操作所在轮数
     */
    private Integer refundRound;

    /**
     * 节点类型
     */
    private String type;

    /**
     * 操作人类型
     */
    private Integer operatorType;

    /**
     * 操作人的用户id
     */
    private Long operatorId;

    /**
     * 操作人的用户名称
     */
    private String operatorName;

    /**
     * 操作时间
     */
    private Date operationTime;

    /**
     * 退款金额
     */
    private Long refundFee;

    /**
     * 售后类型
     */
    private Integer refundType;

    /**
     * 当前节点的其它专有属性内容
     */
    private String content;

    /**
     * 是否删除（0：否，1：是）
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 修改时间
     */
    private Date updatedAt;
}
