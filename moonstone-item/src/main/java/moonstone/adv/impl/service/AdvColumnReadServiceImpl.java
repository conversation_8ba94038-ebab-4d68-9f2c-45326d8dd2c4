package moonstone.adv.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.adv.impl.dao.AdvColumnDao;
import moonstone.adv.model.AdvColumn;
import moonstone.adv.service.AdvColumnReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/10/9.
 */
@Slf4j
@Service
@RpcProvider
public class AdvColumnReadServiceImpl implements AdvColumnReadService{
    @Autowired
    private AdvColumnDao advColumnDao;

    @Override
    public Response<List<AdvColumn>> list(){
        try {
            List<AdvColumn> advColumnList = advColumnDao.list();
            return Response.ok(advColumnList);
        } catch (Exception e) {
            log.error("fail to list all columns, cause: {}", Throwables.getStackTraceAsString(e));
            return Response.fail("adv.column.list.fail");
        }
    }

    @Override
    public Response<AdvColumn> findById(Long id){
        try {
            AdvColumn advColumn = advColumnDao.findById(id);
            return Response.ok(advColumn);
        } catch (Exception e) {
            log.error("fail to find column by id={}, cause: {}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("adv.column.find.fail");
        }
    }

    @Override
    public Response<List<AdvColumn>> findBy(List<Long> ids, String sn,
                                            Integer platform, Integer canSellerEdit,
                                            String width, String height, Integer position){
        try {
            Map<String, Object> criteria = new HashMap<>();
            if (ids != null && !CollectionUtils.isEmpty(ids)){
                criteria.put("ids", ids);
            }
            if (StringUtils.hasText(sn)){
                criteria.put("sn", sn);
            }
            if (platform != null){
                criteria.put("platform", platform);
            }
            if (canSellerEdit != null){
                criteria.put("canSellerEdit", canSellerEdit);
            }
            if (StringUtils.hasText(width)){
                criteria.put("width", width);
            }
            if (StringUtils.hasText(height)){
                criteria.put("height", height);
            }
            if (position != null){
                criteria.put("position", position);
            }
            List<AdvColumn> advColumnList = advColumnDao.findBy(criteria);
            return Response.ok(advColumnList);
        } catch (Exception e){
            log.error("fail to find adv column by ids={}, sn={}, platform={}, canSellerEdit={}, width={}, height={}, position={}, cause:{}",
                    ids, sn, platform, canSellerEdit, width, height, position, Throwables.getStackTraceAsString(e));
            return Response.fail("adv.column.find.fail");
        }
    }

    @Override
    public Response<List<AdvColumn>> findByPlatformAndCanSellerEdit(Integer platform, Integer canSellerEdit){
        try {
            Map<String, Object> criteria = new HashMap<>();
            if (platform != null){
                criteria.put("platform", platform);
            }
            if (canSellerEdit != null){
                criteria.put("canSellerEdit", canSellerEdit);
            }
            List<AdvColumn> advColumnList = advColumnDao.findBy(criteria);
            return Response.ok(advColumnList);
        } catch (Exception e) {
            log.error("fail to find columns by platform={}, canSellerEdit={}, cause: {}", platform, canSellerEdit, Throwables.getStackTraceAsString(e));
            return Response.fail("adv.column.find.fail");
        }
    }
}
