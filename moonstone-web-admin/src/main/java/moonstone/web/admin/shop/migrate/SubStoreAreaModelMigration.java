package moonstone.web.admin.shop.migrate;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.item.emu.ItemExtraIndex;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.web.admin.shop.migrate.request.MigrateRequest;
import moonstone.web.core.component.item.model.SubStoreAreaModel;
import moonstone.web.core.shop.model.ServiceProvider;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class SubStoreAreaModelMigration {

    @Resource
    private MongoTemplate mongoTemplate;

    @Resource
    private ItemReadService itemReadService;

    /**
     * 迁移内容: (量小，随便搞)
     * SubStoreAreaModel (mongoDB)
     *
     * @param request
     * @return
     */
    @PostMapping("/api/shop/subStoreAreaModel/migrate")
    public APIResp<Boolean> migrate(@RequestBody MigrateRequest request) {
        if (request == null || request.getSourceShopId() == null || request.getTargetShopId() == null) {
            return APIResp.error("入参缺失");
        }

        var sourceList = mongoTemplate.find(
                Query.query(Criteria.where("shopId").is(request.getSourceShopId())), SubStoreAreaModel.class);
        if (CollectionUtils.isEmpty(sourceList)) {
            return APIResp.ok(true, "源商家的SubStoreAreaModel数据集为空");
        }

        // key = 源itemId, value = 目标itemId
        var itemIdMap = findItemIdMap(request.getTargetShopId());

        // key = 源serviceProviderId, value = 目标serviceProviderId
        var serviceProviderIdMap = findServiceProviderIdMap(request.getTargetShopId());

        sourceList.forEach(model -> {
            model.setId(null);
            model.setShopId(request.getTargetShopId());

            model.setItemId(convertItemId(model.getItemId(), itemIdMap));
            model.setServiceProviderId(convertServiceProviderId(model.getServiceProviderId(), serviceProviderIdMap));
            model.setArea(model.getServiceProviderId());
        });
        mongoTemplate.insertAll(sourceList);

        return APIResp.ok(true);
    }

    private List<String> convertServiceProviderId(List<String> sourceServiceProviderIds, Map<String, String> serviceProviderIdMap) {
        if (CollectionUtils.isEmpty(sourceServiceProviderIds)) {
            return Collections.emptyList();
        }

        return sourceServiceProviderIds.stream()
                .map(serviceProviderIdMap::get)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    private List<Long> convertItemId(List<Long> sourceItemIds, Map<Long, Long> itemIdMap) {
        if (CollectionUtils.isEmpty(sourceItemIds)) {
            return Collections.emptyList();
        }

        return sourceItemIds.stream()
                .map(itemIdMap::get)
                .filter(targetItemId -> targetItemId != null && !targetItemId.equals(-1L))
                .collect(Collectors.toList());
    }

    /**
     * @param targetShopId
     * @return key = 源serviceProviderId, value = 目标serviceProviderId
     */
    private Map<String, String> findServiceProviderIdMap(Long targetShopId) {
        var list = mongoTemplate.find(Query.query(Criteria.where("shopId").is(targetShopId)), ServiceProvider.class);
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("目标商家的服务商数据集为空");
        }

        return list.stream().collect(Collectors.toMap(ServiceProvider::getMigrateSourceId, ServiceProvider::getId, (k1, k2) -> k1));
    }

    /**
     * @param targetShopId
     * @return key = 源itemId, value = 目标itemId
     */
    private Map<Long, Long> findItemIdMap(Long targetShopId) {
        var list = itemReadService.findAllByShopId(targetShopId).getResult();
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("目标商家的商品数据集为空");
        }

        return list.stream().collect(Collectors.toMap(this::extractSourceItemId, Item::getId, (k1, k2) -> k1));
    }

    private Long extractSourceItemId(Item item) {
        var extra = item.getExtra();
        if (CollectionUtils.isEmpty(extra)) {
            log.info("itemId={}对应的extra为空", item.getId());
            return -1L;
        }

        var id = extra.get(ItemExtraIndex.migrateSourceItemId.name());
        if (StringUtils.isBlank(id)) {
            log.info("itemId={}对应的 migrateSourceItemId 为空", item.getId());
            return -1L;
        }

        try {
            return Long.parseLong(id);
        } catch (Exception ex) {
            log.error("itemId={}对应的 migrateSourceItemId={}, 解析失败", item.getId(), id, ex);
            return -1L;
        }
    }
}
