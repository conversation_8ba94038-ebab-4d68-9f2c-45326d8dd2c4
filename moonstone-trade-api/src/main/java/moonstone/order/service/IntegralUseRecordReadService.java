package moonstone.order.service;

import com.danding.common.search.PageParam;
import com.danding.common.search.SearchResult;
import moonstone.order.model.IntegralUseRecord;
import moonstone.user.criteria.IntegralUseRecordCriteria;

import java.util.List;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/24 15:14
 */
public interface IntegralUseRecordReadService {
    SearchResult<IntegralUseRecord> pageRecordGradeList(PageParam pageParam, IntegralUseRecordCriteria criteria);
    List<IntegralUseRecord> getRecordGradeList(PageParam pageParam, IntegralUseRecordCriteria criteria);

    /**
     * test 导出
     * @param pageParam
     * @param criteria
     * @return
     */
    List<IntegralUseRecord> getRecordGradeListTest(PageParam pageParam, IntegralUseRecordCriteria criteria);

    /**
     * test 导出
     * @param criteria
     * @return
     */
    Integer getRecordTotal(IntegralUseRecordCriteria criteria);
}
