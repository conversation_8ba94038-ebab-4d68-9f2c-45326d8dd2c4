plugins {
    id 'java'
    id 'scala'
    id 'maven-publish'
}

sourceSets {
    main {
        scala {
            srcDirs = ['src/main/scala', 'src/main/java']
        }
        java {
            srcDirs = []
        }
    }
}
repositories {
    mavenLocal()
    maven {
        allowInsecureProtocol = true
        url = uri('http://mvn.yang800.cn/repository/maven-public/')
    }

    maven {
        allowInsecureProtocol = true
        url = uri('http://repo.maven.apache.org/maven2')
    }
}

group = 'trade.herald'
version = "${scala_compat_version}"
description = 'compatible-common'


dependencies {
    implementation project(":moonstone-trade-api")
    implementation project(":moonstone-item-api")
    implementation project(":moonstone-user-api")
    implementation project(":moonstone-common")

    implementation group: "javax.validation", name: "validation-api", version: "2.0.1.Final"
    implementation group: "javax.annotation", name: "javax.annotation-api", version: "$javax_annotaion_version"
    implementation "io.terminus.search:search-api:3.1.RELEASE"
    implementation "io.terminus.msg:terminus-msg-api:1.6.3.RELEASE"
    implementation "io.terminus.msg:terminus-msg-light:1.6.3.RELEASE"
    implementation "org.springframework:spring-webmvc:$spring_version"
    implementation "org.springframework:spring-web:$spring_version"
    implementation "org.springframework.boot:spring-boot-starter:$spring_boot_version"
    implementation "org.springframework.boot:spring-boot-starter-json:$spring_boot_version"
    implementation "org.springframework.boot:spring-boot-starter-jetty:$spring_boot_version"
    implementation "org.springframework.boot:spring-boot-starter-data-mongodb:$spring_boot_version"
    implementation "org.scala-lang:scala-library:$scala_version"
    implementation "org.projectlombok:lombok:$lombok_version"
    implementation "com.google.code.gson:gson:$gson_version"
    implementation "org.slf4j:slf4j-api:$simple_log_version"
    implementation "javax.annotation:javax.annotation-api:$javax_annotaion_version"
    implementation "com.google.guava:guava:$guava_version"
    annotationProcessor "org.projectlombok:lombok:$lombok_version"
    testAnnotationProcessor "org.projectlombok:lombok:$lombok_version"
    testImplementation group: 'junit', name: 'junit', version: '4.12'
}
sourceCompatibility = '17'

java {
    withSourcesJar()
}
