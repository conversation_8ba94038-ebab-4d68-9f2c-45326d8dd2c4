package moonstone.wxOpen.service;

import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.open.bean.message.WxOpenMaSubmitAuditMessage;
import me.chanjar.weixin.open.bean.result.WxOpenMaCategoryListResult;
import me.chanjar.weixin.open.bean.result.WxOpenMaPageListResult;
import me.chanjar.weixin.open.bean.result.WxOpenMaSubmitAuditResult;

/**
 * Created by <PERSON>ai<PERSON>hy on 2018/11/21.
 */
public interface WxOpenParanaMaService {
    String codeCommit(Long templateId, String userVersion, String userDesc, String extInfo, String appId) throws WxErrorException;

    WxOpenMaCategoryListResult getCategoryList(String appId) throws WxErrorException;

    WxOpenMaPageListResult getPageList(String appId) throws WxErrorException;

    WxOpenMaSubmitAuditResult submitAudit(WxOpenMaSubmitAuditMessage submitAuditMessage, String appId) throws WxErrorException;

    String getLatestAuditStatus(String appId) throws WxErrorException;

    String releaseAuthed(String appId) throws WxErrorException;
}
