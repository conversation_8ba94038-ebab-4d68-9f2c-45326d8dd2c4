package moonstone.order.service;

import com.danding.common.search.PageParam;
import com.danding.common.search.SearchResult;
import moonstone.common.model.Either;
import moonstone.order.model.IntegralDataStatus;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/21 10:07
 */
public interface IntegralDataStatusReadService {
    Either<Optional<IntegralDataStatus>> findByCode(String code, Integer status);

    Either<Optional<IntegralDataStatus>> findIntegralDataStatusByCode(String codeFlag);

    Either<Optional<IntegralDataStatus>> findByLimitOneMaxTime();

    Either<List<IntegralDataStatus>> findBystatus(int value);

    Either<Optional<IntegralDataStatus>> findByCodeAndShopId(String batch, long shopId);

    SearchResult<IntegralDataStatus> getIntegralDataStatusReadList(PageParam pageParam, Map<String, String> map);

    Either<List<IntegralDataStatus>> findByType(int type);

    Either<Optional<IntegralDataStatus>> findByCodeAndShopIdAndType(Long shopId, String code, int type);
}
