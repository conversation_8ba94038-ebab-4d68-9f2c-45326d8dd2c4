package moonstone.user.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.EntityBase;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/19 14:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class StoreIntegralGoods extends EntityBase {
    Long shopId;
    String code;//商家积分编码
    Long faceValue;//积分码面值
    Long thirdId;//类目id
    String thirdName;//类目名称
    String batch;//下载的code
    Integer flag;//是否需要冻结0 冻结 1-不冻结
    Long num;//生成码的数量

}
