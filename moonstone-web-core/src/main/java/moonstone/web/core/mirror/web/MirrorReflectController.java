package moonstone.web.core.mirror.web;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.model.Either;
import moonstone.common.utils.APIRespWrapper;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.web.core.component.pay.app.Json;
import moonstone.web.core.mirror.model.MirrorSourceEvent;
import moonstone.web.core.mirror.model.domain.MirrorDomain;
import moonstone.web.core.mirror.model.factory.DomainFactory;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.function.BiFunction;

/**
 * <AUTHOR>
 * 用于数据源同步api
 */
@RestController
@RequestMapping("/api/v1/mirror/{domain}/{source}")
@Slf4j
public class MirrorReflectController {

    Map<String, BiFunction<String, String, MirrorDomain>> domainFactoryMap;

    @Autowired
    List<DomainFactory<? extends MirrorDomain>> factories;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    MongoTemplate mongoTemplate;

    @PostConstruct
    public void loadFactory() {
        domainFactoryMap = new HashMap<>(factories.size());
        for (DomainFactory<? extends MirrorDomain> factory : factories) {
            domainFactoryMap.put(factory.getDomainName().replace("MirrorDomain", "").toLowerCase(), factory::build);
        }
    }

    @RequestMapping("/{identity}/{action}")
    public APIResp<?> handle(HttpServletRequest request
            , @PathVariable String domain, @PathVariable String source, @PathVariable String identity, @PathVariable String action) {
        MirrorSourceEvent mirrorSourceEvent = new MirrorSourceEvent(null, source, domain, identity, action, null, false, new Date());

        Lock domainLock = redissonClient.getLock(String.format("%s@%s{%s}", domain, source, identity));
        try {
            if (!domainLock.tryLock()) {
                return APIResp.error(Translate.of("系统繁忙, 请稍后再试"));
            }
            try {
                MirrorDomain mirrorDomain = domainFactoryMap.get(domain)
                        .apply(identity, source);
                ByteArrayOutputStream buff = new ByteArrayOutputStream();
                for (int i = request.getInputStream().read(); i > -1; i = request.getInputStream().read()) {
                    buff.write(i);
                }
                mirrorSourceEvent.setData(buff.toString());
                mirrorDomain.setSource(source);
                Object result = mirrorDomain.chain(action)
                        .apply(type -> Json.parseObject(new ByteArrayInputStream(buff.toByteArray()), type));
                if (result instanceof Either) {
                    if (((Either<?>) result).isSuccess()) {
                        mirrorSourceEvent.setHandled(true);
                    }
                    return APIRespWrapper.describe((Either<?>) result, "业务[{}]实体来源[Source => {}, Id => {}]操作[{}] 错误", domain, source, identity, action);
                }
                if (result instanceof APIResp) {
                    if (((APIResp<?>) result).ok()) {
                        mirrorSourceEvent.setHandled(true);
                    }
                    return (APIResp<?>) result;
                }
                mirrorSourceEvent.setHandled(true);
                return APIResp.ok(result);
            } catch (IOException ie) {
                log.error("{} connection error,can't handle domain[{}] source[{}] id[{}] action[{}]", LogUtil.getClassMethodName(), domain, source, identity, action, ie);
                return APIResp.error(Translate.of("链接错误"));
            }
        } catch (Exception e) {
            log.error("{} fail to handle domain[{}] source[{}] id[{}] action[{}]", LogUtil.getClassMethodName(), domain, source, identity, action, e);
            return APIResp.error(e.getMessage());
        } finally {
            domainLock.unlock();
            mongoTemplate.insert(mirrorSourceEvent);
            log.debug("{} insert the event [{}]", LogUtil.getClassMethodName(), Json.toJson(mirrorSourceEvent));
        }
    }
}
