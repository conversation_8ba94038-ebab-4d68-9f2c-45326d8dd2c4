package moonstone.web.front.profit.application;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.terminus.common.model.Paging;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.AccountStatementWithdrawStatusEnum;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.model.AuthAble;
import moonstone.order.api.BankAccountJudge;
import moonstone.order.dto.WithDrawProfitApplyCriteria;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.model.AccountStatement;
import moonstone.order.model.AccountStatementDetail;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawAccount;
import moonstone.order.service.*;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.ShopReadService;
import moonstone.shop.service.ShopWriteService;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.model.ServiceProvider;
import moonstone.web.core.util.MongoUtil;
import moonstone.web.front.profit.convert.WithdrawProfitApplyConvertor;
import moonstone.web.front.profit.domain.AbstractWithdrawAccountDomain;
import moonstone.web.front.profit.view.*;
import moonstone.web.front.shop.substore.app.SubStoreWithdrawAccountInitApp;
import moonstone.web.front.shop.substore.app.SubStoreWithdrawApp;
import moonstone.web.front.shop.substore.component.SubStoreUserIdentityComponent;
import moonstone.web.front.shop.substore.dto.SubStoreWithdrawApplyBO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WithdrawProfitApplyApp {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Resource
    private ShopReadService shopReadService;

    @Resource
    private ShopWriteService shopWriteService;

    @Resource
    private AccountStatementReadService accountStatementReadService;

    @Resource
    private AccountStatementDetailReadService accountStatementDetailReadService;

    @Resource
    private WithDrawProfitApplyReadService withDrawProfitApplyReadService;

    @Resource
    private SubStoreWithdrawAccountInitApp subStoreWithdrawAccountInitApp;

    @Resource
    private WithdrawAccountReadService withdrawAccountReadService;

    @Resource
    private WithdrawAccountWriteService withdrawAccountWriteService;

    @Resource
    private UserReadService<User> userReadService;

    @Resource
    private SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;

    @Resource
    private SubStoreReadService subStoreReadService;

    @Resource
    private BalanceDetailReadService balanceDetailReadService;

    @Resource
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private BankAccountJudge bankAccountJudge;

    @Resource
    private SubStoreUserIdentityComponent subStoreUserIdentityComponent;

    @Resource
    private SubStoreWithdrawApp subStoreWithdrawApp;

    private static final String SEPARATOR = "@@@";

    /**
     * 查询用户下的所有提现单
     *
     * @param userId
     * @param shopId
     * @param pageNum
     * @param pageSize
     * @return
     */
    public Paging<WithdrawProfitApplySimpleVO> findByUserId(Long userId, Long shopId, int pageNum, int pageSize) {
        WithDrawProfitApplyCriteria criteria = new WithDrawProfitApplyCriteria();
        criteria.setUserId(userId);
        criteria.setSourceId(shopId);
        criteria.setPageNo(pageNum);
        criteria.setPageSize(pageSize);

        var isSubStore = subStoreReadService.findUserIdAndShopId(userId, shopId).getResult() != null;
        if (!isSubStore) {
            criteria.setHasUserRole(true);
        }

        var page = withDrawProfitApplyReadService.paging(criteria).getResult();
        if (page == null) {
            return Paging.empty();
        }

        return new Paging<>(page.getTotal(), WithdrawProfitApplyConvertor.convert(page.getData()));
    }

    /**
     * 配置商家每月可以发起提现的日期区间
     *
     * @param shopId
     * @param request
     */
    public boolean setWithdrawPeriod(Long shopId, WithdrawPeriodConfigRequest request) {
        var shop = shopReadService.findById(shopId).getResult();
        if (shop == null) {
            throw new RuntimeException("商家不存在");
        }

        if (CollectionUtils.isEmpty(shop.getExtra())) {
            shop.setExtra(new HashMap<>());
        }
        request.getDetailList().forEach(detailConfig -> shop.getExtra().put(getShopExtraKey(detailConfig.getUserRole()),
                detailConfig.getDayOfMonthFrom() + SEPARATOR + detailConfig.getDayOfMonthTo()));

        return shopWriteService.update(shop).getResult();
    }

    /**
     * 查询商家配置的每月可以发起提现的日期区间
     *
     * @param shopId
     * @return
     */
    public List<WithdrawPeriodConfigVO> findWithdrawPeriod(Long shopId) {
        if (shopId == null) {
            return Collections.emptyList();
        }

        var shop = shopReadService.findById(shopId).getResult();
        if (shop == null) {
            throw new RuntimeException("商家不存在");
        }

        return WithdrawProfitApplyConvertor.convert(shop);
    }

    private String getShopExtraKey(Integer userRole) {
        SubStoreUserIdentityEnum userIdentityEnum = SubStoreUserIdentityEnum.parse(userRole);
        if (userIdentityEnum == null) {
            throw new RuntimeException("不支持的用户身份类型");
        }

        return switch (userIdentityEnum) {
            case STORE_GUIDER -> ShopExtra.subStoreWithdrawPeriod_1.getCode();
            case SERVICE_PROVIDER -> ShopExtra.subStoreWithdrawPeriod_3.getCode();
            default -> throw new RuntimeException("不支持的用户身份类型");
        };
    }

    /**
     * 查询指定商家的配置，判断当前是否可以发起提现
     *
     * @param shopId
     * @return
     */
    public Pair<String, Boolean> isWithdrawAvailable(Long shopId, Long userId) {
        var shop = shopReadService.findById(shopId).getResult();
        if (shop == null) {
            throw new RuntimeException("商家不存在");
        }
        if (CollectionUtils.isEmpty(shop.getExtra())) {
            shop.setExtra(new HashMap<>());
        }

        String config = shop.getExtra().get(getWithdrawPeriodKey(shopId, userId));
        if (StringUtils.isBlank(config)) {
            //没有配置的，默认不给提现
            return Pair.of("当前商家平台没有配置有效的提现申请开放日期", false);
        }

        var start = Integer.parseInt(config.split(SEPARATOR)[0]);
        var end = Integer.parseInt(config.split(SEPARATOR)[1]);
        LocalDate today = LocalDate.now();

        var allow = today.getDayOfMonth() >= start && today.getDayOfMonth() <= end;
        var message = allow ? StringUtils.EMPTY : String.format("当前时间不在提现时间内，请在每月%s日到%s日进行提现", start, end);

        return Pair.of(message, allow);
    }

    /**
     * 根据用户所属身份返回指定的shopExtra下标key
     *
     * @param shopId
     * @param userId
     * @return
     */
    private String getWithdrawPeriodKey(Long shopId, Long userId) {
        var guiders = subStoreTStoreGuiderReadService.findByStoreGuiderUserId(userId, shopId).getResult();
        if (!CollectionUtils.isEmpty(guiders)) {
            return ShopExtra.subStoreWithdrawPeriod_1.getCode();
        }

        var serviceProvider = serviceProviderCache.findByMongo(userId, shopId);
        if (serviceProvider != null) {
            return ShopExtra.subStoreWithdrawPeriod_3.getCode();
        }

        throw new RuntimeException("不支持的用户身份类型");
    }

    /**
     * 获取最近一次提现成功的提现单所使用的银行账户信息
     *
     * @param userId
     * @param shopId
     * @return
     */
    public WithdrawAccountSimpleVO findLastPaidSuccessAccount(Long userId, Long shopId) {
        //服务商，直接取其中的账户信息
        var serviceProvider = serviceProviderCache.findServiceProviderByUserIdAndShopId(userId, shopId);
        if (hasValidAccountInfo(serviceProvider)) {
            return new WithdrawAccountSimpleVO(serviceProvider.getBankAccountNo(), serviceProvider.getBankName(),
                    serviceProvider.getBankAccountName());
        }

        // 查询门店当前绑定的卡信息
        SubStore subStore = subStoreReadService.findUserIdAndShopId(userId, shopId).getResult();
        if(subStore != null){
            WithdrawAccountSimpleVO vo = new WithdrawAccountSimpleVO();
            vo.setBankCardNo(subStore.getBankNo());
            vo.setBankName(subStore.getBankName());
            vo.setBankCardOwnerName(subStore.getName());
            return vo;
        }

        var lastSuccess = findLastPaidSuccess(userId, shopId);
        if (lastSuccess == null) {
            //若没有成功提现记录，查询默认提现账户
            return WithdrawProfitApplyConvertor.convert(getDefaultWithdrawAccount(userId, shopId));
        }

        if (lastSuccess.getWithdrawAccountId() == null) {
            return WithdrawAccountSimpleVO.emptyAccount();
        }

        var account = withdrawAccountReadService.findById(lastSuccess.getWithdrawAccountId()).getResult();
        if (account.isEmpty()) {
            return WithdrawAccountSimpleVO.emptyAccount();
        }

        return WithdrawProfitApplyConvertor.convert(account.get());
    }

    /**
     * 提现账户相关的信息是否有效
     *
     * @param serviceProvider
     * @return
     */
    private boolean hasValidAccountInfo(ServiceProvider serviceProvider) {
        if (serviceProvider == null) {
            return false;
        }

        return StringUtils.isNotBlank(serviceProvider.getBankAccountNo()) &&
                StringUtils.isNotBlank(serviceProvider.getBankName()) &&
                StringUtils.isNotBlank(serviceProvider.getBankAccountName());
    }

    /**
     * 分页查询提现单
     *
     * @param request
     * @return
     */
    public Paging<WithdrawProfitApplyVO> findPage(WithdrawProfitApplyQueryRequest request) {
        var page = withDrawProfitApplyReadService.paging(buildCriteria(request)).getResult();
        if (page == null) {
            return Paging.empty();
        }
        if (CollectionUtils.isEmpty(page.getData())) {
            return new Paging<>(page.getTotal(), Collections.emptyList());
        }

        //提现单使用的账户信息
        var accountMap = findAccountMap(page.getData().stream()
                .map(WithDrawProfitApply::getWithdrawAccountId).collect(Collectors.toList()));

        //名称信息
        var nameMap = findNameMap(page.getData());

        return new Paging<>(page.getTotal(), WithdrawProfitApplyConvertor.convert(page.getData(), accountMap, nameMap));
    }

    /**
     * 按需保存账户信息
     *
     * @param account
     * @return 账户id
     */
    public Long saveAccount(WithdrawAccount account) {
        //查询是否存在相同的账户信息
        var response = withdrawAccountReadService.findAccount(account.getShopId(), account.getUserId(),
                account.getFrom(), account.getAccount());
        if (!response.isSuccess()) {
            throw new RuntimeException("账户信息查询失败: " + response.getError());
        }

        WithdrawAccount existedAccount = null;
        if (!CollectionUtils.isEmpty(response.getResult())) {
            existedAccount = response.getResult().stream()
                    .filter(o -> account.getRealName().equals(o.getRealName()))
                    .findAny()
                    .orElse(null);
        }
        if (existedAccount != null) {
            return existedAccount.getId();
        }

        //不存在相同的账户信息，则新建
        account.setName(account.getRealName());
        var createResult = withdrawAccountWriteService.create(account);
        if (!createResult.isSuccess() || account.getId() == null) {
            throw new RuntimeException("账户信息创建失败: " + createResult.getErrorMsg());
        }

        return account.getId();
    }

    /**
     * 所有可提现账单一并发起提现申请
     *
     * @param shopId
     * @param userId
     * @param accountId
     */
    public void withdrawAll(Long shopId, Long userId, Long accountId) {
        //发起提现的用户所属角色
        SubStoreUserIdentityEnum userIdentityEnum = determineUserIdentity(shopId, userId);
        if (userIdentityEnum == null) {
            throw new RuntimeException("当前用户角色身份不明");
        }
        if (SubStoreUserIdentityEnum.SUB_STORE.equals(userIdentityEnum)) {
            throw new RuntimeException("门店用户当前不能通过账单发起提现申请");
        }

        //查询当前所有可发起提现的账单
        var statementList = findCanWithdraw(shopId, userId);
        if (CollectionUtils.isEmpty(statementList)) {
            throw new RuntimeException("当前没有可以发起提现的账单");
        }

        //账单明细
        var detailList = accountStatementDetailReadService.findByAccountStatementIds(
                statementList.stream().map(AccountStatement::getId).collect(Collectors.toList())).getResult();
        if (CollectionUtils.isEmpty(detailList)) {
            throw new RuntimeException("账单明细数据为空");
        }

        //发起申请
        doApply(shopId, userId, userIdentityEnum, accountId, statementList, detailList);
    }

    /**
     * 发起申请
     *
     * @param shopId
     * @param userId
     * @param userIdentityEnum
     * @param accountId
     * @param statementList
     * @param detailList
     */
    private void doApply(Long shopId, Long userId, SubStoreUserIdentityEnum userIdentityEnum, Long accountId,
                         List<AccountStatement> statementList, List<AccountStatementDetail> detailList) {
        // 延用老逻辑
        var applyBO = new SubStoreWithdrawApplyBO();
        applyBO.setAccountStatementIdList(statementList.stream().map(AccountStatement::getId).collect(Collectors.toList()));
        applyBO.setProfitBelongUserId(findProfitBelongUserId(detailList.get(0).getBalanceDetailId()));
        applyBO.setProfitIdList(detailList.stream().map(AccountStatementDetail::getBalanceDetailId).distinct().collect(Collectors.toList()));
        applyBO.setShopId(shopId);

        applyBO.setUserId(userId);
        applyBO.setUserRole(userIdentityEnum);
        applyBO.setWithdrawAccountId(accountId);

        subStoreWithdrawApp.withdrawProfit(applyBO);
    }

    private Long findProfitBelongUserId(Long balanceDetailId) {
        var balanceDetail = balanceDetailReadService.findById(balanceDetailId).getResult();
        if (balanceDetail == null) {
            throw new RuntimeException(String.format("利润明细查询失败[balanceDetailId=%s]", balanceDetailId));
        }

        return balanceDetail.getUserId();
    }

    /**
     * 查询当前所有可发起提现的账单
     *
     * @param shopId
     * @param userId
     * @return
     */
    private List<AccountStatement> findCanWithdraw(Long shopId, Long userId) {
        var accountStatements = accountStatementReadService.findNoWithdraw(shopId, userId).getResult();
        if (CollectionUtils.isEmpty(accountStatements)) {
            return Collections.emptyList();
        }

        //账单的金额大于0，才可以发起提现
        return accountStatements.stream()
                .filter(o -> o.getTotalAmount() >= 0)
                .collect(Collectors.toList());
    }

    /**
     * 识别当前用户的身份(导购/门店/服务商)
     *
     * @param shopId
     * @param userId
     * @return
     */
    private SubStoreUserIdentityEnum determineUserIdentity(Long shopId, Long userId) {
        var guiders = subStoreTStoreGuiderReadService.findByStoreGuiderUserId(userId, shopId).getResult();
        if (!CollectionUtils.isEmpty(guiders)) {
            return SubStoreUserIdentityEnum.STORE_GUIDER;
        }

        var subStore = subStoreReadService.findUserIdAndShopId(userId, shopId).getResult();
        if (subStore != null) {
            return SubStoreUserIdentityEnum.SUB_STORE;
        }

        var serviceProvider = serviceProviderCache.findByMongo(userId, shopId);
        if (serviceProvider != null) {
            return SubStoreUserIdentityEnum.SERVICE_PROVIDER;
        }

        return null;
    }

    /**
     * 查询名称信息 map
     *
     * @param sourceList
     */
    private Map<String, WithdrawProfitApplyConvertor.BaseUserInfoDTO> findNameMap(List<WithDrawProfitApply> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyMap();
        }

        //导购
        var guiderMap = findGuiderName(sourceList.stream()
                .filter(o -> SubStoreUserIdentityEnum.STORE_GUIDER.getCode().equals(o.getUserRole()))
                .map(WithDrawProfitApply::getUserId)
                .collect(Collectors.toList()), sourceList.get(0).getSourceId());
        var result = new HashMap<>(guiderMap);

        //门店
        var subStoreMap = findSubStoreName(sourceList.stream()
                .filter(o -> SubStoreUserIdentityEnum.SUB_STORE.getCode().equals(o.getUserRole()))
                .map(WithDrawProfitApply::getUserId)
                .collect(Collectors.toList()), sourceList.get(0).getSourceId());
        result.putAll(subStoreMap);

        //服务商
        var serviceProviderMap = findServiceProviderName(sourceList.stream()
                .filter(o -> SubStoreUserIdentityEnum.SERVICE_PROVIDER.getCode().equals(o.getUserRole()))
                .map(WithDrawProfitApply::getUserId)
                .collect(Collectors.toList()), sourceList.get(0).getSourceId());
        result.putAll(serviceProviderMap);

        return result;
    }

    private Map<String, WithdrawProfitApplyConvertor.BaseUserInfoDTO> findGuiderName(List<Long> guiderUserIds, Long shopId) {
        if (CollectionUtils.isEmpty(guiderUserIds)) {
            return Collections.emptyMap();
        }

        var list = subStoreTStoreGuiderReadService.findByUserIds(guiderUserIds, shopId).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(
                o -> WithdrawProfitApplyConvertor.getNameMapKey(o.getStoreGuiderId(), SubStoreUserIdentityEnum.STORE_GUIDER),
                o -> new WithdrawProfitApplyConvertor.BaseUserInfoDTO(o.getStoreGuiderNickname(), o.getStoreGuiderMobile()),
                (k1, k2) -> k1));
    }

    private Map<String, WithdrawProfitApplyConvertor.BaseUserInfoDTO> findSubStoreName(List<Long> subStoreUserIds, Long shopId) {
        if (CollectionUtils.isEmpty(subStoreUserIds)) {
            return Collections.emptyMap();
        }

        var list = subStoreReadService.findByUserIds(subStoreUserIds, shopId).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(
                o -> WithdrawProfitApplyConvertor.getNameMapKey(o.getUserId(), SubStoreUserIdentityEnum.SUB_STORE),
                o -> new WithdrawProfitApplyConvertor.BaseUserInfoDTO(o.getName(), o.getMobile()),
                (k1, k2) -> k1));
    }

    private Map<String, WithdrawProfitApplyConvertor.BaseUserInfoDTO> findServiceProviderName(List<Long> serviceProviderUserIds, Long shopId) {
        if (CollectionUtils.isEmpty(serviceProviderUserIds)) {
            return Collections.emptyMap();
        }

        var list = serviceProviderCache.findByMongo(serviceProviderUserIds, shopId);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(
                o -> WithdrawProfitApplyConvertor.getNameMapKey(o.getUserId(), SubStoreUserIdentityEnum.SERVICE_PROVIDER),
                o -> new WithdrawProfitApplyConvertor.BaseUserInfoDTO(o.getName(), o.getMobile()),
                (k1, k2) -> k1));
    }

    /**
     * 查询账户信息map
     *
     * @param idList
     * @return key=账户id, value=账户对象
     */
    private Map<Long, WithdrawAccount> findAccountMap(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyMap();
        }

        var accountList = withdrawAccountReadService.findByIds(idList).getResult();
        if (CollectionUtils.isEmpty(accountList)) {
            return Collections.emptyMap();
        }

        return accountList.stream().collect(Collectors.toMap(WithdrawAccount::getId, o -> o, (k1, k2) -> k1));
    }

    public WithDrawProfitApplyCriteria buildCriteria(WithdrawProfitApplyQueryRequest request) {
        WithDrawProfitApplyCriteria criteria = new WithDrawProfitApplyCriteria();

        criteria.setPageNo(request.getCurrentPage());
        criteria.setPageSize(request.getPageSize());
        criteria.setSourceId(request.getShopId());
        criteria.setHasUserRole(true);

        if (request.getUserRole() != null) {
            criteria.setUserRole(SubStoreUserIdentityEnum.cleanCode(request.getUserRole()));
        }
        if (request.getStatus() != null) {
            criteria.setWithdrawApplyStatus(AccountStatementWithdrawStatusEnum.cleanCode(request.getStatus()));
        }
        if (StringUtils.isNotBlank(request.getUserMobile())) {
            appendUserIdsByUserMobile(criteria, request.getUserMobile());
        }
        if (StringUtils.isNotBlank(request.getUserName())) {
            appendUserIdsByUserName(criteria, request.getUserName());
        }

        return criteria;
    }

    /**
     * 使用userMobile精确查询，获取userId列表
     *
     * @param criteria
     * @param userMobile
     */
    private void appendUserIdsByUserMobile(WithDrawProfitApplyCriteria criteria, String userMobile) {
        if (StringUtils.isBlank(userMobile) ||
                (!CollectionUtils.isEmpty(criteria.getUserIds()) && criteria.getUserIds().contains(-1L))) {
            return;
        }

        List<Long> all = new ArrayList<>();

        //当前
        var user = userReadService.findByMobile(userMobile).getResult();
        if (user != null) {
            all.add(user.getId());
        }

        //快照
        var bySnapshot = subStoreUserIdentityComponent.findUserIdsByMobile(criteria.getSourceId(), userMobile,
                criteria.getUserRole(), OrderRoleSnapshotOrderTypeEnum.WITHDRAW_APPLY);
        if (!CollectionUtils.isEmpty(bySnapshot)) {
            all.addAll(bySnapshot);
        }

        setUserIds(criteria, all);
    }

    /**
     * 使用userName进行模糊查询，获取userId列表
     *
     * @param criteria
     * @param userName
     */
    private void appendUserIdsByUserName(WithDrawProfitApplyCriteria criteria, String userName) {
        if (StringUtils.isBlank(userName)) {
            return;
        }

        List<Long> all = new ArrayList<>();

        //当前
        var list = subStoreUserIdentityComponent.findUserIdsByName(criteria.getSourceId(), userName, criteria.getUserRole());
        if (!CollectionUtils.isEmpty(list)) {
            all.addAll(list);
        }

        //快照
        var bySnapshot = subStoreUserIdentityComponent.findUserIdsByName(criteria.getSourceId(), userName,
                criteria.getUserRole(), OrderRoleSnapshotOrderTypeEnum.WITHDRAW_APPLY);
        if (!CollectionUtils.isEmpty(bySnapshot)) {
            all.addAll(bySnapshot);
        }

        setUserIds(criteria, all);
    }

    private void setUserIds(WithDrawProfitApplyCriteria criteria, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            criteria.setUserIds(Lists.newArrayList(-1L));
        } else {
            if (CollectionUtils.isEmpty(criteria.getUserIds())) {
                criteria.setUserIds(userIds.stream().distinct().collect(Collectors.toList()));
            } else {
                criteria.getUserIds().retainAll(userIds);
                if (CollectionUtils.isEmpty(criteria.getUserIds())) {
                    criteria.setUserIds(Lists.newArrayList(-1L));
                }
            }
        }
    }

    /**
     * 获取默认的提现账户
     *
     * @param userId
     * @param shopId
     * @return
     */
    private WithdrawAccount getDefaultWithdrawAccount(Long userId, Long shopId) {
        //按需初始化一下门店的账户信息
        subStoreWithdrawAccountInitApp.initSubStoreWithdrawAccount(userId, shopId);

        //查询
        var accountInfo = AbstractWithdrawAccountDomain.from(withdrawAccountReadService, bankAccountJudge, userId, shopId)
                .withdrawAccountQuerySlice().withdrawAccountQuery().take();

        if (accountInfo != null) {
            return accountInfo.getDefaultAccount();
        }

        return null;
    }

    /**
     * 获取最近一次提现成功的提现单
     *
     * @param userId
     * @param shopId
     * @return
     */
    private WithDrawProfitApply findLastPaidSuccess(Long userId, Long shopId) {
        WithDrawProfitApplyCriteria criteria = new WithDrawProfitApplyCriteria();
        criteria.setUserId(userId);
        criteria.setSourceId(shopId);
        criteria.setPageNo(1);
        criteria.setPageSize(10);

        //完成支付
        criteria.setBitmasks(Arrays.asList(AuthAble.AuthStatus.AUTHED.getValue()
                , WithDrawProfitApply.WithdrawExtraStatus.PAID.getMaskBit()));
        criteria.setNotBitMasks(Arrays.asList(AuthAble.AuthStatus.REJECT.getValue()
                , WithDrawProfitApply.WithdrawExtraStatus.WAITING.getMaskBit()));

        var page = withDrawProfitApplyReadService.paging(criteria).getResult();
        if (page == null || CollectionUtils.isEmpty(page.getData())) {
            return null;
        }

        return page.getData().get(0);
    }

    /**
     * 把服务商的提现账户信息初始化一下（使用上次提现成功的账户）
     *
     * @param shopId
     * @return
     */
    public boolean initServiceProviderWithdrawAccount(Long shopId) {
        var shop = shopReadService.findById(shopId).getResult();
        if (shop == null) {
            throw new RuntimeException("商家不存在");
        }

        List<ServiceProvider> serviceProviders = mongoTemplate.find(
                Query.query(Criteria.where("shopId").is(shopId)), ServiceProvider.class);
        if (CollectionUtils.isEmpty(serviceProviders)) {
            return true;
        }

        //逐个更新
        serviceProviders.forEach(serviceProvider -> {
            var account = findLastPaidSuccessAccount(serviceProvider.getUserId(), shopId);
            if (account != null && StringUtils.isNotBlank(account.getBankCardNo()) &&
                    StringUtils.isNotBlank(account.getBankCardOwnerName()) &&
                    StringUtils.isNotBlank(account.getBankName())) {
                //构造更新参数
                var map = ImmutableMap.of("bankName", account.getBankName(),
                        "bankAccountNo", account.getBankCardNo(),
                        "bankAccountName", account.getBankCardOwnerName());
                Update update = MongoUtil.generateUpdate(map, new HashSet<>(Arrays.asList("createdAt", "relationId")), false);

                //更新
                mongoTemplate.updateMulti(Query.query(Criteria.where("userId").is(serviceProvider.getUserId()))
                                .addCriteria(Criteria.where("shopId").is(shopId))
                        , update, ServiceProvider.class);

                //刷下缓存
                serviceProviderCache.invalidate(shopId, serviceProvider.getUserId());
            }
        });

        return true;
    }
}
