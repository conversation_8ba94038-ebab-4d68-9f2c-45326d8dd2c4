package moonstone.weShop.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.user.area.model.dto.AreaInfoView;
import moonstone.user.dto.AccountCertificationForSellerVO;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.weShop.enums.WeShopStatus;
import moonstone.weShop.model.WeShop;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

public interface WeShopApplyView {
    Long getId();

    String getName();

    String getUserName();

    String getMobile();

    String getAvatar();

    String getProvince();

    Long getProvinceId();

    String getCity();

    Long getCityId();

    String getCounty();

    Long getCountyId();

    String getAddress();

    String getAuthStatus();

    Integer getStatus();

    String getReason();

    List<String> getOperation();

    Date getCreatedAt();

    Date getAuthAt();

    AccountCertificationForSellerVO getAccountCertification();

    static WeShopApplyView build(WeShop weShop, User user, UserProfile userProfile, AccountCertificationForSellerVO accountCertificationVO, AreaInfoView areaInfo) {
        String status;
        if (Objects.isNull(areaInfo)) {
            areaInfo = AreaInfoView.none();
        }
        user = Optional.ofNullable(user).orElseGet(User::new);
        List<String> action = new LinkedList<>();
        status = WeShopStatus.fromInt(weShop.getStatus()).getDesc();
        switch (WeShopStatus.fromInt(weShop.getStatus())) {
            case NORMAL:
                action.add("AUTH");
                action.add("REJECT");
                action.add("STOP_COOP");
                break;
            case NOT_ACTIVE:
                action.add("STOP_COOP");
                break;
            default:
                status = "未知";
        }
        Date authAt = Optional.ofNullable(weShop.getExtra())
                .map(ex -> ex.get("authAt"))
                .map(LocalDateTime::parse)
                .map(time -> time.atZone(ZoneId.systemDefault()).toInstant())
                .map(Date::from)
                .orElse(null);
        return new WeShopApplyViewImpl(weShop.getId(), weShop.getName()
                , Optional.ofNullable(userProfile).map(UserProfile::getRealName).orElseGet(user::getName)
                , user.getMobile(), Optional.ofNullable(weShop.getLogoUrl()).orElseGet(() -> Optional.ofNullable(userProfile).map(UserProfile::getAvatar_).orElse(null)), status
                , areaInfo.getProvince()
                , areaInfo.getCity()
                , areaInfo.getCounty()
                , areaInfo.getProvinceId()
                , areaInfo.getCityId()
                , areaInfo.getCountyId()
                , areaInfo.getAddress()
                , weShop.getReason(), weShop.getStatus(), action
                , accountCertificationVO
                , weShop.getCreatedAt()
                , authAt
        );
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class WeShopApplyViewImpl implements WeShopApplyView {
        Long id;
        String name;
        String userName;
        String mobile;
        String avatar;
        String authStatus;
        String province;
        String city;
        String county;
        Long provinceId;
        Long cityId;
        Long countyId;
        String address;
        String reason;
        Integer status;
        List<String> operation;
        AccountCertificationForSellerVO accountCertification;
        Date createdAt;
        Date authAt;
    }
}
