package moonstone.user.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.user.entity.UserBindAllinPay;
import moonstone.user.impl.dao.UserBindAllinPayDao;
import moonstone.user.service.UserBindAllinPayService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class UserBindAllinPayServiceImpl implements UserBindAllinPayService {

    @Resource
    private UserBindAllinPayDao userBindAllinPayDao;

    @Override
    public boolean save(UserBindAllinPay bindAllinPay) {
        // 创建通联会员时需要设置启用禁用状态
        bindAllinPay.setIsEnabled(getIsEnabledByRecordCreate(bindAllinPay.getShopId(), bindAllinPay.getUserId(), bindAllinPay.getAllinpayRoleType()));
        return userBindAllinPayDao.save(bindAllinPay);
    }

    @Override
    public Boolean updateById(UserBindAllinPay updateObject) {
        return userBindAllinPayDao.updateById(updateObject);
    }

//    @Override
//    public UserBindAllinPay getByShopIdAndUserId(Long shopId, Long userId) {
//        return userBindAllinPayDao.getByShopIdAndUserId(shopId, userId);
//    }

    @Override
    public UserBindAllinPay getPersonByShopIdAndUserId(Long shopId, Long userId) {
        return userBindAllinPayDao.getPersonByShopIdAndUserId(shopId, userId);
    }
    @Override
    public UserBindAllinPay getCompanyByShopIdAndUserId(Long shopId, Long userId) {
        return userBindAllinPayDao.getCompanyByShopIdAndUserId(shopId, userId);
    }

    @Override
    public UserBindAllinPay getActiveByShopIdAndUserId(Long shopId, Long userId) {
        return userBindAllinPayDao.getActiveByShopIdAndUserId(shopId, userId);
    }

    @Override
    public UserBindAllinPay getByShopIdAndUserIdAndAllinUserType(Long shopId, Long userId, Long allinpayRoleType) {
        return userBindAllinPayDao.getByShopIdAndUserIdAndAllinpayRoleType(shopId, userId, allinpayRoleType);
    }

    @Override
    public UserBindAllinPay getByBizUserId(String bizUserId) {
        return userBindAllinPayDao.getByBizUserId(bizUserId);
    }

    @Override
    public List<UserBindAllinPay> getByOpenIdAndAppId(String openId, String appId, Long allinPayRoleType) {
        return userBindAllinPayDao.getByOpenIdAndAppId(openId, appId, allinPayRoleType);
    }

    public boolean getIsEnabledByRecordCreate(Long shopId, Long userId, Long allinpayRoleType) {
        if(allinpayRoleType == 3){
            // 创建个人会员时 存在启用的企业会员时 个人会员设置为不启用状态
            UserBindAllinPay entity = getByShopIdAndUserIdAndAllinUserType(shopId, userId, 2l);
            if(entity == null){
                // 不存在企业会员 个人会员默认为启用
                log.info("不存在企业会员 个人会员默认为启用 {} {} {}", shopId, userId, allinpayRoleType);
                return true;
            }else{
                if(entity.getIsEnabled()){
                    // 存在启用的企业会员 个人会员默认为关闭
                    log.info("存在启用的企业会员 个人会员默认为关闭 {} {} {}", shopId, userId, allinpayRoleType);
                    return false;
                }else{
                    // 存在关闭的企业会员 个人会员默认为启用
                    log.info("存在关闭的企业会员 个人会员默认为启用 {} {} {}", shopId, userId, allinpayRoleType);
                    return true;
                }
            }
        }else if (allinpayRoleType == 2){
            // 创建企业会员时 默认为不启用
            log.info("创建企业会员时 默认为不启用 {} {} {}", shopId, userId, allinpayRoleType);
            return false;
        }
        log.info("未知的通联会员类型 {} {} {}", shopId, userId, allinpayRoleType);
        throw new RuntimeException("未知的通联会员类型 " + allinpayRoleType);
    }


}
