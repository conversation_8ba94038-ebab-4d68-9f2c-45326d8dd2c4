package moonstone.web.core.shop.dto;

import lombok.Data;
import moonstone.common.utils.DateUtil;
import org.springframework.beans.BeanUtils;

import java.util.*;

@Data
public class CompleteDataActivityView {
    String id;
    String name;
    Long shopId;
    List<String> infoNeed;
    Long rewardIntegral;
    Integer status;
    Date startAt;
    Date endAt;
    Boolean longTerm;
    Date createdAt;
    Date updatedAt;
    String className;

    public CompleteDataActivity completeDataActivity() {
        CompleteDataActivity activity = new CompleteDataActivity();
        BeanUtils.copyProperties(this, activity);
        if (infoNeed != null)
            activity.setInfoNeed(new HashSet<>(infoNeed));
        return activity;
    }

    public static CompleteDataActivityView from(CompleteDataActivity activity) {
        CompleteDataActivityView view = new CompleteDataActivityView();
        BeanUtils.copyProperties(activity, view);
        view.setInfoNeed(new ArrayList<>(activity.getInfoNeed()));
        Date now = new Date();
        if (Objects.equals(1, activity.getLongTerm())) {
            if (activity.getLongTerm() || (!Optional.ofNullable(activity.getStartAt()).orElse(now).after(now) && !Optional.ofNullable(activity.getEndAt()).map(DateUtil::withTimeAtEndOfDay).orElse(now).before(now))) {
                view.setStatus(4);
            }
        }
        return view;
    }
}
