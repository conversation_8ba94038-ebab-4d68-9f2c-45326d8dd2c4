package moonstone.web.core.mirror.web;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.utils.APIRespWrapper;
import moonstone.web.core.mirror.app.SourceShopQuerySlice;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/api/v1/mirror/config")
@AllArgsConstructor
public class SourceConfigController {
    SourceShopQuerySlice sourceShopQuerySlice;

    @GetMapping
    public APIResp<String> findSourceForProjectId(Long projectId) {
        return APIRespWrapper.describe(sourceShopQuerySlice.queryShopSourceByProjectId(projectId), "获取项目配置渠道[%s]信息失败", projectId);
    }

    @GetMapping("/query")
    public APIResp<Long> queryByShopIdAndSource(Long shopId, String source) {
        return APIRespWrapper.describe(sourceShopQuerySlice.queryProjectIdByShopIdAndSource(shopId, source), "获取店铺[%s]配置渠道[%s]的项目信息失败", shopId, source);
    }

    @GetMapping("/source")
    public APIResp<Long> queryBySource(String source) {
        return APIRespWrapper.describe(sourceShopQuerySlice.queryShopIdByOutShopCodeAndSource(null, source), "获取渠道[%s]配置的店铺信息失败", source);
    }

    @PostMapping("/set")
    public APIResp<Boolean> setSource(Long shopId, String source, Long projectId) {
        return APIRespWrapper.describe(sourceShopQuerySlice.setSource(shopId, source, projectId), "设置渠道信息失败");
    }
}
