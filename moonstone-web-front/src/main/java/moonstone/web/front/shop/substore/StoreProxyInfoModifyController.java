package moonstone.web.front.shop.substore;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.enums.OpBusinessType;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.utils.*;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.StoreProxyInfo;
import moonstone.user.service.StoreProxyInfoReadService;
import moonstone.user.service.StoreProxyInfoWriteService;
import moonstone.user.service.StoreProxyReadService;
import moonstone.web.front.shop.substore.component.StoreProxyInfoUpdateComponent;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/26 14:00
 */
@RestController
@RequestMapping("/api/storeProxy")
@Slf4j
@AllArgsConstructor
public class StoreProxyInfoModifyController {

    private final StoreProxyReadService storeProxyReadService;

    private final StoreProxyInfoWriteService storeProxyInfoWriteService;

    private final StoreProxyInfoReadService storeProxyInfoReadService;

    private final ShopCacheHolder shopCacheHolder;

    private final StoreProxyInfoUpdateComponent storeProxyInfoUpdateComponent;

    /**
     * 请求修改信息
     * 同时只允许存在一个信息修改申请, 并且需要通过审核才行
     *
     * @param newInfo 信息请求
     * @return 结果
     */
    @RequestMapping(value = "/infos", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public R applyInfoUpdate(@RequestBody StoreProxyInfo newInfo) {

        CommonUser applier = UserUtil.getCurrentUser();
        if (Objects.isNull(applier)) {
            return R.error(-1001, "未登录");
        }

        if (ObjectUtils.isEmpty(newInfo) || StringUtils.isEmpty(newInfo.getProxyId())) {
            return R.error(-1, "参数为空");
        }

        Either<Optional<StoreProxy>> storeProxyOptRes = storeProxyReadService.findById(newInfo.getProxyId());
        if (!storeProxyOptRes.isSuccess() || !storeProxyOptRes.take().isPresent()) {
            log.error("{} infoUpdateApplier is error proxyId:{}", LogUtil.getClassMethodName(), newInfo.getProxyId());
            return R.error(-1, "未查询到相关信息");
        }
        if (!Objects.equals(3, storeProxyOptRes.take().get().getStatus())) {
            log.error("{} infoUpdateApplier is error proxyId:{}", LogUtil.getClassMethodName(), newInfo.getProxyId());
            return R.error(-1, "审核未通过，不能变更信息");
        }
        if (!Objects.equals(storeProxyOptRes.take().get().getUserId(), applier.getId())
                && !Objects.equals(storeProxyOptRes.take().get().getShopId(), applier.getShopId())) {
            return R.error(-1, Translate.of("该门店不属于你"));
        }

        // 如果开启审核减少, 则直接修改信息
        if (ShopFunctionSlice.build(shopCacheHolder.findShopById(storeProxyOptRes.take().get().getShopId())).isLessAuth()) {
            storeProxyInfoUpdateComponent.updateStoreProxyFromInfo(newInfo);
            return R.ok();
        }

        Either<Optional<StoreProxyInfo>> oldInfoUpdateApplierOptRes = storeProxyInfoReadService.findByProxyId(newInfo.getProxyId());
        if (!oldInfoUpdateApplierOptRes.isSuccess()) {
            log.error("{} infoUpdateApplier is error proxyId:{}", LogUtil.getClassMethodName(), newInfo.getProxyId());
            return R.error(-1, "未查询到相关信息");
        }

        if (Objects.equals(storeProxyOptRes.take().get().getLevel(), 1)) {
            newInfo.setStatus(7);
        } else {
            newInfo.setStatus(11);
        }
        // 不存在则插入
        if (!oldInfoUpdateApplierOptRes.take().isPresent()) {
            Either<Long> updateApplierIdRes = storeProxyInfoWriteService.create(newInfo);
            if (!updateApplierIdRes.isSuccess() || ObjectUtils.isEmpty(updateApplierIdRes.take())) {
                log.error("{} infoUpdateApplier create is error proxyId:{}", LogUtil.getClassMethodName(), newInfo.getProxyId());
                return R.error(-1, "插入数据失败");
            }
            return R.ok().add("data", updateApplierIdRes.take());
        }

        if (!Objects.equals(oldInfoUpdateApplierOptRes.take().get().getProxyId(), newInfo.getProxyId())) {
            log.error("{} infoUpdateApplier is error proxyId:{}", LogUtil.getClassMethodName(), newInfo.getProxyId());
            return R.error(-1, "信息有误");
        }
        newInfo.setId(oldInfoUpdateApplierOptRes.take().get().getId());
        Either<Boolean> booleanResult = storeProxyInfoWriteService.update(newInfo);
        if (!booleanResult.isSuccess() || !booleanResult.take()) {
            log.error("{} infoUpdateApplier create is error proxyId:{}", LogUtil.getClassMethodName(), newInfo.getProxyId());
            return R.error(-1, "修改数据失败");
        }

        return R.ok();
    }

    /**
     * 贝拉米同意申请
     */
    @GetMapping("/infos/auth")
    @MyLog(title = "分销管理->品牌代言人审核", value = "商家同意信息变更申请", opBusinessType = OpBusinessType.UPDATE)
    public R authProxyInfo(Long id, String auditingRemark) {
        CommonUser user = UserUtil.getCurrentUser();
        Either<Optional<StoreProxyInfo>> infoModifyOptRes = storeProxyInfoReadService.findByProxyId(id);
        if (!infoModifyOptRes.isSuccess() || !infoModifyOptRes.take().isPresent()) {
            log.error("auth is fail id:{}", id);
            return R.error(-1, "未查询到相关信息");
        }
        if (!Objects.equals(infoModifyOptRes.take().get().getStatus(), 7)) {
            log.error("wait jxs auth is fail id:{}", id);
            return R.error(-1, "请等待经销商审核");
        }

        infoModifyOptRes.take().get().setStatus(3);

        Map<String, String> map = infoModifyOptRes.take().get().getExtra();
        map.put("auditingRemark", auditingRemark);
        infoModifyOptRes.take().get().setExtraStr(JSON.toJSONString(map));
        Either<Boolean> booleanResult = storeProxyInfoWriteService.update(infoModifyOptRes.take().get());
        if (!booleanResult.isSuccess() || !booleanResult.take()) {
            log.error("auth is fail id:{}", id);
            return R.error(-1, "审核失败");
        }
        Either<Optional<StoreProxy>> oldStoreProxyOptRes = storeProxyReadService.findById(id);
        if (!oldStoreProxyOptRes.isSuccess() || !oldStoreProxyOptRes.take().isPresent()) {
            log.error("auths is fail id:{}", id);
            return R.error(-1, "审核失败");
        }
        try {
            storeProxyInfoUpdateComponent.updateStoreProxyFromInfo(infoModifyOptRes.take().get()).take();
        } catch (Exception exception) {
            log.error("{} fail to update StoreProxyInfo[{}]", LogUtil.getClassMethodName(), JSON.toJSONString(infoModifyOptRes.take()), exception);
            return R.error(-1, exception.getMessage());
        }
        return R.ok();
    }

    /**
     * 经销商同意申请
     */
    @GetMapping("/infos/jxAuth")
    public R jxAuthProxyInfo(Long id, String jxRemark) {
        CommonUser user = UserUtil.getCurrentUser();
        Either<Optional<StoreProxyInfo>> storeProxyInfo = storeProxyInfoReadService.findByProxyId(id);
        if (!storeProxyInfo.isSuccess() || !storeProxyInfo.take().isPresent()) {
            log.error("auth is fail id:{}", id);
            return R.error(-1, "未查询到相关信息");
        }

        if (!Objects.equals(storeProxyInfo.take().get().getStatus(), 11)) {
            log.error("wait jxs auth is fail id:{}", id);
            return R.error(-1, "审核状态不对");
        }

        storeProxyInfo.take().get().setStatus(7);

        Map<String, String> map = storeProxyInfo.take().get().getExtra();
        map.put("jxRemark", jxRemark);
        storeProxyInfo.take().get().setExtraStr(JSON.toJSONString(map));
        Either<Boolean> booleanResult = storeProxyInfoWriteService.update(storeProxyInfo.take().get());
        if (!booleanResult.isSuccess() || !booleanResult.take()) {
            log.error("auth is fail id:{}", id);
            return R.error(-1, "审核失败");
        }
        return R.ok();
    }

    /**
     * 贝拉米拒绝申请
     */
    @GetMapping("/infos/reject")
    @MyLog(title = "分销管理->品牌代言人审核", value = "商家拒绝信息变更申请", opBusinessType = OpBusinessType.UPDATE)
    public R rejectProxyInfo(Long id, String auditingRemark) {
        CommonUser user = UserUtil.getCurrentUser();
        Either<Optional<StoreProxyInfo>> storeProxyInfo = storeProxyInfoReadService.findByProxyId(id);
        if (!storeProxyInfo.isSuccess() || !storeProxyInfo.take().isPresent()) {
            log.error("auth is fail id:{}", id);
            return R.error(-1, "未查询到相关信息");
        }
        if (!Objects.equals(storeProxyInfo.take().get().getStatus(), 7)) {
            log.error("wait jxs auth is fail id:{}", id);
            return R.error(-1, "请等待经销商审核");
        }

        storeProxyInfo.take().get().setStatus(5);

        Map<String, String> map = storeProxyInfo.take().get().getExtra();
        map.put("auditingRemark", auditingRemark);
        storeProxyInfo.take().get().setExtraStr(JSON.toJSONString(map));
        Either<Boolean> booleanResult = storeProxyInfoWriteService.update(storeProxyInfo.take().get());
        if (!booleanResult.isSuccess() || !booleanResult.take()) {
            log.error("auth is fail id:{}", id);
            return R.error(-1, "审核失败");
        }
        return R.ok();
    }

    /**
     * 经销商拒绝申请
     */
    @GetMapping("/infos/jxReject")
    public R jxRejectProxyInfo(Long id, String jxRemark) {
        CommonUser user = UserUtil.getCurrentUser();
        Either<Optional<StoreProxyInfo>> storeProxyInfo = storeProxyInfoReadService.findByProxyId(id);
        if (!storeProxyInfo.isSuccess() || !storeProxyInfo.take().isPresent()) {
            log.error("auth is fail id:{}", id);
            return R.error(-1, "未查询到相关信息");
        }

        if (!Objects.equals(storeProxyInfo.take().get().getStatus(), 11)) {
            log.error("wait jxs auth is fail id:{}", id);
            return R.error(-1, "审核状态不对");
        }

        storeProxyInfo.take().get().setStatus(9);

        Map<String, String> map = storeProxyInfo.take().get().getExtra();
        map.put("jxRemark", jxRemark);
        storeProxyInfo.take().get().setExtraStr(JSON.toJSONString(map));
        Either<Boolean> booleanResult = storeProxyInfoWriteService.update(storeProxyInfo.take().get());
        if (!booleanResult.isSuccess() || !booleanResult.take()) {
            log.error("auth is fail id:{}", id);
            return R.error(-1, "审核失败");
        }
        return R.ok();
    }

    /**
     *
     */

    @GetMapping("/infos/page")
    public R paging(long id) {
        Either<Optional<StoreProxyInfo>> storeProxyInfo = storeProxyInfoReadService.findByProxyId(id);
        if (!storeProxyInfo.isSuccess() || !storeProxyInfo.take().isPresent()) {
            log.error("StoreProxyInfo is empty id:{}", id);
            return R.error(-1, "查询失败");
        }
        Either<Optional<StoreProxy>> storeProxy = storeProxyReadService.findById(id);
        if (!storeProxy.isSuccess() || !storeProxy.take().isPresent()) {
            log.error("storeProxy is empty id:{}", id);
            return R.error(-1, "查询失败");
        }
        ProxyInfos proxyInfosOld = new ProxyInfos();
        BeanUtils.copyProperties(storeProxy.take().get(), proxyInfosOld);
        ProxyInfos proxyInfosNew = new ProxyInfos();
        BeanUtils.copyProperties(storeProxyInfo.take().get(), proxyInfosNew);

        return R.ok().add("proxyId", id).add("oldData", proxyInfosOld).add("newData", proxyInfosNew);
    }

    /**
     * 获取信息变更审核的数量
     */
    @GetMapping("/getCountSubProxyInfo")
    public R getCountSubProxyInfo(Long shopId, @RequestParam(defaultValue = "7") Integer status) {

        Either<List<StoreProxy>> listResults = storeProxyReadService.findByShopId(shopId);
        List<Long> proxyId = new ArrayList<>();
        if (listResults.isSuccess() && !listResults.take().isEmpty()) {
            for (StoreProxy s : listResults.take()) {
                proxyId.add(s.getId());
            }
            Either<List<StoreProxyInfo>> listResult = storeProxyInfoReadService.getCountSubProxyInfo(proxyId, status);
            if (listResult.isSuccess() && !listResult.take().isEmpty()) {
                return R.ok().add("size", listResult.take().size());
            }
        }
        return R.ok().add("size", 0);
    }

    @Data
    class ProxyInfos {
        String city;

        String cityId;

        String provinceId;

        String province;

        String countyId;

        String county;

        String proxyShopName;

        String tel;

        String address;

        String proxyMobile;

        String bossName;

        String bossMobile;

        Long eastLongtitude;

        Long northLatitude;
    }

}
