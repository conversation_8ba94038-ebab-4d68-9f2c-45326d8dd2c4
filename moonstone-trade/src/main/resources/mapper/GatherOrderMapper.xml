<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="GatherOrder">
    <resultMap id="GatherOrderMap" type="GatherOrder">
        <id column="id" property="id"/>
        <result column="declared_id" property="declaredId"/>
        <result column="shop_id" property="shopId"/>
        <result column="out_shop_id" property="outShopId"/>
        <result column="out_from" property="outFrom"/>
        <result column="buyer_id" property="buyerId"/>
        <result column="promotion_id" property="promotionId"/>
        <result column="origin_fee" property="originFee"/>
        <result column="fee" property="fee"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="auth_status" property="authStatus"/>
        <result column="auth_at" property="authAt"/>
        <result column="extra_json" property="extraJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">parana_gather_order</sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        declared_id,shop_id,out_shop_id,out_from,buyer_id,promotion_id,origin_fee,fee,type,status,auth_status,auth_at,extra_json,tags_json,created_at,updated_at
    </sql>

    <sql id="vals">
        #{declareId},#{shopId},#{outShopId},#{outFrom},#{buyerId},#{promotionId},#{originFee},#{fee},#{type},#{status},#{authStatus},#{authAt},#{extraJson},#{tagsJson},#{createdAt},#{updatedAt}
    </sql>

    <sql id="criteria">
        <if test="id != null">id = #{id}</if>
        <if test="status != null">and status = #{status}</if>
        <if test="buyerId!= null">and buyer_id = #{buyerId}</if>
        <if test="authStatus!= null">and auth_status = #{authStatus}</if>
    </sql>

    <insert id="create" parameterType="GatherOrder" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO<include refid="tb"/>(<include refid="cols_exclude_id"/>) VALUES(<include refid="vals"/>)
    </insert>

    <delete id="delete" parameterType="long">
        delete from
        <include refid="tb"/>
        where id = #{id}
    </delete>

    <update id="authFrom" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            auth_status = #{targetAuthStatus}
        </set>
        <where>
            auth_status = #{currentAuthStatus}
            and id = #{gatherOrderId}
        </where>
    </update>

    <update id="update" parameterType="GatherOrder">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="declaredId != null">declared_id = #{declaredId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="outShopId != null">out_shop_id = #{outShopId},</if>
            <if test="outFrom != null">out_from = #{outFrom},</if>
            <if test="buyerId != null">buyer_id = #{buyerId},</if>
            <if test="promotionId != null">promotion_id = #{promotionId},</if>
            <if test="originFee != null">origin_fee = #{originFee},</if>
            <if test="fee != null">fee = #{fee},</if>
            <if test="type != null">type = #{type},</if>
            <if test="status != null">status = #{status},</if>
            <if test="authStatus != null">auth_status = #{authStatus},</if>
            <if test="authAt != null">auth_at = #{authAt},</if>
            <if test="extraJson != null">extra_json = #{extraJson},</if>
            <if test="tagsJson != null">tags_json = #{tagsJson},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

    <select id="findById" parameterType="long" resultMap="GatherOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where id = #{id}
    </select>

    <select id="findByIds" parameterType="list" resultMap="GatherOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        where id in
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="count" parameterType="map" resultType="long">
        select count(1) from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="GatherOrderMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="findByStatusWithGroupByBuyerId" resultType="list">
        select min(id) from
        <include refid="tb"/>
        <where>
            status = #{status}
        </where>
        group by buyer_id
    </select>
</mapper>
