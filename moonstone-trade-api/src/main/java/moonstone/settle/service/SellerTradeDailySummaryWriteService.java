package moonstone.settle.service;

import io.terminus.common.model.Response;
import moonstone.settle.model.SellerTradeDailySummary;

import java.util.Date;
import java.util.List;

/**
 * Code generated by terminus code gen
 * Desc: 写服务
 * Date: 2016-07-24
 */

public interface SellerTradeDailySummaryWriteService {

    /**
     * 创建SellerTradeDailySummary
     * @param sellerTradeDailySummary
     * @return 主键id
     */
    Response<Long> createSellerTradeDailySummary(SellerTradeDailySummary sellerTradeDailySummary);

    /**
     * 更新SellerTradeDailySummary
     * @param sellerTradeDailySummary
     * @return 是否成功
     */
    Response<Boolean> updateSellerTradeDailySummary(SellerTradeDailySummary sellerTradeDailySummary);

    /**
     * 根据主键id删除SellerTradeDailySummary
     * @param sellerTradeDailySummaryId
     * @return 是否成功
     */
    Response<Boolean> deleteSellerTradeDailySummaryById(Long sellerTradeDailySummaryId);


    /**
     * 批量创建 商家日汇总
     * @param forwardSummarys 正向汇总
     * @param reverseSummarys 逆向汇总
     * @param mergeSummarys 合并汇总
     * @return 是否创建成功
     */
    Response<Boolean> batchCreate(List<SellerTradeDailySummary> forwardSummarys,List<SellerTradeDailySummary> reverseSummarys,List<SellerTradeDailySummary> mergeSummarys);


    /**
     * 生成指定汇总时间的商家日汇总记录, 如果已经存在则更新
     * @param sumAt 指定的汇总时间, 格式为yyyy-MM-dd
     * @return 是否成功
     */
    Response<Boolean> generateSellerTradeDailySummary(Date sumAt);

}