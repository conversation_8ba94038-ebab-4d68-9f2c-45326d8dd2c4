/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.bricks;

import moonstone.common.model.CommonUser;
import moonstone.order.dto.RichSku;
import moonstone.promotion.annotation.PromotionBrick;
import moonstone.promotion.api.BrickType;
import moonstone.promotion.api.SkuScope;

import java.util.Map;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-31
 */
@PromotionBrick(key = "all", type = BrickType.SKU_SCOPE)
public class AllSkuScope implements SkuScope {

    /**
     * 判断当前sku是否适用营销
     *
     * @param richSku        sku
     * @param skuScopeParams 涉及到的参数
     * @return 是否适用
     */
    @Override
    public boolean apply(RichSku richSku, CommonUser buyer, Map<String, String> skuScopeParams) {
        if (skuScopeParams.getOrDefault(REQUIRE_INTEGRAL, "false").equals("true")) {
            return richSku.getItem().getType() > 2;
        } else return richSku.getItem().getType() <= 2;
    }
}
