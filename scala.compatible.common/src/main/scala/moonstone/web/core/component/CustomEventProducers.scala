package moonstone.web.core.component

import moonstone.common.event.ForceNewVersionByShopIdNotifyEvent
import moonstone.common.utils.EventSender
import moonstone.event.{MethodSwitchNotify, ThirdPartyLogLevelChangeEvent}
import moonstone.web.core.PayTokenReloadEvent
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.{ContextRefreshedEvent, EventListener}
import org.springframework.web.bind.annotation._

@RestController
@RequestMapping(Array("/api/custom/event/producer"))
@Autowired
case class CustomEventProducers(eventSender: EventSender
                                , backendPasswordHolder: AppStartUUID) {
  @EventListener(classes = Array(classOf[ContextRefreshedEvent]))
  def afterBoot(): Unit = {
    forceNewVersionByShopId(23, force = true) // 测试先切换为Y800新平台
    forceNewVersionByShopId(37, force = true) //
    forceNewVersionByShopId(38, force = true)
  }

  /**
   * 设置第三方的日志打印
   *
   * @param invoke 是否将调用外部Api的数据打印出来
   */
  @PostMapping(Array("/thirdParty/logAPIInvoke"))
  def logAPIInvoke(invoke: Boolean): Unit = {
    EventSender.publish(ThirdPartyLogLevelChangeEvent(invoke))
  }

  /**
   * 重新注册读取支付信息Token
   */
  @PostMapping(Array("/pay/reloadPayToken")) def reloadPayToken: Boolean = {
    EventSender.publish(new PayTokenReloadEvent)
    true
  }

  /**
   * 发送某个方法版本切换的信息
   *
   * @param className  类名
   * @param methodName 方法名
   * @param version    版本
   */
  @PostMapping(Array("/switchMethod/{class}/{method}/{version}"))
  def switchMethodVersion(@PathVariable("class") className: String, @PathVariable("method") methodName: String, @PathVariable version: String): Unit = {
    EventSender.publish(MethodSwitchNotify(className, methodName, version))
  }

  /**
   * 强制设定某个店铺使用新版本(方法)
   *
   * @param shopId 店铺信息
   * @param force  是否强制
   */
  @PostMapping(Array("/switch/forceNewVersion/{shopId}"))
  def forceNewVersionByShopId(@PathVariable("shopId") shopId: Long, force: Boolean): Unit = {
    EventSender.publish(ForceNewVersionByShopIdNotifyEvent(shopId, force))
  }

  /**
   * 修改应用设定
   *
   * @param name  名字
   * @param value 数据
   */
  @PostMapping(Array("/switch/Function/{name}"))
  def switchFunction(@PathVariable("name") name: String, value: String, @RequestParam(required = false) password: String): Unit = {
    if (!this.backendPasswordHolder.password.equals(password)) return
    EventSender.publish(MethodSwitchNotify(className = "FunctionSwitch", name, value))
  }
}
