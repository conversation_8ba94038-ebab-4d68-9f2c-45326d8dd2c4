package moonstone.order.service;

import moonstone.common.model.Either;
import moonstone.order.model.IntegralData;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/19 17:14
 */
public interface IntegralDataWriteService {
    Either<Boolean> createIntegralData(IntegralData data);

    Either<Boolean> updateByShopIdAndCode(String genCode);

    Either<Boolean> updateByShopIdAndCodeAndPed(String substring, String substring1, long shopId);

    Either<Boolean> updateByShopIdAndCodeAndPedLock(String substring, String substring1, long shopId);

    Either<Boolean> updateByShopIdAndCodeAndPedBcak(String substring, String substring1, long shopId);
}
