/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.search.item;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-03
 */
@ConfigurationProperties(prefix = "item.search")
@Getter
@Setter
public class SearchItemProperties {

    /**
     * 商品的索引名称, 可能是alias
     */
    private String indexName = "items";

    /**
     * 商品的索引类型
     */
    private String indexType = "item";

    /**
     * 对应商品类型的索引文件路径, 用以初始化索引的mapping, 默认为 ${indexType}_mapping.json
     */
    private String mappingPath;

    /**
     * 全量dump索引时, 最多涉及多少天之前有更新的商品
     */
    private Integer fullDumpRange = 3;

    /**
     * 每次批量处理多少个商品
     */
    private Integer batchSize = 100;
}
