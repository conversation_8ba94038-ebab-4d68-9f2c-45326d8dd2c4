package moonstone.user.enums;

import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 导出任务的状态
 */
public enum DataExportTaskStatusEnum {
    PROCESSING(1, "处理中"),
    FINISHED(2, "处理完成"),
    FAILED(3, "处理失败");

    private Integer code;
    private String description;

    DataExportTaskStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String parseToDescription(Integer code) {
        if (code == null) {
            return StringUtils.EMPTY;
        }

        for (DataExportTaskStatusEnum current : DataExportTaskStatusEnum.values()) {
            if (current.getCode().equals(code)) {
                return current.getDescription();
            }
        }

        return StringUtils.EMPTY;
    }

    public static List<Map<String, String>> toComboBox() {
        return Arrays.stream(DataExportTaskStatusEnum.values()).map(e -> {
            Map<String, String> map = new HashedMap<>();
            map.put("name", e.getDescription());
            map.put("value", e.getCode().toString());

            return map;
        }).collect(Collectors.toList());
    }
}
