/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.common.Digestors;
import moonstone.item.impl.dao.ItemAttributeDao;
import moonstone.item.impl.dao.ItemDao;
import moonstone.item.impl.dao.ItemDetailDao;
import moonstone.item.impl.manager.ItemManager;
import moonstone.item.model.Item;
import moonstone.item.model.ItemAttribute;
import moonstone.item.model.ItemDetail;
import moonstone.item.service.AdminItemWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 运营对商品进行管理操作
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-16
 */
@Service
@Slf4j
@RpcProvider
public class AdminItemWriteServiceImpl implements AdminItemWriteService {

    private final ItemDao itemDao;

    private final ItemDetailDao itemDetailDao;

    private final ItemAttributeDao itemAttributeDao;

    private final ItemManager itemManager;

    @Autowired
    public AdminItemWriteServiceImpl(ItemDao itemDao,
                                     ItemDetailDao itemDetailDao,
                                     ItemAttributeDao itemAttributeDao,
                                     ItemManager itemManager) {
        this.itemDao = itemDao;
        this.itemDetailDao = itemDetailDao;
        this.itemAttributeDao = itemAttributeDao;
        this.itemManager = itemManager;
    }

    /**
     * 批量更新一个店铺的所有状态, 这是给运营使用的
     *
     * @param shopId 店铺id
     * @param status 商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     */
    @Override
    public Response<Boolean> batchUpdateStatusByShopId(Long shopId, Integer status) {

        try {
            itemDao.batchUpdateStatusByShopId(shopId, status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update item(shopId={}) to target status({}),cause:{}",
                    shopId, status, Throwables.getStackTraceAsString(e));
            return Response.fail("item.update.fail");
        }
    }

    /**
     * 批量更新商品状态, 这是给运营使用的
     *
     * @param ids    商品id列表
     * @param status 商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     */
    @Override
    public Response<Boolean> batchUpdateStatus(List<Long> ids, Integer status) {
        try {
            itemManager.batchUpdateStatusByItemIds(ids,status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update items(ids={}) to target status({}),cause:{}",
                    ids, status, Throwables.getStackTraceAsString(e));
            return Response.fail("item.update.fail");
        }
    }

    /**
     * 更新单个商品状态, 这是给运营使用的
     *
     * @param id     商品id
     * @param status 商品状态 1: 上架, -1:下架, -2:冻结, -3:删除
     */
    @Override
    public Response<Boolean> updateStatus(Long id, Integer status) {
        try {
            // 已删除商品不允许更新状态
            Item originItem = itemDao.findById(id);
            if (Objects.equals(originItem.getStatus(), -3)) {
                log.error("try to update item status by itemId:{}, status:{}, but item already deleted",
                          id, status);
                return Response.fail("item.update.fail");
            }

            itemManager.updateStatusByItemId(id,status);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update item(id={}) to target status({}),cause:{}",
                    id, status, Throwables.getStackTraceAsString(e));
            return Response.fail("item.update.fail");
        }
    }

    /**
     * 运营标记商品
     *
     * @param itemId 商品id
     * @param tags   商品标签
     * @return 是否标记成功
     */
    @Override
    public Response<Boolean> tags(Long itemId, Map<String, String> tags) {

        try {
            //生成商品快照摘要
            ItemDetail itemDetail = itemDetailDao.findByItemId(itemId);
            Item item = itemDao.findById(itemId);
            item.setTags(tags);
            ItemAttribute itemAttribute = itemAttributeDao.findByItemId(itemId);
            String itemInfoMd5 = Digestors.itemDigest(item, itemDetail, itemAttribute);
            //更新tag
            itemDao.updateTags(itemId, tags, itemInfoMd5);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update item(id={}) tags to {}, cause:{}",
                    itemId, tags, Throwables.getStackTraceAsString(e));
            return Response.fail("item.update.fail");
        }
    }
}
