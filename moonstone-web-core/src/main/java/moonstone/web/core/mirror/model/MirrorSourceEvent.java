package moonstone.web.core.mirror.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

import java.util.Date;
import java.util.Optional;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class MirrorSourceEvent {
    @Id
    String id;
    String source;
    String domain;
    String identity;
    String action;
    String data;
    Boolean handled;
    Date createdAt;

    public String getDate() {
        return Optional.ofNullable(createdAt).map(Date::toString).orElse("未知");
    }
}
