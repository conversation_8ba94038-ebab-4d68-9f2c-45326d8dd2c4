/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.api;

import moonstone.common.model.CommonUser;

/**
 * 营销行为
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-06-01
 */
public interface Behavior {

    /**
     * 是否影响支付金额
     *
     * @return 是否影响支付金额, 如果不影响支付金额, 则在支付回调时执行
     */
    boolean affectPay();

    /**
     * 检查用户是否有这项优惠, 比如优惠券需要检查用户是否有优惠, 而对于直降这个营销则不需要检查, 默认所有用户都有直降优惠
     *
     * @param buyer  买家
     * @param  promotionId 营销活动id
     * @return  是否拥有
     */
    boolean userOwn(CommonUser buyer, Long promotionId);
}
