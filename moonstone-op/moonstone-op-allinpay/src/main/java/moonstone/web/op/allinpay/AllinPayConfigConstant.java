package moonstone.web.op.allinpay;

public interface AllinPayConfigConstant {
	String URL = "parana.allinpay.url";
	String APPID = "parana.allinpay.appId";
	String SECRET_KEY = "parana.allinpay.secretKey";
	String PRIVATE_KEY_PATH = "parana.allinpay.privateKeyPath";
	String PWD = "parana.allinpay.pwd";
	String PUBLIC_KEY = "parana.allinpay.publicKey";
	String WX_APP_ID = "parana.allinpay.wxappId";
	String WX_OPEN_ID = "parana.allinpay.wxopenId";

	String INDUSTRY_CODE = "0000";
	String INDUSTRY_NAME = "xxxx";

	/**
	 * 账户集编号
	 */
	String ACCOUNT_SET_NO = "parana.allinpay.accountSetNo";

	// TODO 换其他
	String SYS_ID = "1354697378326904XXX";

	String CODE_OK = "10000";
	String SUBCODE_OK = "OK";
	String SUBCODE_ERROR = "ERROR";

}
