package moonstone.web.front.util;

import lombok.Data;
import moonstone.common.model.Either;
import moonstone.common.utils.ExcelField;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.AccessibleObject;
import java.lang.reflect.Field;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 简单导出工具
 *
 * <AUTHOR>
 */
public interface SimpleExportUtil {
    Logger log = LoggerFactory.getLogger(SimpleExportUtil.class);

    @Data
    class FieldWithAnnotation {
        String column;
        String name;
        Field field;
        boolean exportedAble;

        public FieldWithAnnotation(Field field) {
            try {
                this.field = field;
                ExcelField excelField = field.getAnnotation(ExcelField.class);
                if (excelField != null) {
                    column = excelField.column();
                    name = excelField.name();
                    exportedAble = excelField.isExport();
                } else {
                    exportedAble = false;
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                exportedAble = false;
            }
        }
    }

    /**
     * 将数据打包成workBook输出
     *
     * @param dataList  数据原表
     * @param aimClass  类型
     * @param sheetName 表名
     * @return 封装好的sheet
     */
    static <T> Either<Workbook> tryMake(List<T> dataList, Class<T> aimClass, String sheetName) {
        List<FieldWithAnnotation> fields = Stream.of(aimClass.getDeclaredFields())
                .map(FieldWithAnnotation::new)
                .filter(FieldWithAnnotation::isExportedAble)
                .sorted(Comparator.comparing(FieldWithAnnotation::getColumn))
                .collect(Collectors.toList());
        if (!fields.isEmpty()) {
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet(sheetName);
            // init first row column
            Row column = sheet.createRow(0);
            List<Boolean> accessAbleList = fields.stream().map(FieldWithAnnotation::getField).map(AccessibleObject::isAccessible).collect(Collectors.toList());
            for (int i = 0; i < fields.size(); i++) {
                column.createCell(i).setCellValue(fields.get(i).getName());
                fields.get(i).getField().setAccessible(true);
            }
            // fill with data
            try {
                for (int i = 0; i < dataList.size(); i++) {
                    Row row = sheet.createRow(i + 1);
                    for (int j = 0; j < fields.size(); j++) {
                        Object value = fields.get(j).getField().get(dataList.get(i));
                        value = value == null ? "" : value;
                        row.createCell(j).setCellValue(value.toString());
                    }
                }
            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("{} fail to explain the value ex:{} stack:{}", LogUtil.getClassMethodName(), ex, ex.getStackTrace()[0]);
            }
            // rewind the privilege
            for (int i = 0; i < accessAbleList.size(); i++) {
                fields.get(i).getField().setAccessible(accessAbleList.get(i));
            }
            return Either.ok(workbook);
        }
        return Either.error(new Translate("无法解析类型").toString());
    }
}
