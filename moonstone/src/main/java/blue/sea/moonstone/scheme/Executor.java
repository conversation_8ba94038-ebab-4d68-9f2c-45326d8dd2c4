package blue.sea.moonstone.scheme;

import java.io.OutputStream;
import java.util.*;
import java.util.function.BiFunction;

public class Executor {
    Executor parent;
    int last = 0;
    Map<String, BiFunction<Executor, AST, AST>> env = new HashMap<>();
    Map<String, OutputStream> io = new HashMap<>();


    void localDefine(String name, BiFunction<Executor, AST, AST> v) {
        env.put(name, v);
    }

    void hotSet(String name, BiFunction<Executor, AST, AST> v) {
        if (env.containsKey(name)) {
            env.put(name, v)
            ;
            return;
        }
        if (parent != null) {
            parent.hotSet(name, v);
            return;
        }
        throw new RuntimeException("trying set unknown variable -> " + name);
    }

    /**
     * define string at scheme machine
     *
     * @param name   name
     * @param string str
     */
    public void defineString(String name, String string) {
        AST str = new AST();
        str.type = AST.AtomType.String;
        str.name = string;
        setGlobal(name, (c, a) -> str);
    }

    /**
     * define number at scheme machine
     *
     * @param name   name
     * @param number number
     */
    public void defineNumber(String name, Number number) {
        AST num = new AST();
        num.type = AST.AtomType.Number;
        num.name = number.toString();
        setGlobal(name, (c, a) -> num);
    }

    /**
     * set global action for scheme machine
     *
     * @param name name
     * @param v    lambda
     */
    public void setGlobal(String name, BiFunction<Executor, AST, AST> v) {
        if (parent == null) {
            env.put(name, v);
            return;
        }
        parent.setGlobal(name, v);
    }

    OutputStream getIO(String name) {
        if (!io.containsKey(name) && parent != null) {
            return parent.getIO(name);
        }
        return io.get(name);
    }
    void setGlobalIO(String name, OutputStream outputStream){
        if (parent!=null){
            parent.setGlobalIO(name,outputStream);
            return;
        }
        setLocalIO(name, outputStream);
    }

    void setLocalIO(String name, OutputStream outputStream) {
        io.put(name, outputStream);
    }

    BiFunction<Executor, AST, AST> pointEnv(AST ast) {
        if (env.get(ast.name) != null) {
            return env.get(ast.name);
        }
        if (parent != null) {
            return parent.pointEnv(ast);
        }
        throw new IllegalStateException("Unbound lambda for apply " + ast.name);
    }

    List<BiFunction<Executor, AST, AST>> hook = new ArrayList<>();

    public static Executor withOutput(OutputStream outputStream) {
        Executor env = new Executor();
        env.setGlobalIO("current", outputStream);
        return env;
    }

    Executor(Executor parent) {
        // init simple env stack
        // add id, just return self
        hook.add((context, ast) -> {
            if (ast.isBracket()) {
                return null;
            }
            if (ast.type == AST.AtomType.String || ast.type == AST.AtomType.Number) {
                return ast;
            }
            return null;
        });
        this.parent = parent;
        env.put("core.procedure", new Procedure());
        env.put("core.begin", new Begin());
        env.put("core.eval", new Eval());
    }
    Executor() {
        // init simple env stack
        // add id, just return self
        hook.add((context, ast) -> {
            if (ast.isBracket()) {
                return null;
            }
            if (ast.type == AST.AtomType.String || ast.type == AST.AtomType.Number) {
                return ast;
            }
            return null;
        });
        env.put("+", new Add());
        env.put("add", new Add());
        env.put("-", new Sub());
        env.put("sub", new Sub());
        env.put("display", new Display());
        env.put("define", new Define());
        env.put("newline", new Newline());
        env.put("quote", new Quote());
        env.put("lambda", new Lambda());
        env.put("core.procedure", new Procedure());
        env.put("core.begin", new Begin());
        env.put("core.eval", new Eval());
        env.put("eval", new Eval());
        env.put("begin", new Begin());
        env.put("let", new Let());
        env.put("let*", new LetX());
        env.put("mul", new Mul());
        env.put("*", new Mul());
        env.put("if", new If());
        env.put("eq?", new Eq());
        env.put("=", new Eq());
        env.put(">", new Bigger());
        env.put("not", new Not());

        eval("(define < (lambda (a b) (not (> a b))))");
    }

    public AST execute(AST ast) {
        return execute(ast, this);
    }

    public AST eval(String code) {
        AST res = null;
        for (AST ast : SimpleParser.parseAll(code)) {
            res = eval(ast);
        }
        return res;
    }
    /**
     * eval take the value
     *
     * @param ast ast
     * @return the value
     */
    public AST eval(AST ast) {
        ast = execute(ast);
        if (ast.type == AST.AtomType.Atom ||
                ast.type == AST.AtomType.String ||
                ast.type == AST.AtomType.Number || ast.isBracket()) {
            return ast;
        }
        // 存储方式有问题, 导致eval的时候获取值时无法识别值的类型, 函数和值
        // 通过将数据获取方式交给内部来决定是在执行还是在获取值
        return pointEnv(ast).apply(this, ast);
    }

    /**
     * execute the ast
     * @param one ast
     * @param current current env
     * @return value
     */
    AST execute(AST one, Executor current) {
        if (!one.isBracket()) {
            // execute hook before real execute
            for (BiFunction<Executor, AST, AST> e : hook) {
                AST res = e.apply(current, one);
                if (res != null) {
                    return res;
                }
            }
            return one;
        }
        if (one.lisp.size() == 0) {
            return one;
        }
        Executor inside = new Executor(current);
        // AST{AST{let} AST{a} AST{1}}
        AST headValue = execute((AST) one.lisp.get(0), inside);
        if (headValue.isBracket()) {
            throw new IllegalStateException("unexpected execute code " + one + " no form understand");
        }
        // use point to take value.
        // so variable will be able to gain the real value.
        // the function should return them self.
        AST value = current.pointEnv(headValue).apply(current, headValue);
        // real execute
        AST ast = current.pointEnv(value).apply(inside, one);

        current.env.put("*" + last, (skip, none) -> ast);
        last++;
        return ast;
    }

    // fake

    /**
     * broken design for stack mock
     *
     * @param startAst wrong
     * @param startEnv wrong
     * @return wrong
     */
    private AST executeAtMock(AST startAst, Executor startEnv) {
        Stack<AST> lastOne = new Stack<>();
        Stack<Executor> lastEnv = new Stack<>();
        Stack<String> pcStack = new Stack<>();
        AST ast;
        Executor current;
        AST res = null;
        lastOne.push(startAst);
        lastEnv.push(startEnv);
        pcStack.push("start");
        while (!pcStack.isEmpty()) {
            ast = lastOne.pop();
            current = lastEnv.pop();
            switch (pcStack.pop()) {
                case "start": {
                    // execute
                    if (!ast.isBracket()) {
                        // execute hook before real execute
                        boolean found = false;
                        for (BiFunction<Executor, AST, AST> e : hook) {
                            res = e.apply(current, ast);
                            if (res != null) {
                                found = true;
                                break;
                            }
                        }
                        if (found) {
                            break;
                        }
                        res = current.pointEnv(ast).apply(current, ast);
                        break;
                    }
                    if (ast.lisp.size() == 0) {
                        res = ast;
                        break;
                    }
                    Executor newContext = new Executor(current);
                    // protect now
                    lastOne.push(ast);
                    lastEnv.push(current);
                    pcStack.push("callback");
                    // execute
                    lastOne.push((AST) ast.lisp.get(0));
                    lastEnv.push(newContext);
                    pcStack.push("start");
                    break;
                }
                case "callback": {
                    if (res.isBracket()) {
                        throw new IllegalStateException("unexpected execute code " + ast + " no form understand");
                    }
                    res = current.pointEnv(res).apply(current, ast);
                    break;
                }
            }
        }
        return res;
    }
}
