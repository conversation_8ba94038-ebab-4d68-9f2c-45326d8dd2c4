package moonstone.web.core.exports.common;

import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.PagingCriteria;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.ParanaDefaultThreadFactory;
import moonstone.common.utils.Translate;
import moonstone.web.core.util.ImgBufferUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.beans.PropertyDescriptor;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * DATE: 16/11/24 上午9:22 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
@Component
public class DefaultExporter implements Exporter {

    static final ExecutorService DOWNLOAD = Executors.newScheduledThreadPool(Runtime.getRuntime().availableProcessors() * 4,
            new ParanaDefaultThreadFactory("defaultExporter"));
    private final ExportTables tables;
    private final ColumnFormatterRegistry formatterRegistry;

    private final Map<String, ImageBuffer> imageCache = new ConcurrentHashMap<>(64);

    @Autowired
    public DefaultExporter(ExportTables tables, ColumnFormatterRegistry formatterRegistry) {
        this.tables = tables;
        this.formatterRegistry = formatterRegistry;
    }

    /**
     * 设置导出时的响应实体, 注意避免名称乱码问题。
     *
     * @param request  Http请求
     * @param response Http回应
     * @param fileName 文件名称
     * @throws UnsupportedEncodingException 不支持的编码
     */
    public static void setHttpServletResponse(HttpServletRequest request, HttpServletResponse response, String fileName) throws UnsupportedEncodingException {
        fileName = fileName + ".xlsx";

        final String userAgent = request.getHeader("USER-AGENT");
        log.debug("user-agent={}", userAgent);

        String finalFileName;
        if (StringUtils.contains(userAgent.toUpperCase(), "MSIE")) {//IE浏览器
            finalFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        } else if (StringUtils.contains(userAgent.toUpperCase(), "Mozilla".toUpperCase())) {//safari,火狐浏览器
            finalFileName = new String(fileName.getBytes(), "ISO8859-1");
        } else {
            finalFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);//其他浏览器, 如chrome等
        }
        //这里设置一下让浏览器弹出下载提示框，而不是直接在浏览器中打开
        response.setHeader("Content-Disposition", "attachment; filename=\"" + finalFileName + "\"");
        //response.setContentType("application/vnd.ms-excel;charset=utf-8;");
        response.setContentType("application/octet-stream;charset=utf-8");
    }

    /**
     * 根据传入的分页查询方法和参数去查询出所有的数据。
     * todo 待优化, 应该与export结合起来, 读出一个分页, 处理一个分页。
     *
     * @param criteria 分页查询条件
     * @param func     分页查询方法
     * @param <T>      元素类型约束
     * @param <C>      分页查询条件的类型约束
     * @return 返回列表
     */
    public static <T, C extends PagingCriteria> List<T> findPages(C criteria, Function<C, Response<Paging<T>>> func) {

        List<T> data = Lists.newArrayList();

        criteria.setPageNo(1);
        criteria.setHasNext(true);
        criteria.setPageSize(200);
        while (criteria.hasNext()) {
            Response<Paging<T>> resp = func.apply(criteria);
            List<T> list = resp.getResult().getData();
            if (list == null || list.size() == 0) {
                break; //or criteria.setHasNext(false);
            } else {
                data.addAll(list);
                criteria.nextPage();
            }
        }
        return data;
    }

    @Override
    public void export(List<?> dataList, Class<?> clazz, HttpServletRequest request, HttpServletResponse response) {

        ExportTable table = tables.tables().get(clazz.getSimpleName());
        if (table == null) {
            log.error("download the excel of {} failed, cause={}",
                    clazz.getSimpleName(), "table.config.missing");
            throw new JsonResponseException("table.config.missing");
        }

        try {
            setHttpServletResponse(request, response, table.getDisplay());
            Workbook book = this.export(dataList, clazz);
            book.write(response.getOutputStream());
            book.close();
        } catch (Exception e) {
            log.error("download the excel of {} failed, cause={}",
                    clazz.getSimpleName(), Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    public <T, C extends PagingCriteria> void export(Class<?> clazz, C criteria, Function<C, Response<Paging<T>>> func, HttpServletRequest request, HttpServletResponse response) {
        List<T> list = findPages(criteria, func);
        export(list, clazz, request, response);
    }

    @Override
    public <T> Workbook exportWithDecorate(List<T> dataList, Class<T> clazz, BiConsumer<Row, T> decorator) {
        final int CACHE_LINE = 1000;
        Workbook wb;
        if (dataList.size() > CACHE_LINE) {
            wb = new SXSSFWorkbook(CACHE_LINE);
        } else {
            wb = new XSSFWorkbook();
        }
        try {
            String tableName = clazz.getSimpleName();
            ExportTable table = tables.tables().get(tableName);
            if (table == null) {
                throw Translate.exceptionOf("导出模板未配置, 请联系客服");
            }
            Sheet sheet = wb.createSheet(table.getDisplay());

            //标题
            Row titleRow = sheet.createRow(0);
            int i = 0;
            for (ExportColumn column : table.getColumns()) {
                Cell cell = titleRow.createCell(i);
                cell.setCellValue(column.getDisplay());
                sheet.setColumnWidth(i, column.getWidth());
                i++;
            }
            if (decorator != null) {
                decorator.accept(titleRow, null);
            }
            //内容
            Map<String, Method> methodMap = new HashMap<>(32);
            for (PropertyDescriptor propertyDescriptor : BeanUtils.getPropertyDescriptors(clazz)) {
                methodMap.put(propertyDescriptor.getName(), propertyDescriptor.getReadMethod());
                propertyDescriptor.getReadMethod().setAccessible(true);
            }
            for (i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                int x = 0;
                for (ExportColumn column : table.getColumns()) {
                    if (column.getFormat() == null) {
                        column.setFormat("default");
                    }
                    ColumnFormatter formatter = formatterRegistry.getFormatter(column.getFormat());
                    if (!methodMap.containsKey(column.getName())) {
                        continue;
                    }
                    Method method = methodMap.get(column.getName());

                    Cell cell = row.createCell(x);
                    Object value = method.invoke(dataList.get(i));
                    cell.setCellValue(formatter.format(value));
                    ++x;
                }
                if (decorator != null) {
                    decorator.accept(row, dataList.get(i));
                }
            }

        } catch (Exception e) {
            log.error("export fail, cause={}", Throwables.getStackTraceAsString(e));
        }
        return wb;
    }

    @Override
    public Workbook export(List<?> dataList, Class<?> clazz) {
        final int CACHE_LINE = 1000;
        Workbook wb;
        if (dataList.size() > CACHE_LINE) {
            wb = new SXSSFWorkbook(CACHE_LINE);
        } else {
            wb = new XSSFWorkbook();
        }
        exportWithBook(wb, dataList, clazz.getSimpleName(), 0, 30000);
        return wb;
    }
    @Override
    public <T,R> void exportCsv(Supplier<Optional<R>> supplier, Class<T> clazz, OutputStream outputStream) throws IOException {
        ExportTable exportTable = tables.tables().get(clazz.getSimpleName());
        for (ExportColumn column : exportTable.getColumns()) {
            outputStream.write(column.getDisplay().getBytes());
            outputStream.write(",\t".getBytes());
        }
        outputStream.write("\n".getBytes());
        for (Optional<R> data = supplier.get(); data.isPresent(); data = supplier.get()) {
            var view = JsonObject.mapFrom(data.get());
            for (ExportColumn column : exportTable.getColumns()) {
                ColumnFormatter formatter = formatterRegistry.getFormatter(Optional.ofNullable(column.getFormat()).orElse("default"));
                String dataExportStr = "EXPORT_ERROR";
                try {
                    dataExportStr = formatter.format(view.getValue(column.getName()));
                } catch (Exception ex) {
                    log.error("{} fail to export data [{}] for [{}]", LogUtil.getClassMethodName(), data.get(), clazz.getSimpleName());
                }
                dataExportStr = dataExportStr.replaceAll("\"", "\"\"");
                outputStream.write("\"".getBytes());
                outputStream.write(dataExportStr.getBytes());
                outputStream.write("\",\t".getBytes());
            }
            outputStream.write("\n".getBytes());
        }
        outputStream.flush();
    }

    @Override
    public void exportWithBook(Workbook wb, List<?> dataList, String tableName, int prefix) {
        exportWithBook(wb, dataList, tableName, prefix, 30000);
    }

    private void exportWithBook(Workbook wb, List<?> dataList, String tableName, int prefix, long timeout) {
        try {
            Map<String, Future<ImageBuffer>> imageMap = new HashMap<>();
            Drawing<?> drawing = null;

            ExportTable table = tables.tables().get(tableName);

            Sheet sheet;
            if (wb.getSheet(table.getDisplay()) != null) {
                sheet = wb.getSheet(table.getDisplay());
            } else {
                sheet = wb.createSheet(table.getDisplay());
            }

            //标题
            int i = 0;
            if (sheet.getLastRowNum() <= 0) {
                //标题
                Row titleRow = sheet.createRow(0);
                for (ExportColumn column : table.getColumns()) {
                    Cell cell = titleRow.createCell(i);
                    cell.setCellValue(column.getDisplay());
                    sheet.setColumnWidth(i, column.getWidth());
                    i++;
                }
            }
            long startAt = System.currentTimeMillis();
            if (dataList.size() == 0) {
                return;
            }
            Set<String> imageColumn = new HashSet<>();
            for (ExportColumn column : table.getColumns()) {
                if (Boolean.TRUE.equals(column.getImg())) {
                    imageColumn.add(column.getName());
                }
            }
            for (String name : imageColumn) {
                for (Object o : dataList) {
                    var exportView = JsonObject.mapFrom(o);
                    String url = Objects.toString(exportView.getValue(name));
                    Future<ImageBuffer> f = DOWNLOAD.submit(() -> {
                        try {
                            if (imageCache.containsKey(url)) {
                                return imageCache.get(url);
                            }
                            HttpRequest request = HttpRequest.get(url);
                            if (!request.ok()) {
                                return new ImageBuffer(Buffer.buffer(), 0, 0);
                            }
                            Buffer buffer = Buffer.buffer(request.bytes());
                            BufferedImage image = ImgBufferUtil.image(buffer);
                            if (image == null) {
                                return new ImageBuffer(Buffer.buffer(), 0, 0);
                            }
                            imageCache.put(url, new ImageBuffer(buffer, image.getHeight(), image.getWidth()));
                            return new ImageBuffer(buffer, image.getHeight(), image.getWidth());
                        } catch (Exception e) {
                            return new ImageBuffer(Buffer.buffer(), 0, 0);
                        }
                    });
                    imageMap.put(url, f);
                }
            }
            for (i = 0; i < dataList.size(); i++) {
                var exportView = JsonObject.mapFrom(dataList.get(i));
                boolean needSetHeight = true;
                if (System.currentTimeMillis() - startAt > timeout) {
                    log.error("Export Data List -> {}, Data -> {} ,Time Out", dataList.size(), tableName);
                    return;
                }
                Row row = sheet.createRow(prefix + i + 1);
                int x = 0;
                for (ExportColumn column : table.getColumns()) {
                    if (column.getFormat() == null) {
                        column.setFormat("default");
                    }
                    ColumnFormatter formatter = formatterRegistry.getFormatter(column.getFormat());

                    Cell cell = row.createCell(x);
                    Object value = exportView.getValue(column.getName());
                    if (Boolean.TRUE.equals(column.getImg())) {
                        if (drawing == null) {
                            drawing = sheet.createDrawingPatriarch();
                        }

                        String url = value != null ? value.toString() : StringUtils.EMPTY;
                        ImageBuffer img = imageMap.get(url) != null ? imageMap.get(url).get(5, TimeUnit.SECONDS) : null;

                        if (img != null && img.buffer() != null && img.buffer().length() > 0) {
                            int pId = wb.addPicture(img.buffer().getBytes(), Workbook.PICTURE_TYPE_PNG); // PNG -> 6
                            int x2 = img.weight();
                            int y2 = img.height();
                            ClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, x, i + 1, x + 1, i + 2);
                            drawing.createPicture(anchor, pId);
                            if (needSetHeight) {
                                short height = (short) (y2 * sheet.getColumnWidth(x) / x2);
                                row.setHeight(height);
                                needSetHeight = false;
                            }
                        }
                    } else {
                        cell.setCellValue(formatter.format(value));
                    }
                    ++x;
                }
            }
        } catch (Exception e) {
            log.error("export fail, Name -> {}, Size = {}, cause={}", tableName, dataList.size(), Throwables.getStackTraceAsString(e));
        }
    }

    @Override
    public Buffer exportWithTimeOut(String className, List<?> data, int millsTimeout) {
        Workbook workbook = new HSSFWorkbook();
        exportWithBook(workbook, data, className, 0, millsTimeout);
        SheetMergeUtil.mergeWorkbook(workbook);
        ByteArrayOutputStream buff = new ByteArrayOutputStream();
        try {
            workbook.write(buff);
        } catch (Exception e) {
            log.error("Fail to Write ClassName -> {}, Size -> {}", className, data.size(), e);
        }
        return Buffer.buffer(buff.toByteArray());
    }

    record ImageBuffer(Buffer buffer, int height, int weight) {

    }
}

