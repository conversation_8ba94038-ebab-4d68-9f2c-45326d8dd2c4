package moonstone.web.front.component.user;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.dto.paging.UserWithTagsCriteria;
import moonstone.item.model.Tag;
import moonstone.item.model.UserWithTags;
import moonstone.item.service.ItemTagManager;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.model.view.SubUserView;
import moonstone.user.service.UserProfileReadService;
import moonstone.user.service.UserReadService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class UserViewBuildHelper {
    private final ItemTagManager itemTagManager;
    private final UserReadService<User> userReadService;
    private final UserProfileReadService userProfileReadService;

    /**
     * 修饰用户显示模式
     *
     * @param userRelationEntity 用户显示view
     * @param shopId             店铺Id
     * @return 用户显示模式
     */
    public SubUserView buildUserView(UserRelationEntity userRelationEntity, Long shopId) {
        InnerUser userView = new InnerUser();
        BeanUtils.copyProperties(userRelationEntity, userView);
        User user = userReadService.findById(userView.getUserId()).getResult();
        if (user != null) {
            userView.setMobile(user.getMobile());
        }

        Response<UserProfile> userProfileRes = userProfileReadService.findProfileByUserId(userView.getUserId());
        if (userProfileRes.isSuccess() && userProfileRes.getResult() != null) {
            userView.setName(userProfileRes.getResult().getRealName());
            userView.setAvatar(userProfileRes.getResult().getAvatar_());
        }
        decorateUserViewWithItemTag(userView, shopId);
        return userView;
    }

    private void decorateUserViewWithItemTag(InnerUser userView, Long shopId) {
        UserWithTagsCriteria userWithTagsCriteria = new UserWithTagsCriteria();
        userWithTagsCriteria.setUserIds(Collections.singletonList(userView.getUserId()));
        userWithTagsCriteria.setShopId(shopId);
        userWithTagsCriteria.setPageSize(Integer.MAX_VALUE);
        StringBuilder builder = new StringBuilder();
        for (UserWithTags userWithTags : itemTagManager.queryUserWithTag(userWithTagsCriteria).orElse(new Paging<>(0L, new ArrayList<>()))
                .getData()) {
            itemTagManager.getTag(userWithTags.getTagId()).map(Tag::getName).ifSuccess(tagName -> {
                builder.append(tagName);
                builder.append(",");
            });
        }
        if (builder.length() > 0) {
            builder.deleteCharAt(builder.length() - 1);
        }
        userView.setAllowBugTags(builder.toString());
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class InnerUser extends SubUserView {
        String allowBugTags;
    }
}
