/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.shop.service;

import io.terminus.common.model.Response;
import moonstone.common.model.Either;
import moonstone.shop.model.Shop;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-27
 */
public interface ShopWriteService {

    /**
     * 创建店铺
     * @param shop 店铺
     * @return 店铺id
     */
    Response<Long> create(Shop shop);


    /**
     * 更新店铺
     *
     * @param shop 店铺
     * @return 是否更新成功
     */
    Response<Boolean> update(Shop shop);

    /**
     * 审核店铺
     *
     * @param shopId 店铺Id
     * @return 是否审核成功
     */
    Either<Boolean> approval(Long shopId);
}
