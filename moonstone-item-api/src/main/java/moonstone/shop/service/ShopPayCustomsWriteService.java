package moonstone.shop.service;


import io.terminus.common.model.Response;
import moonstone.shop.model.ShopPayCustoms;

public interface ShopPayCustomsWriteService {
    /**
     * 新增支付海关信息
     *
     * @param shopPayCustoms 支付海关信息
     * @return 新增记录ID
     */
    Response<Long> create(ShopPayCustoms shopPayCustoms);

    /**
     * 更新支付海关信息
     *
     * @param shopPayCustoms 支付海关信息
     * @return 成功返回true
     */
    Response<Boolean> update(ShopPayCustoms shopPayCustoms);
}
