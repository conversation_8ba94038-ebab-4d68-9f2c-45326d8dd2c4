/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.service;

import io.terminus.common.model.Response;
import moonstone.common.model.Either;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.OrderShipment;
import moonstone.order.model.Shipment;
import moonstone.order.model.result.OrderShipmentInfoDO;

import javax.annotation.Nullable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 发货单读服务
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-11
 */
public interface ShipmentReadService {

    /**
     * 根据(子)订单id和级别查找对应的发货表
     *
     * @param orderId
     * @param orderLevel
     * @return
     */
    Response<List<OrderShipment>> findOrderShipmentByOrderAndOrderLevel(long orderId, OrderLevel orderLevel);

    /**
     * 根据(子)订单id和级别查找对应的发货单列表
     *
     * @param orderId    (子)订单id
     * @param orderLevel 级别
     * @return 对应的发货单列表
     */
    Response<List<Shipment>> findByOrderIdAndOrderLevel(Long orderId, OrderLevel orderLevel);

    /**
     * 根据(子)订单id列表和级别查找对应的发货单列表
     *
     * @param orderIds   (子)订单id列表
     * @param orderLevel 级别
     * @return 对应的发货单列表
     */
    Response<List<Shipment>> findByOrderIdsAndOrderLevel(List<Long> orderIds, OrderLevel orderLevel);

    /**
     * 根据id查找对应的发货单
     *
     * @param shipmentId 发货单id
     * @return 对应的发货单
     */
    Response<Shipment> findById(Long shipmentId);

    /**
     * 根据运费单号和公司查找对应的发货单
     *
     * @param shipmentSerialNo 发货单号
     * @param corpCode         承运公司代码
     * @return 对应的发货单
     */
    Response<Shipment> findBySerialNoAndCorpCode(String shipmentSerialNo, String corpCode);


    /**
     * 根据发货单id查找关联的(子)订单id列表
     *
     * @param shipmentId 发货单id
     * @return 对应发货单与(子)订单的关联关系
     */
    Response<List<OrderShipment>> findOrderIdsByShipmentId(Long shipmentId);

    /**
     * 根据运单状态和修改时间查询
     *
     * @param status    运单状态
     * @param updatedAt 运单修改时间
     */
    Either<List<Shipment>> findShipmentByStatusAndUpdatedAt(@Nullable Integer status, @Nullable Date updatedAt);

    Response<List<OrderShipmentInfoDO>> findByOrderIds(List<Long> orderIds, OrderLevel orderLevel);

    /**
     * 查询发货信息
     *
     * @param orderIdList
     * @param orderLevel
     * @return key = OrderId, value = OrderShipmentInfoDO
     */
    Map<Long, OrderShipmentInfoDO> getShipmentMap(List<Long> orderIdList, OrderLevel orderLevel);
}
