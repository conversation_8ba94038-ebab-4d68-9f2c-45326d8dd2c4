package moonstone.order.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Data
public class WithdrawPrinciple {
    String _id;
    Long shopId; // 平台Id
    Map<String, BigDecimal> moneyLimit = new HashMap<>();
    Map<String, Long> timeLimit = new HashMap<>();
    BigDecimal moneyBottomLimit = BigDecimal.ZERO;
    BigDecimal moneyMaxLimit;

    BigDecimal staticServiceFee = BigDecimal.ZERO;
    BigDecimal rateServiceFee = BigDecimal.ZERO;

    @AllArgsConstructor
    @Getter
    public enum IndexEnum {
        DAY("day"), MONTH("month"), YEAR("year");
        String index;
    }
}
