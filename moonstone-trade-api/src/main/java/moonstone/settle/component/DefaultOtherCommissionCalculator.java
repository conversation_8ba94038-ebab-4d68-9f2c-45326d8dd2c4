package moonstone.settle.component;

import io.terminus.common.model.Paging;
import moonstone.common.model.IsPersistAble;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.settle.api.OtherCommissionCalculator;
import moonstone.settle.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

/**
 * 默认的其他扩展佣金计算规则: 只返回0
 * <p>
 * DATE: 16/11/9 下午3:47 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Component
public class DefaultOtherCommissionCalculator implements OtherCommissionCalculator {
    @Autowired
    private BalanceDetailReadService balanceDetailReadService;

    @Override
    public OtherCommissionTree calculate(SettleTrade settleTrade, SettleFeeDetailTree feeDetailTree) {
        OtherCommissionTree tree = new OtherCommissionTree();
        tree.setRootCommission(new OtherCommission());
        for (Payment payment : settleTrade.getPaymentByIdMap().values()) {
            tree.getPaymentCommissionMap().put(payment.getId(), new OtherCommission());
        }
        for (RefundEntry refundEntry : settleTrade.getRefundByIdMap().values()) {
            OtherCommission otherCommission = new OtherCommission();
            BalanceDetailCriteria criteria = new BalanceDetailCriteria();
            criteria.setRelatedId(refundEntry.getRefund().getId());
            criteria.setType(ProfitType.OutCome.getValue());
            criteria.setStatusBitMarks(Arrays.asList(BalanceDetail.maskBit.RefundRelated.getValue(), IsPersistAble.maskBit.PersistAble.getValue()));
            criteria.setNotStatusBitMarks(Collections.singletonList(BalanceDetail.presentMaskBit.Present.getValue()));
            criteria.setSourceId(refundEntry.getShopOrder().getShopId());
            Optional.ofNullable(balanceDetailReadService.paging(criteria.toMap()).getResult()).map(Paging::getData).orElse(new ArrayList<>())
                    .stream().map(BalanceDetail::getChangeFee).reduce(Long::sum)
                    .map(a -> -a)
                    .ifPresent(otherCommission::setCommission1);
            tree.getRefundCommissionMap().put(refundEntry.getRefund().getId(), new OtherCommission());
        }
        for (ShopOrder shopOrder : settleTrade.getShopOrderByIdMap().values()) {
            OtherCommission otherCommission = new OtherCommission();
            BalanceDetailCriteria criteria = new BalanceDetailCriteria();
            criteria.setRelatedId(shopOrder.getId());
            criteria.setType(ProfitType.InCome.getValue());
            criteria.setStatusBitMarks(Arrays.asList(BalanceDetail.maskBit.OrderRelated.getValue(), IsPersistAble.maskBit.PersistAble.getValue()));
            criteria.setNotStatusBitMarks(Collections.singletonList(BalanceDetail.presentMaskBit.Present.getValue()));
            criteria.setSourceId(shopOrder.getShopId());
            Optional.ofNullable(balanceDetailReadService.paging(criteria.toMap()).getResult()).map(Paging::getData).orElse(new ArrayList<>())
                    .stream().map(BalanceDetail::getChangeFee).reduce(Long::sum)
                    .ifPresent(otherCommission::setCommission1);
            tree.getShopOrderCommissionMap().put(shopOrder.getId(), otherCommission);
        }
        for (SkuOrder skuOrder : settleTrade.getSkuOrderList()) {
            tree.getSkuOrderCommissionMap().put(skuOrder.getId(), new OtherCommission());
        }
        return tree;
    }
}
