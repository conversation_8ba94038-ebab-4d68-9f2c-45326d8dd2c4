/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.impl.service;

import moonstone.BaseServiceTest;
import moonstone.category.model.BackCategory;
import moonstone.category.service.BackCategoryReadService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.hamcrest.CoreMatchers.is;
import static org.junit.Assert.assertThat;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-24
 */
public class BackCategoryReadServiceImplTest extends BaseServiceTest {

    @Autowired
    private BackCategoryReadService backCategoryReadService;

    @Test
    public void testFindAncestorsOf() throws Exception {
        List<BackCategory> actual = backCategoryReadService.findAncestorsOf(4L).getResult();
        assertThat(actual.size(),is(2));
    }
}