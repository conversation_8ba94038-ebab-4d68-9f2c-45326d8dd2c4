package moonstone.user.enums;

/**
 * Created by CaiZhy on 2018/10/12.
 */
public enum UserWxRoad {
    TO_LOGIN(1, "仅需登录"),
    WX_USER_CREATE(2, "创建用户跟创建微信"),
    WX_ADD(3,"已有账户,用户添加USER_WX表");

    private final int value;
    private final String desc;

    UserWxRoad(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public UserWxRoad getEnum(int value) {
        for (UserWxRoad item: UserWxRoad.values()) {
            if (item.value == value)
                return item;
        }
        return null;
    }

}
