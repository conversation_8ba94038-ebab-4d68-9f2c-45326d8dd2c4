<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="WeDistributionApplication">

    <resultMap id="WeDistributionApplicationMap" type="WeDistributionApplication">
        <id property="id" column="id"/>
        <result column="we_shop_id" property="weShopId"/>
        <result column="user_id" property="userId"/>
        <result column="shop_id" property="shopId"/>
        <result column="recommend_type" property="recommendType"/>
        <result column="recommend_shop_id" property="recommendShopId"/>
        <result column="recommend_user_id" property="recommendUserId"/>
        <result column="apply_remark" property="applyRemark"/>
        <result column="auditing_remark" property="auditingRemark"/>
        <result column="extra_json" property="extraJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        we_distribution_applications
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `we_shop_id`, `user_id`, `shop_id`, `recommend_type`, `recommend_shop_id`, `recommend_user_id`,
        `apply_remark`, `auditing_remark`, `extra_json`, `tags_json`, `status`, `created_at`, `updated_at`
    </sql>

    <sql id="vals">
        #{weShopId}, #{userId}, #{shopId}, #{recommendType}, #{recommendShopId}, #{recommendUserId},
        #{applyRemark}, #{auditingRemark}, #{extraJson}, #{tagsJson}, #{status}, now(), now()
    </sql>

    <sql id="criteria">
        <if test="ids != null">AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="weShopId != null">AND we_shop_id = #{weShopId}</if>
        <if test="userIds != null">AND user_id IN
            <foreach collection="userIds" open="(" separator="," close=")" item="userId">#{userId}</foreach>
        </if>
        <if test="shopId != null">AND shop_id = #{shopId}</if>
        <if test="recommendType != null">AND recommend_type = #{recommendType}</if>
        <if test="recommendShopId != null">AND recommend_shop_id = #{recommendShopId}</if>
        <if test="recommendUserId != null">AND recommend_user_id = #{recommendUserId}</if>
        <if test="statuses == null and status == null">
            AND `status` != -99
        </if>
        <if test="status != null">AND status = #{status}</if>
        <if test="statuses != null">
            and status in
            <foreach collection="statuses" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'weShopId'">ORDER BY `we_shop_id`
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'userId'">ORDER BY user_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'shopId'">ORDER BY shop_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'recommendType'">ORDER BY recommend_type
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'recommendShopId'">ORDER BY recommend_shop_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'recommendUserId'">ORDER BY recommend_user_id
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <insert id="create" parameterType="WeDistributionApplication" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.weShopId}, #{i.userId}, #{i.shopId}, #{i.recommendType}, #{i.recommendShopId}, #{i.recommendUserId},
            #{i.applyRemark}, #{i.auditingRemark}, #{i.extraJson}, #{i.tagsJson}, #{i.status}, now(), now()
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="WeDistributionApplicationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="WeDistributionApplicationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByWeShopId" parameterType="long" resultMap="WeDistributionApplicationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `we_shop_id` = #{weShopId} AND status != -99
    </select>

    <select id="findByWeShopIdAuditing" parameterType="long" resultMap="WeDistributionApplicationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `we_shop_id` = #{weShopId} AND status = 1
    </select>

    <select id="findByUserId" parameterType="long" resultMap="WeDistributionApplicationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `user_id` = #{userId} AND status != -99
    </select>

    <select id="findByRecommendShopId" parameterType="long" resultMap="WeDistributionApplicationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `recommend_shop_id` = #{recommendShopId} AND status != -99
    </select>

    <select id="findByRecommendUserId" parameterType="long" resultMap="WeDistributionApplicationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `recommend_user_id` = #{recommendUserId} AND status != -99
    </select>

    <select id="findByWeShopIdRecommendAuditing" parameterType="map" resultMap="WeDistributionApplicationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            `we_shop_id` = #{weShopId}
            AND `recommend_type` = #{recommendType}
            AND `recommend_shop_id` = #{recommendShopId}
            AND `recommend_user_id` = #{recommendUserId}
            AND status = 1
        </where>
        LIMIT 1
    </select>

    <update id="update" parameterType="WeDistributionApplication">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="weShopId != null">`we_shop_id` = #{weShopId},</if>
            <if test="userId != null">`user_id` = #{userId},</if>
            <if test="shopId != null">`shop_id` = #{shopId},</if>
            <if test="recommendType != null">`recommend_type` = #{recommendType},</if>
            <if test="recommendShopId != null">`recommend_shop_id` = #{recommendShopId},</if>
            <if test="recommendUserId != null">`recommend_user_id` = #{recommendUserId},</if>
            <if test="applyRemark != null">`apply_remark` = #{applyRemark},</if>
            <if test="auditingRemark != null">`auditing_remark` = #{auditingRemark},</if>
            <if test="extraJson != null">`extra_json` = #{extraJson},</if>
            <if test="tagsJson != null">`tags_json` = #{tagsJson},</if>
            <if test="status != null">`status` = #{status},</if>
            `updated_at` = now()
        </set>
        WHERE id = #{id} and status != -99
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id = #{id}
    </update>

    <update id="batchUpdateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id IN
        <foreach item="id" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <update id="deletes" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET status = -99
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="WeDistributionApplicationMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>
</mapper>
