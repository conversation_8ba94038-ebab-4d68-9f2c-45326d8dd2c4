package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.impl.dao.OutIdShopOrderDao;
import moonstone.order.model.OutIdShopOrder;
import moonstone.order.service.OutIdShopOrderReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@RpcProvider
@Service
@Slf4j
public class OutIdShopOrderReadServiceImpl implements OutIdShopOrderReadService {
    @Autowired
    OutIdShopOrderDao outIdShopOrderDao;

    @Override
    public Response<OutIdShopOrder> findById(Long id) {
        try {
            return Response.ok(outIdShopOrderDao.findById(id));
        } catch (Exception ex) {
            log.error("fail to find outIdShopOrderDao Id:{} cause:{}", id, ex.getMessage());
            ex.printStackTrace();
            return Response.fail("fail.find.outIdShopOrder");
        }
    }

    @Override
    public Response<List<OutIdShopOrder>> findByShopId(Long shopId) {
        try {
            return Response.ok(outIdShopOrderDao.findByShopId(shopId));
        } catch (Exception ex) {

            log.error("fail to find outIdShopOrderDao shopId:{} cause:{}", shopId, ex.getMessage());
            ex.printStackTrace();
            return Response.fail("fail.find.outIdShopOrder");
        }
    }

    @Override
    public Response<OutIdShopOrder> findByOutIdAndShopId(Long shopId, String outId) {
        try {
            return Response.ok(outIdShopOrderDao.findByOutIdAndShopId(shopId, outId));
        } catch (
                Exception ex
        ) {
            log.error("fail to find outIdShopOrder shopId:{} outId:{}, cause:{}", shopId, outId, ex.getMessage());
            ex.printStackTrace();
            return Response.fail("fail.find.outIdShopOrder");
        }
    }

    @Override
    public Either<List<OutIdShopOrder>> findByShopOrderIdWithLimit(Long shopOrderId, Integer limit) {
        try {
            return Either.ok(outIdShopOrderDao.findByShopOrderIdWithLimit(shopOrderId, limit));
        } catch (Exception ex) {
            log.error("{} shopOrderId:{} limit:{} ex:", LogUtil.getClassMethodName(), shopOrderId, limit, ex);
            return Either.error(ex);
        }
    }
}
