## 订单预览

* 请求url
```
GET order.preview
```

* 请求参数示例
```
skuIdAndQuantity=[{"skuId":1499,"quantity":1},{"skuId":5763,"quantity":1}]&channel=2
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| skuIdAndQuantity | 字符串 | skuId及数量 | 是 |  |
| channel | 整形 | 渠道 | 否 | 1-PC,2-手机,默认为2 |


* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
     {
       "success": true,
       "result": {
         "receiverInfo": null,
         "buyer": {
           "id": 2,
           "name": "buyer",
           "type": 2,
           "shopId": null,
           "roles": [
             "BUYER"
           ],
           "extra": null,
           "tags": null,
           "typeName": null
         },
         "richSkusByShops": [
           {
             "shop": {
               "id": 1,
               "outerId": "*********",
               "userId": 3,
               "userName": "seller",
               "name": "第一个店铺",
               "status": 1,
               "type": 1,
               "phone": "",
               "businessId": null,
               "imageUrl": "//terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/11/610f7eb0-aba4-4484-918c-f2cb53e7219c.png",
               "address": null,
               "extra": {
                 "isVatInvoice": "true",
                 "city": "哈尔滨市",
                 "companyRegionId": "130102",
                 "defaultInvoiceValue": "2",
                 "companyName": "老王二厂",
                 "companyProvinceId": "130000",
                 "customPhone": "",
                 "cityId": "230100",
                 "companyCityId": "130100",
                 "companyStreet": "and",
                 "isCod": "true",
                 "province": "黑龙江省",
                 "isEinvoice": "true",
                 "street": "45674324567865",
                 "companyProvince": "河北省",
                 "companyCity": "石家庄市",
                 "defaultInvoice": "2",
                 "contactName": "老王",
                 "provinceId": "230000",
                 "customEmail": "",
                 "companyRegion": "长安区",
                 "regionId": "230103",
                 "contactPhone": "18888888888",
                 "region": "南岗区"
               },
               "tags": null,
               "tagsJson": null
             },
             "richSkus": [
               {
                 "item": {
                   "id": 1245,
                   "itemCode": "************",
                   "categoryId": 318,
                   "spuId": null,
                   "shopId": 1,
                   "shopName": "浙江天猫供应链管理有限公司",
                   "brandId": 29,
                   "brandName": "得力（DELI）",
                   "name": "Deli/得力 S52 中性笔 1桶 碳素笔 水笔 签字笔 书写笔 30支/桶",
                   "mainImage": "https://img.alicdn.com/i3/TB18E4yKFXXXXXPXpXXXXXXXXXX_!!0-item_pic.jpg",
                   "lowPrice": 1950,
                   "highPrice": 1950,
                   "stockType": 1,
                   "stockQuantity": 77,
                   "saleQuantity": 20,
                   "status": 1,
                   "onShelfAt": null,
                   "advertise": null,
                   "specification": null,
                   "type": 1,
                   "reduceStockType": 1,
                   "extra": {
                     "itemVersion": "7"
                   },
                   "tags": null,
                   "tagsJson": null,
                   "itemInfoMd5": "8e514e49b0236f2769ce0b26253ca2b2",
                   "createdAt": 1459149809000,
                   "updatedAt": 1465282882000
                 },
                 "sku": {
                   "id": 1499,
                   "skuCode": "3122284813323",
                   "itemId": 1245,
                   "shopId": 1,
                   "status": 1,
                   "specification": null,
                   "outerSkuId": null,
                   "outerShopId": null,
                   "image": null,
                   "thumbnail": null,
                   "name": null,
                   "extraPriceJson": "{\"originPrice\":1990}",
                   "extraPrice": {
                     "originPrice": 1990
                   },
                   "price": 1950,
                   "attrs": [
                     {
                       "attrKey": "颜色分类",
                       "attrVal": "黑色",
                       "unit": null,
                       "showImage": null,
                       "thumbnail": null,
                       "image": null
                     }
                   ],
                   "stockType": 1,
                   "stockQuantity": 77,
                   "extra": null,
                   "createdAt": 1459149809000,
                   "updatedAt": 1465282872000
                 },
                 "quantity": 1,
                 "receiverInfo": null,
                 "invoiceId": null,
                 "shipmentType": null,
                 "channel": null,
                 "skuPromotionPrice": null,
                 "fee": 1950,
                 "originFee": 1950,
                 "discount": null,
                 "shipFee": null,
                 "shipFeeDiscount": null,
                 "integral": null,
                 "balance": null,
                 "itemSnapshotId": null,
                 "promotionId": null,
                 "skuPromotions": [],
                 "orderStatus": null,
                 "mask": 1
               }
             ],
             "buyerNote": null,
             "receiverInfo": null,
             "invoiceId": null,
             "shipmentType": null,
             "companyId": null,
             "fee": 1950,
             "originFee": 1950,
             "discount": null,
             "shipFee": null,
             "originShipFee": null,
             "integral": null,
             "balance": null,
             "promotionId": null,
             "shopPromotions": [],
             "shipmentPromotionId": null,
             "shipmentPromotions": null,
             "channel": 2,
             "orderStatus": null,
             "commissionRate": 10
           },
           {
             "shop": {
               "id": 18,
               "outerId": null,
               "userId": 26,
               "userName": "leo",
               "name": "护肤美妆专营店",
               "status": 1,
               "type": 1,
               "phone": "***********",
               "businessId": null,
               "imageUrl": "//terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/28/e9c5fc2f-0e79-463a-840e-8e2d485846ea.png",
               "address": null,
               "extra": {
                 "isVatInvoice": "true",
                 "city": "北京市",
                 "companyRegionId": "110101",
                 "defaultInvoiceValue": "2",
                 "companyName": "三十的公司",
                 "companyProvinceId": "110000",
                 "customPhone": "",
                 "cityId": "110100",
                 "companyCityId": "110100",
                 "companyStreet": "三十的街道",
                 "isCod": "true",
                 "province": "北京",
                 "isEinvoice": "true",
                 "street": "三十的店",
                 "companyProvince": "北京",
                 "companyCity": "北京市",
                 "defaultInvoice": "2",
                 "contactName": "三十",
                 "provinceId": "110000",
                 "customEmail": "",
                 "companyRegion": "东城区",
                 "regionId": "110101",
                 "contactPhone": "***********",
                 "region": "东城区"
               },
               "tags": null,
               "tagsJson": null
             },
             "richSkus": [
               {
                 "item": {
                   "id": 5004,
                   "itemCode": "",
                   "categoryId": 604,
                   "spuId": null,
                   "shopId": 18,
                   "shopName": "护肤美妆专营店",
                   "brandId": null,
                   "brandName": null,
                   "name": "VDL贝壳提亮液妆前乳30ml遮毛孔隔离霜保湿控油隐形毛孔柔嫩肌肤",
                   "mainImage": "//terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/14/5eb6ff00-db2a-43f8-991e-9a4025389a2a.jpg",
                   "lowPrice": 9900,
                   "highPrice": 9900,
                   "stockType": 0,
                   "stockQuantity": 9993,
                   "saleQuantity": 6,
                   "status": 1,
                   "onShelfAt": null,
                   "advertise": null,
                   "specification": "",
                   "type": 1,
                   "reduceStockType": 1,
                   "extra": {
                     "unit": "件",
                     "selfPlatformLink": "",
                     "unitQuantity": ""
                   },
                   "tags": {
                     "promotionTypes": "3"
                   },
                   "tagsJson": "{\"promotionTypes\":\"3\"}",
                   "itemInfoMd5": "83a7c3dd1bc4fd1110b74bd50dd26e84",
                   "createdAt": 1468490348000,
                   "updatedAt": 1471491000000
                 },
                 "sku": {
                   "id": 5763,
                   "skuCode": null,
                   "itemId": 5004,
                   "shopId": 18,
                   "status": 1,
                   "specification": null,
                   "outerSkuId": "",
                   "outerShopId": null,
                   "image": null,
                   "thumbnail": null,
                   "name": null,
                   "extraPriceJson": "{\"platformPrice\":9900,\"originPrice\":19900}",
                   "extraPrice": {
                     "platformPrice": 9900,
                     "originPrice": 19900
                   },
                   "price": 9900,
                   "attrs": [
                     {
                       "attrKey": "颜色分类",
                       "attrVal": "乳白色",
                       "unit": null,
                       "showImage": null,
                       "thumbnail": null,
                       "image": null
                     }
                   ],
                   "stockType": 0,
                   "stockQuantity": 9993,
                   "extra": {
                     "unitQuantity": "1"
                   },
                   "createdAt": 1468490348000,
                   "updatedAt": 1470655546000
                 },
                 "quantity": 1,
                 "receiverInfo": null,
                 "invoiceId": null,
                 "shipmentType": null,
                 "channel": null,
                 "skuPromotionPrice": 6900,
                 "fee": 6900,
                 "originFee": 9900,
                 "discount": 3000,
                 "shipFee": null,
                 "shipFeeDiscount": null,
                 "integral": null,
                 "balance": null,
                 "itemSnapshotId": null,
                 "promotionId": 38,
                 "skuPromotions": [
                   {
                     "id": 38,
                     "shopId": 18,
                     "name": "化妆品全场大促",
                     "promotionDefId": 20,
                     "type": 3,
                     "status": 2,
                     "startAt": 1467302400000,
                     "endAt": 1477756799000,
                     "userScopeParams": null,
                     "skuScopeParams": {
                       "type": "include",
                       "skuIds": "5771,5770,5763,5762,5761,5760",
                       "itemIds": "5011,5010,5004,5003,5002,5001"
                     },
                     "conditionParams": {},
                     "behaviorParams": {
                       "skusWithDiscount": "[{\"skuId\":5760,\"discount\":75},{\"skuId\":5761,\"discount\":65},{\"skuId\":5762,\"discount\":60},{\"skuId\":5763,\"reduceFee\":3000},{\"skuId\":5770,\"reduceFee\":2000},{\"skuId\":5771,\"reduceFee\":2000}]"
                     },
                     "extra": null,
                     "createdAt": 1468984911000,
                     "updatedAt": 1468984919000
                   }
                 ],
                 "orderStatus": null,
                 "mask": 1
               }
             ],
             "buyerNote": null,
             "receiverInfo": null,
             "invoiceId": null,
             "shipmentType": null,
             "companyId": null,
             "fee": 6900,
             "originFee": 6900,
             "discount": null,
             "shipFee": null,
             "originShipFee": null,
             "integral": null,
             "balance": null,
             "promotionId": null,
             "shopPromotions": [],
             "shipmentPromotionId": null,
             "shipmentPromotions": null,
             "channel": 2,
             "orderStatus": null,
             "commissionRate": 10
           }
         ],
         "payType": null,
         "promotionId": null,
         "promotions": [],
         "extra": null,
         "fee": 8850,
         "originFee": 8850,
         "discount": null,
         "integral": null,
         "balance": null
       }
     }

    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询商品失败"
     }
    ```


## 创建订单

* 请求url
```
POST order.create
```

* 请求参数示例
```
order=
{
  "receiverInfoId": 22,
  "submittedSkusByShops": [
    {
      "shopId": 1,
      "buyerNote": "",
      "invoiceId": 39,
      "promotionId": null,
      "submittedSkus": [
        {
          "skuId": 5721,
          "quantity": 3,
          "promotionId": null
        }
      ]
    },
    {
      "shopId": 18,
      "buyerNote": "",
      "invoiceId": 39,
      "promotionId": null,
      "submittedSkus": [
        {
          "skuId": 5763,
          "quantity": 1,
          "promotionId": 38
        }
      ]
    }
  ],
  "channel": 1,
  "payType": 1
}
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| receiverInfoId | 整形 | 收货地址id | 是 |  |
| channel | 整形 | 下单渠道 | 否 | 1-PC,2-移动,默认2 |
| payType | 整形 | 支付方式 | 否 | 1-在线支付,2-货到付款,默认1 |
| submittedSkusByShops.shopId | 整形 | 店铺id | 是 | |
| submittedSkusByShops.buyerNote | 字符串 | 买家留言 | 否 | |
| submittedSkusByShops.invoiceId | 整形 | 发票id | 否 | |
| submittedSkusByShops.promotionId | 整形 | 店铺级别营销活动id | 否 | |
| submittedSkusByShops.submittedSkus.skuId | 整形 | sku id | 是 | |
| submittedSkusByShops.submittedSkus.quantity | 整形 | sku数量 | 是 | |
| submittedSkusByShops.submittedSkus.promotionId | 整形 | 商品级别营销活动id | 否 | |



* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": [411,412]
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| result | 整形数组 | 生成的订单id |  |

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "创建订单失败"
     }
    ```


## 买家查询订单

* 请求url
```
GET order.query.for.buyer
```

* 请求参数示例
```
id=&shopName=&startAt=2016-08-24&endAt=2016-08-26&statusStr=0&pageNo=1&pageSize=20
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| id | 整形 | 订单id | 否 |  |
| shopName | 字符串 | 店铺名称 | 否 |  |
| startAt | 字符串 | 下单日期开始范围 | 否 |  |
| endAt | 字符串 | 下单日期截止范围 | 否 |  |
| statusStr | 字符串 | 订单状态 | 否 | 多个状态之间用逗号分隔 |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": {
        "total": 2,
        "data": [
          {
            "shopOrder": {
              "id": 411,
              "shopId": 1,
              "buyerId": 2,
              "fee": 16000,
              "status": 0,
              "promotionId": null,
              "extra": null,
              "tags": null,
              "createdAt": 1472002943000,
              "updatedAt": 1472002943000,
              "buyerName": "buyer",
              "outBuyerId": null,
              "shopName": "第一个店铺",
              "outShopId": "*********",
              "companyId": null,
              "originFee": 15000,
              "shipFee": 1000,
              "originShipFee": 1000,
              "shipmentPromotionId": null,
              "discount": null,
              "integral": null,
              "balance": null,
              "shipmentType": null,
              "payType": 1,
              "channel": 1,
              "hasRefund": null,
              "buyerNote": "",
              "outId": null,
              "outFrom": null,
              "commissionRate": 10
            },
            "shopOrderOperations": [
              {
                "value": 1,
                "text": "pay",
                "operator": [
                  "buyer"
                ]
              },
              {
                "value": -2,
                "text": "sellerCancel",
                "operator": [
                  "seller",
                  "admin"
                ]
              },
              {
                "value": -1,
                "text": "buyerCancel",
                "operator": [
                  "buyer"
                ]
              }
            ],
            "skuOrderAndOperations": [
              {
                "skuOrder": {
                  "id": 614,
                  "shopId": 1,
                  "buyerId": 2,
                  "fee": 15000,
                  "status": 0,
                  "promotionId": null,
                  "extra": null,
                  "tags": null,
                  "createdAt": 1472002943000,
                  "updatedAt": 1472002944000,
                  "skuId": 5721,
                  "quantity": 3,
                  "orderId": 411,
                  "outId": null,
                  "buyerName": "buyer",
                  "outBuyerId": null,
                  "itemId": 4,
                  "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
                  "skuImage": "//parana.oss-cn-hangzhou.aliyuncs.com/2016/07/10/TB1VF8TJpXXXXbZXFXXXXXXXXXX_!!2-item_pic.png",
                  "shopName": "第一个店铺",
                  "outShopId": null,
                  "companyId": null,
                  "outSkuId": null,
                  "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
                  "skuAttrs": [
                    {
                      "attrKey": "颜色分类",
                      "attrVal": "白色",
                      "unit": null,
                      "showImage": null,
                      "thumbnail": null,
                      "image": null
                    }
                  ],
                  "channel": 1,
                  "payType": 1,
                  "shipmentType": 1,
                  "originFee": 15000,
                  "discount": null,
                  "shipFee": null,
                  "shipFeeDiscount": null,
                  "integral": null,
                  "balance": null,
                  "itemSnapshotId": 135,
                  "hasRefund": false,
                  "invoiced": false,
                  "commented": null
                },
                "skuOrderOperations": [
                  {
                    "value": 1,
                    "text": "pay",
                    "operator": [
                      "buyer"
                    ]
                  },
                  {
                    "value": -2,
                    "text": "sellerCancel",
                    "operator": [
                      "seller",
                      "admin"
                    ]
                  },
                  {
                    "value": -1,
                    "text": "buyerCancel",
                    "operator": [
                      "buyer"
                    ]
                  }
                ]
              }
            ]
          },
          {
            "shopOrder": {
              "id": 412,
              "shopId": 18,
              "buyerId": 2,
              "fee": 6900,
              "status": 0,
              "promotionId": null,
              "extra": null,
              "tags": null,
              "createdAt": 1472002943000,
              "updatedAt": 1472002943000,
              "buyerName": "buyer",
              "outBuyerId": null,
              "shopName": "护肤美妆专营店",
              "outShopId": null,
              "companyId": null,
              "originFee": 6900,
              "shipFee": 0,
              "originShipFee": 0,
              "shipmentPromotionId": null,
              "discount": null,
              "integral": null,
              "balance": null,
              "shipmentType": null,
              "payType": 1,
              "channel": 1,
              "hasRefund": null,
              "buyerNote": "",
              "outId": null,
              "outFrom": null,
              "commissionRate": 10
            },
            "shopOrderOperations": [
              {
                "value": 1,
                "text": "pay",
                "operator": [
                  "buyer"
                ]
              },
              {
                "value": -2,
                "text": "sellerCancel",
                "operator": [
                  "seller",
                  "admin"
                ]
              },
              {
                "value": -1,
                "text": "buyerCancel",
                "operator": [
                  "buyer"
                ]
              }
            ],
            "skuOrderAndOperations": [
              {
                "skuOrder": {
                  "id": 615,
                  "shopId": 18,
                  "buyerId": 2,
                  "fee": 6900,
                  "status": 0,
                  "promotionId": 38,
                  "extra": null,
                  "tags": null,
                  "createdAt": 1472002943000,
                  "updatedAt": 1472002944000,
                  "skuId": 5763,
                  "quantity": 1,
                  "orderId": 412,
                  "outId": null,
                  "buyerName": "buyer",
                  "outBuyerId": null,
                  "itemId": 5004,
                  "itemName": "VDL贝壳提亮液妆前乳30ml遮毛孔隔离霜保湿控油隐形毛孔柔嫩肌肤",
                  "skuImage": "//terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/14/5eb6ff00-db2a-43f8-991e-9a4025389a2a.jpg",
                  "shopName": "护肤美妆专营店",
                  "outShopId": null,
                  "companyId": null,
                  "outSkuId": null,
                  "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"乳白色\"}]",
                  "skuAttrs": [
                    {
                      "attrKey": "颜色分类",
                      "attrVal": "乳白色",
                      "unit": null,
                      "showImage": null,
                      "thumbnail": null,
                      "image": null
                    }
                  ],
                  "channel": 1,
                  "payType": 1,
                  "shipmentType": 1,
                  "originFee": 9900,
                  "discount": 3000,
                  "shipFee": null,
                  "shipFeeDiscount": null,
                  "integral": null,
                  "balance": null,
                  "itemSnapshotId": 62,
                  "hasRefund": false,
                  "invoiced": false,
                  "commented": null
                },
                "skuOrderOperations": [
                  {
                    "value": 1,
                    "text": "pay",
                    "operator": [
                      "buyer"
                    ]
                  },
                  {
                    "value": -2,
                    "text": "sellerCancel",
                    "operator": [
                      "seller",
                      "admin"
                    ]
                  },
                  {
                    "value": -1,
                    "text": "buyerCancel",
                    "operator": [
                      "buyer"
                    ]
                  }
                ]
              }
            ]
          }
        ],
        "empty": false
      }
    }

    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询订单失败"
     }
    ```


## 查询订单详情

* 请求url
```
GET order.detail.query
```

* 请求参数示例
```
id=412
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| id | 整形 | 订单id | 是 |  |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
     {
       "success": true,
       "result": {
         "shopOrder": {
           "id": 412,
           "shopId": 18,
           "buyerId": 2,
           "fee": 6900,
           "status": 0,
           "promotionId": null,
           "extra": null,
           "tags": null,
           "createdAt": 1472002943000,
           "updatedAt": 1472002943000,
           "buyerName": "buyer",
           "outBuyerId": null,
           "shopName": "护肤美妆专营店",
           "outShopId": null,
           "companyId": null,
           "originFee": 6900,
           "shipFee": 0,
           "originShipFee": 0,
           "shipmentPromotionId": null,
           "discount": null,
           "integral": null,
           "balance": null,
           "shipmentType": null,
           "payType": 1,
           "channel": 1,
           "hasRefund": null,
           "buyerNote": "",
           "outId": null,
           "outFrom": null,
           "commissionRate": 10
         },
         "skuOrders": [
           {
             "id": 615,
             "shopId": 18,
             "buyerId": 2,
             "fee": 6900,
             "status": 0,
             "promotionId": 38,
             "extra": null,
             "tags": null,
             "createdAt": 1472002943000,
             "updatedAt": 1472002944000,
             "skuId": 5763,
             "quantity": 1,
             "orderId": 412,
             "outId": null,
             "buyerName": "buyer",
             "outBuyerId": null,
             "itemId": 5004,
             "itemName": "VDL贝壳提亮液妆前乳30ml遮毛孔隔离霜保湿控油隐形毛孔柔嫩肌肤",
             "skuImage": "//terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/14/5eb6ff00-db2a-43f8-991e-9a4025389a2a.jpg",
             "shopName": "护肤美妆专营店",
             "outShopId": null,
             "companyId": null,
             "outSkuId": null,
             "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"乳白色\"}]",
             "skuAttrs": [
               {
                 "attrKey": "颜色分类",
                 "attrVal": "乳白色",
                 "unit": null,
                 "showImage": null,
                 "thumbnail": null,
                 "image": null
               }
             ],
             "channel": 1,
             "payType": 1,
             "shipmentType": 1,
             "originFee": 9900,
             "discount": 3000,
             "shipFee": null,
             "shipFeeDiscount": null,
             "integral": null,
             "balance": null,
             "itemSnapshotId": 62,
             "hasRefund": false,
             "invoiced": false,
             "commented": null
           }
         ],
         "payment": null,
         "promotion": null,
         "discount": 3000,
         "shopOrderOperations": [
           {
             "value": 1,
             "text": "pay",
             "operator": [
               "buyer"
             ]
           },
           {
             "value": -2,
             "text": "sellerCancel",
             "operator": [
               "seller",
               "admin"
             ]
           },
           {
             "value": -1,
             "text": "buyerCancel",
             "operator": [
               "buyer"
             ]
           }
         ],
         "orderReceiverInfos": [
           {
             "orderId": 412,
             "orderType": 1,
             "id": 409,
             "receiverInfo": {
               "id": 22,
               "userId": 2,
               "receiveUserName": "小郭郭",
               "phone": null,
               "mobile": "18969973054",
               "email": null,
               "isDefault": true,
               "status": 1,
               "province": "北京",
               "provinceId": 110000,
               "city": "北京市",
               "cityId": 110100,
               "region": "东城区",
               "regionId": 110101,
               "street": null,
               "streetId": null,
               "detail": "sdfsdfs",
               "postcode": "123456",
               "createdAt": 1469176486000,
               "updatedAt": 1469448425000
             },
             "createdAt": 1472002943000,
             "updatedAt": 1472002943000
           }
         ],
         "invoices": [
           {
             "id": 39,
             "userId": 2,
             "title": "白度",
             "detail": {
               "type": "1",
               "titleType": "2"
             },
             "status": 1,
             "isDefault": true,
             "createdAt": 1470626253000,
             "updatedAt": 1470626253000
           }
         ],
         "shipType": 0,
         "shipAt": null,
         "autoCancelTime": 1472004743000,
         "autoConfirmTime": null,
         "confirmAt": null,
         "commentAt": null
       }
     }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询订单失败"
     }
    ```


## 查询店铺订单下的所有子订单

* 请求url
```
GET sku.orders.find.by.shop.order.id
```

* 请求参数示例
```
id=412
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| id | 整形 | 店铺订单id | 是 |  |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": [
        {
          "id": 616,
          "shopId": 16,
          "buyerId": 25,
          "fee": 140800,
          "status": -8,
          "promotionId": null,
          "extra": null,
          "tags": null,
          "createdAt": 1471440036000,
          "updatedAt": 1473067186000,
          "skuId": 5759,
          "quantity": 11,
          "orderId": 412,
          "outId": null,
          "buyerName": "英俊",
          "outBuyerId": null,
          "itemId": 5000,
          "itemName": "艾蜜莉8口味松露型巧克力2盒装800克礼盒装零食礼品（代可可脂） ",
          "skuImage": "http://terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/14/af1fe51a-f688-4613-a488-edc616369797.jpg",
          "shopName": "锁妖塔",
          "outShopId": null,
          "companyId": null,
          "outSkuId": null,
          "skuAttributes": null,
          "skuAttrs": null,
          "channel": 1,
          "payType": 1,
          "shipmentType": 1,
          "originFee": 140800,
          "discount": 0,
          "shipFee": null,
          "shipFeeDiscount": null,
          "integral": null,
          "balance": null,
          "itemSnapshotId": 61,
          "hasRefund": true,
          "invoiced": false,
          "commented": 1,
          "hasApplyAfterSale": null,
          "commissionRate": 0
        },
        {
          "id": 617,
          "shopId": 16,
          "buyerId": 25,
          "fee": 432000,
          "status": 2,
          "promotionId": 131,
          "extra": null,
          "tags": null,
          "createdAt": 1471440036000,
          "updatedAt": 1473067186000,
          "skuId": 5830,
          "quantity": 11,
          "orderId": 412,
          "outId": null,
          "buyerName": "英俊",
          "outBuyerId": null,
          "itemId": 5039,
          "itemName": "items072101-spu0712008",
          "skuImage": "http://terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/12/37c77a94-1956-43fa-8e98-612f6f736a10.jpg",
          "shopName": "锁妖塔",
          "outShopId": null,
          "companyId": null,
          "outSkuId": null,
          "skuAttributes": "[{\"attrKey\":\"关键必填销售自定义文本\",\"attrVal\":\"文本2\"},{\"attrKey\":\"关键必填销售自定义数字\",\"attrVal\":\"9.99\"}]",
          "skuAttrs": [
            {
              "attrKey": "关键必填销售自定义文本",
              "attrVal": "文本2",
              "unit": null,
              "showImage": null,
              "thumbnail": null,
              "image": null
            },
            {
              "attrKey": "关键必填销售自定义数字",
              "attrVal": "9.99",
              "unit": null,
              "showImage": null,
              "thumbnail": null,
              "image": null
            }
          ],
          "channel": 1,
          "payType": 1,
          "shipmentType": 1,
          "originFee": 440000,
          "discount": 8000,
          "shipFee": null,
          "shipFeeDiscount": null,
          "integral": null,
          "balance": null,
          "itemSnapshotId": 131,
          "hasRefund": false,
          "invoiced": false,
          "commented": 1,
          "hasApplyAfterSale": null,
          "commissionRate": 0
        }
      ]
    }

    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询子订单失败"
     }
    ```


## 查询订单的快递信息

* 请求url
```
GET order.express.track
```

* 请求参数示例
```
orderId=412&orderType=1
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| orderId | 整形 | 订单id | 是 |  |
| orderType | 整形 | 订单类型 | 否 | 1表示店铺订单,2表示子订单,默认为1 |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": [
        {
          "officialQueryUrl": null,
          "steps": [
            {
              "time": "2016-05-31 20:08:12",
              "desc": "由南汇临港 发往 浦东分拨中心",
              "extra": null
            },
            {
              "time": "2016-05-31 20:11:41",
              "desc": "南汇临港 的收件员 桌游已收件",
              "extra": null
            },
            {
              "time": "2016-05-31 21:47:51",
              "desc": "快件已到达浦东分拨中心 扫描员是 张坤 上一站是南汇临港",
              "extra": null
            },
            {
              "time": "2016-05-31 22:01:53",
              "desc": "由浦东分拨中心 发往 杭州分拨中心",
              "extra": null
            },
            {
              "time": "2016-06-01 03:04:59",
              "desc": "快件已到达杭州分拨中心 扫描员是 卸货李兴贤 上一站是浦东分拨中心",
              "extra": null
            },
            {
              "time": "2016-06-01 05:03:28",
              "desc": "由杭州分拨中心 发往 杭州新滨江",
              "extra": null
            },
            {
              "time": "2016-06-01 08:03:38",
              "desc": "快件已到达杭州新滨江 扫描员是 沈振元 上一站是杭州分拨中心",
              "extra": null
            },
            {
              "time": "2016-06-01 08:55:13",
              "desc": "杭州新滨江 的派件员 胡健 正在派件",
              "extra": null
            },
            {
              "time": "2016-06-01 16:48:46",
              "desc": "已签收,签收人是图片签收",
              "extra": null
            }
          ],
          "shipmentId": 78,
          "shipmentSerialNo": "2806296778",
          "shipmentCorpName": "国通快递"
        }
      ]
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| shipmentId | 整形 | 发货单id |  |
| shipmentSerialNo | 字符串 | 快递单号 |  |
| shipmentCorpName | 字符串 | 快递公司 |  |
| officialQueryUrl | 字符串 | 快递官方查询url | 有些物流服务(如快递100免费版)对于部分快递公司物流查询不提供api,只能到其网站查询 |
| steps | 数组 | 快递跟踪信息 |  |


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询快递信息失败"
     }
    ```


## 买家取消订单

* 请求url
```
POST order.buyer.cancel
```

* 请求参数示例
```
orderId=412&orderType=1
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| orderId | 整形 | 订单id | 是 |  |
| orderType | 整形 | 订单类型 | 否 | 1表示店铺订单,2表示子订单,默认为1 |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": true
    }
    ```

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "取消订单失败"
     }
    ```


## 买家确认收货

* 请求url
```
POST order.buyer.confirm
```

* 请求参数示例
```
orderId=412&orderType=1
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| orderId | 整形 | 订单id | 是 |  |
| orderType | 整形 | 订单类型 | 否 | 1表示店铺订单,2表示子订单,默认为1 |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": true
    }
    ```

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "确认收货失败"
     }
    ```



## 买家订单评价

* 请求url
```
POST order.comments.buyer.create
```

* 请求参数示例
```
comments=
[
  {
    "skuOrderId": 564,
    "quality": 5,
    "describe": 5,
    "service": 5,
    "express": 5,
    "context": "是真品",
    "images": [
      {
        "url": "http://terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/22/988c90f9-af14-4b11-bfd9-6437365b0353.jpg"
      },
      {
        "url": "http://terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/22/988c90f9-af14-4b11-bfd9-643736670353.jpg"
      }
    ]
  },
  {
      "skuOrderId": 565,
      "quality": 5,
      "describe": 5,
      "service": 5,
      "express": 5,
      "context": "物流给力",
      "images": [
        {
          "url": "http://terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/22/988c90f9-af14-4b11-bfd9-6487365b0373.jpg"
        },
        {
          "url": "http://terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/22/988c90f9-af14-4b11-bfd9-643736170359.jpg"
        }
      ]
    }
]
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| skuOrderId | 整形 | 子订单id | 是 |  |
| quality | 整形 | 质量评分 | 是 |  |
| describe | 整形 | 描述评分 | 是 |  |
| service | 整形 | 服务评分 | 是 |  |
| express | 整形 | 物流评分 | 是 |  |
| context | 字符串 | 评论内容 | 是 |  |
| images | 数组 | 晒单图片列表 | 否 |  |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": [197,198]
    }
    ```

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "创建评论失败"
     }
    ```