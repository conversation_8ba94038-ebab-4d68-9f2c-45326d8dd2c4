package moonstone.wxa.service;

import io.terminus.common.model.Response;
import moonstone.wxa.model.WxaComponent;

import java.util.List;

/**
 * Created by CaiZhy on 2018/11/12.
 */
public interface WxaComponentReadService {
    Response<WxaComponent> findById(Long id);

    Response<List<WxaComponent>> findByTemplateId(Long templateId);

    Response<WxaComponent> findByTemplateIdAndCode(Long templateId, String code);
}
