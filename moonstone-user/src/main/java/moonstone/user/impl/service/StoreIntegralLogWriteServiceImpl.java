package moonstone.user.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.user.impl.dao.StoreIntegralLogDao;
import moonstone.user.model.StoreIntegralLog;
import moonstone.user.service.StoreIntegralLogWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/27 18:56
 */
@Service
@Slf4j
public class StoreIntegralLogWriteServiceImpl implements StoreIntegralLogWriteService {
    @Autowired
    StoreIntegralLogDao storeIntegralLogDao;

    @Override
    public Either<Boolean> create(StoreIntegralLog storeIntegralLog) {
        try {
            return Either.ok(storeIntegralLogDao.create(storeIntegralLog));
        } catch (Exception ex) {
            log.error("{} storeProxy:{}", LogUtil.getClassMethodName(), storeIntegralLog);
            return Either.error(ex, "fail.create.storeProxy");
        }
    }
}
