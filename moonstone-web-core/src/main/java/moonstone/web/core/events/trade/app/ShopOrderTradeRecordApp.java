package moonstone.web.core.events.trade.app;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.order.model.ShopOrder;
import moonstone.shop.model.Shop;
import moonstone.user.model.StoreProxy;
import moonstone.web.core.component.RecordManager;
import moonstone.web.core.model.dto.record.TradeAmountToday;
import moonstone.web.core.model.dto.record.TradeTimeToday;
import moonstone.web.core.user.StoreProxyManager;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * 用于记录店铺的订单利润与交易额度
 *
 * @see RecordManager 记录管理器
 */
@AllArgsConstructor
@Slf4j
@Component
public class ShopOrderTradeRecordApp {
    StoreProxyManager storeProxyManager;
    WeShopCacheHolder weShopCacheHolder;
    RecordManager recordManager;


    /**
     * 封装一下 增加利润
     *
     * @param shopUserId       店铺用户Id
     * @param fee              订单金额
     * @param outFrom          订单类型
     * @param shopIdOrWeShopId 店铺Id
     */
    private void increaseRecord(Long shopUserId, Long fee, OrderOutFrom outFrom, Long shopIdOrWeShopId) {
        if (fee > 0) {
            recordManager.increaseRecord(shopUserId, TradeAmountToday.build(shopUserId, outFrom, shopIdOrWeShopId).num(fee));
            recordManager.increaseRecord(shopUserId, TradeTimeToday.build(shopUserId, outFrom, shopIdOrWeShopId));
        } else if (fee != 0) {
            recordManager.increaseRecord(shopUserId, TradeAmountToday.build(shopUserId, outFrom, shopIdOrWeShopId).num(fee));
            recordManager.increaseRecord(shopUserId, TradeTimeToday.build(shopUserId, outFrom, shopIdOrWeShopId).num(-1));
        }
    }

    /**
     * 记录订单的今日交易额度
     *
     * @param shopOrder 订单
     * @param affect    是否为今天的记录
     * @param refund    是否为退款
     * @param shop      店铺
     * @param outFrom   订单来源
     */
    public void recordShopOrderTrade(ShopOrder shopOrder, boolean refund, boolean affect, Shop shop, OrderOutFrom outFrom) {
        long fee = 0;
        // 如果是今天的记录, 则退款会影响额度
        if (affect) {
            fee = refund ? -shopOrder.getFee() : shopOrder.getFee();
        }
        try {
            switch (outFrom) {
                case LEVEL_Distribution: {
                    if (Objects.isNull(shopOrder.getReferenceId())) {
                        break;
                    }
                    Optional<StoreProxy> storeProxy = storeProxyManager.getStoreProxyByShopIdAndUserId(shopOrder.getId(), shopOrder.getReferenceId());
                    if (storeProxy.isPresent()) {
                        increaseRecord(storeProxy.get().getUserId(), fee, outFrom, storeProxy.get().getShopId());
                        break;
                    }
                    break;
                }
                case WE_SHOP: {
                    if (!ObjectUtils.isEmpty(shopOrder.getOutShopId())) {
                        long orderFee = fee;
                        weShopCacheHolder.findByWeShopId(Long.parseLong(shopOrder.getOutShopId()))
                                .ifPresent(weShop -> increaseRecord(weShop.getUserId(), orderFee, outFrom, weShop.getId()));
                    }
                    break;
                }
                default:
                    increaseRecord(shop.getUserId(), fee, outFrom, shop.getId());
            }
        } catch (Exception ex) {
            log.error("{} fail to record order[{}] trade", LogUtil.getClassMethodName(), shopOrder.getId(), ex);
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("order-trade-amount-record", new Translate("订单[%s]记录交易失败", shopOrder.getId()).toString(), ex, EmailReceiverGroup.DEVELOPER));
        }
    }
}
