package moonstone.promotion.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 营销跟踪,用于记录某些营销(如优惠券)的领取和使用情况
 * Author:cp
 * Created on 06/25/16
 */
@Data
public class PromotionTrack implements Serializable {

    private static final long serialVersionUID = -5701382779088027586L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 营销id
     */
    private Long promotionId;

    /**
     * 已领取数量
     */
    private int receivedQuantity;

    /**
     * 已使用数量
     */
    private int usedQuantity;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;
}
