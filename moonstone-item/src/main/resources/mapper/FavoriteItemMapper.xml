<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!-- ~ Copyright (c) 2016 杭州端点网络科技有限公司 -->

<mapper namespace="FavoriteItem">
    <resultMap id="FavoriteItemMap" type="FavoriteItem">
        <id column="id" property="id"/>
        <result column="buyer_id" property="buyerId"/>
        <result column="shop_id" property="shopId"/>
        <result column="item_id" property="itemId"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">parana_favorite_items</sql>

    <sql id="cols_all">
        id,<include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        buyer_id,shop_id,item_id,created_at,updated_at
    </sql>

    <sql id="vals">
        #{buyerId},#{shopId},#{itemId},now(),now()
    </sql>

    <sql id="criteria">
        <if test="id != null">id = #{id}</if>
        <if test="buyerId != null">and buyer_id = #{buyerId}</if>
        <if test="shopId != null">and shop_id = #{shopId}</if>
        <if test="itemId != null">and item_id = #{itemId}</if>
    </sql>

    <insert id="create" parameterType="FavoriteItem" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO<include refid="tb"/>(<include refid="cols_exclude_id"/>) VALUES(<include refid="vals"/>)
    </insert>

    <delete id="delete" parameterType="long">
        delete from <include refid="tb"/>
        where id = #{id}
    </delete>

    <select id="findById" parameterType="long" resultMap="FavoriteItemMap">
        select id,<include refid="cols_exclude_id"/>
        from <include refid="tb"/>
        where id = #{id}
    </select>

    <select id="findByIds" parameterType="list" resultMap="FavoriteItemMap">
        select id,<include refid="cols_exclude_id"/>
        from <include refid="tb"/>
        where id in
        <foreach collection="list" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="findByBuyerIdAndItemId" parameterType="map" resultMap="FavoriteItemMap">
        select id,<include refid="cols_exclude_id"/>
        from <include refid="tb"/>
        where buyer_id=#{buyerId} and item_id=#{itemId} limit 1
    </select>

    <select id="deleteByBuyerIdAndItemId" parameterType="map" resultType="long">
        delete from <include refid="tb"/>
        where buyer_id=#{buyerId} and item_id=#{itemId}
    </select>

    <select id="count" parameterType="map" resultType="long">
        select count(1) from <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="FavoriteItemMap">
        select id,<include refid="cols_exclude_id"/>
        from <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        order by id desc
        LIMIT #{offset}, #{limit}
    </select>
</mapper>
