package moonstone.web.front.item;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.model.CommonUser;
import moonstone.common.model.PagingCriteria;
import moonstone.common.utils.APIRespWrapper;
import moonstone.common.utils.UserUtil;
import moonstone.search.dto.IndexedWeShopItem;
import moonstone.search.dto.SearchedWeShopItem;
import moonstone.user.ext.UserTypeBean;
import moonstone.web.core.component.item.model.PriceModifyDTO;
import moonstone.web.core.component.item.model.WeShopItemPreview;
import moonstone.web.core.component.user.UserSubShopPackComponent;
import moonstone.web.core.component.weShop.WeShopItemPriceModifyManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <AUTHOR>
 * ##负 责选择商品 [上架] [改价]
 * 使用vertx进行集群选择负载机操作manager,manager内如有事务要求则必须手动处理好事务关系,不可轻易分布式事务
 * <p>
 * 操作如下
 * - [GET] 显示可选择商品
 * - [GET] 预览改价结果
 * > 是否要使用webSocket技术?
 * - [POST] 上架商品且设定价格
 * * 如果已经存在则变为修改价格
 * - [PUT] 修改改价结果
 * - [PUT] 修改商品状态
 */
@RestController
@RequestMapping("/api/weShopItem")
@Slf4j
public class WeShopItemController {
    @Autowired
    private WeShopItemPriceModifyManager weShopItemPriceModifyManager;
    @Autowired
    private UserSubShopPackComponent userSubShopPackComponent;
    @Autowired
    private UserTypeBean userTypeBean;

    @GetMapping("/countListItem")
    public APIResp<Long> countListItem(@RequestParam(required = false) Long shopId) {
        CommonUser operator = UserUtil.getCurrentUser();
        userSubShopPackComponent.wrap(operator, shopId);
        if (Objects.isNull(operator) || !userTypeBean.isWeDistributor(operator)) {
            return APIResp.notLogin();
        }
        userSubShopPackComponent.wrap(operator, shopId);
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.countOwnedListItem(operator.getWeShopId()));
    }

    @PostMapping("/deleteItemThatInvalidate")
    public APIResp<Boolean> deleteInvalidateItem(@RequestParam(required = false) Long shopId) {
        CommonUser operator = UserUtil.getCurrentUser();
        if (Objects.isNull(operator) || !userTypeBean.isWeDistributor(operator)) {
            return APIResp.notLogin();
        }
        userSubShopPackComponent.wrap(operator, shopId);
        return weShopItemPriceModifyManager.deleteInvalidateItem(operator.getWeShopId());
    }

    @PostMapping("/selectItem")
    public APIResp<Boolean> selectItem(Long itemId, @RequestParam(defaultValue = "false") Boolean taxBear, @RequestParam(required = false) Long shopId) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.selectItem(shopId, itemId, taxBear));
    }

    @GetMapping("/previewForItem")
    public APIResp<WeShopItemPreview> previewForItem(@RequestParam(required = false) Long weShopItemId, Long itemId, Integer type, Long price, @RequestParam(defaultValue = "false") Boolean taxBear) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.previewPriceModifyForItem(weShopItemId, itemId, type, price, taxBear));
    }

    @PostMapping("/addItemIntoListForItem")
    public APIResp<Boolean> addItemIntoListForItem(Long itemId, Integer type, Long price, @RequestParam(defaultValue = "false") Boolean taxBear, @RequestParam(required = false) Long shopId) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.addItemIntoListForItem(shopId, itemId, type, price, taxBear));
    }

    @PutMapping("/modifyPriceForItem")
    public APIResp<Boolean> modifyPriceForItem(Long itemId, Integer type, Long price, @RequestParam(defaultValue = "false") Boolean taxBear, @RequestParam(required = false) Long shopId) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.modifyPriceForItem(shopId, itemId, type, price, taxBear));
    }

    @PostMapping("/previewModify")
    public APIResp<List<WeShopItemPreview>> previewList(@RequestBody Long[] weShopItemId) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.previewFor(Arrays.asList(weShopItemId)));
    }

    @GetMapping("/listItem")
    public APIResp<List<SearchedWeShopItem>> listItem(@RequestParam Map<String, String> params) {
        return APIRespWrapper.wrapPaging(weShopItemPriceModifyManager.listItem(params), Optional.ofNullable(params.get("pageNo")).map(Integer::parseInt).orElse(1), Optional.ofNullable(params.get("pageSize")).map(Integer::parseInt).orElse(100));
    }

    @GetMapping("/invalidateItem")
    public APIResp<List<IndexedWeShopItem>> listInvalidateItem(PagingCriteria criteria, @RequestParam(required = false) Long shopId) {
        return APIRespWrapper.wrapPaging(weShopItemPriceModifyManager.listInvalidateItem(shopId, criteria), criteria.getPageNo(), criteria.getPageSize());
    }

    @PutMapping("/previewPriceModify")
    public APIResp<List<WeShopItemPreview>> previewPriceModifyBatch(@RequestBody List<PriceModifyDTO> priceModifyList) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.previewPriceModifyBatch(priceModifyList));
    }

    @GetMapping("/previewPriceModify")
    public APIResp<List<WeShopItemPreview>> previewPriceModifyBatch(String modify) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.previewPriceModifyBatch(modify));
    }

    @PutMapping("/tryPriceBatch")
    public APIResp<List<WeShopItemPreview>> previewPriceModifyBatch(@RequestBody PriceModifyDTO[] priceModifyDTOS) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.previewPriceModifyBatch(priceModifyDTOS));
    }

    @RequestMapping("/tryPrice")
    public APIResp<List<WeShopItemPreview>> previewPriceModify(@RequestBody PriceModifyDTO modify) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.previewPriceModify(modify));
    }

    @PostMapping("/addItemIntoListBatch")
    public APIResp<Boolean> addWeShopItemWithPriceModifyBatch(@RequestBody List<PriceModifyDTO> priceModifyList, @RequestParam(required = false) Long shopId) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.addWeShopItemWithPriceModifyBatch(shopId, priceModifyList));
    }

    @PostMapping("/addItemIntoList")
    public APIResp<Boolean> addWeShopItemWithPriceModify(@RequestBody PriceModifyDTO modify, @RequestParam(required = false) Long shopId) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.addWeShopItemWithPriceModify(shopId, modify));
    }

    @PutMapping("/modifyItemPriceBatch")
    public APIResp<Boolean> modifyItemPriceBatch(@RequestBody List<PriceModifyDTO> priceModifyDTOList, @RequestParam(required = false) Long shopId) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.modifyItemPriceBatch(shopId, priceModifyDTOList));
    }

    @PutMapping("/modifyItemPrice")
    public APIResp<Boolean> modifyItemPrice(@RequestBody PriceModifyDTO modify, @RequestParam(required = false) Long shopId) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.modifyItemPrice(shopId, modify));
    }

    @DeleteMapping("/remove")
    public APIResp<Boolean> deleteWeShopItem(Long[] weShopItemIds, @RequestBody WeShopItemPriceModifyManager.DeleteListDTO list, @RequestParam(required = false) Long shopId) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.deleteWeShopItem(shopId, weShopItemIds, list));
    }

    @PutMapping("/switchStatus")
    public APIResp<Boolean> switchStatus(Long[] weShopItemIds, boolean onSell, @RequestParam(required = false) Long shopId) {
        return APIRespWrapper.wrap(weShopItemPriceModifyManager.switchStatus(shopId, weShopItemIds, onSell));
    }

    @PostMapping("/share")
    public void share(Long weShopItemId) {
        weShopItemPriceModifyManager.share(weShopItemId);
    }

    @GetMapping("/shareImage")
    public void renderShareImage(@RequestParam(required = false) Long itemId, @RequestParam(required = false) Long weShopItemId, @RequestParam(required = false) Long weShopId, @RequestParam(required = false) Long projectId, HttpServletResponse response) {
        weShopItemPriceModifyManager.renderShareImage(itemId, weShopItemId, weShopId, projectId, response);
    }
}
