package moonstone.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.countryImage.model.CountryImage;
import moonstone.countryImage.service.CountryImageReadService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class OriginCacher {


    private LoadingCache<Long, CountryImage> originCache;

    @RpcConsumer
    private CountryImageReadService countryImageReadService;

    @Value("${cache.duration.in.minutes: 60}")
    private Integer duration;

    @PostConstruct
    public void init() {
        this.originCache = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build(countryId -> {
                    Response<CountryImage> countryImage = countryImageReadService.findByCountryId(countryId);
                    if (!countryImage.isSuccess()) {
                        log.error("failed to find countryImageReadService(id={}), error code:{}",
                                countryId, countryImage.getError());
//                        throw new ServiceException("find countryImage fail,code: "+countryImage.getError());
                        return new CountryImage();
                    }
                    return countryImage.getResult();
                });
    }

    /**
     * 根据国家id查找模板
     *
     * @param countryId 国家id
     * @return 对应的品牌
     */
    public CountryImage findOriginById(Long countryId) {
        return originCache.get(countryId);
    }

}
