/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.component.attribute;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.category.model.CategoryAttribute;
import moonstone.category.service.CategoryAttributeReadService;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-03-07
 */
@Component
@Slf4j
public class CategoryAttributeNoCacher {

    @RpcConsumer
    private CategoryAttributeReadService categoryAttributeReadService;

    /**
     * 根据类目id查找对应的属性
     *
     * @param categoryId  类目id
     * @return 类目属性列表
     */
    public List<CategoryAttribute> findCategoryAttributeByCategoryId(Long categoryId){

        Response<List<CategoryAttribute>> r =
                this.categoryAttributeReadService.findByCategoryId(categoryId);

        if(!r.isSuccess()){
            log.error("failed to find category attributes by category(id={}), error code:{}",
                    categoryId, r.getError());
            throw new ServiceException(r.getError());
        }

        return r.getResult();
    }
}
