package moonstone.shop.enums;

public enum WxCertFileStatusEnum {
    NOT_UPLOAD(0, "未上传"),
    UPLOADED(1, "已上传"),
    ;

    private final Integer code;
    private final String description;

    WxCertFileStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static String parseDescription(Integer wxCertFileStatus) {
        for (var current : values()) {
            if (current.getCode().equals(wxCertFileStatus)) {
                return current.getDescription();
            }
        }

        return null;
    }
}
