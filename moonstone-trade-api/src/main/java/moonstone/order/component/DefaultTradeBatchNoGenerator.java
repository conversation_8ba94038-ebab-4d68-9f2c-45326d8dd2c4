package moonstone.order.component;

import com.google.common.base.Strings;
import moonstone.order.api.TradeBatchNoGenerator;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.Date;

/**
 * 交易批次号生成器默认实现
 * Created by cp on 6/11/17.
 */
public class DefaultTradeBatchNoGenerator implements TradeBatchNoGenerator {

    private static final DateTimeFormatter DFT_BATCH = DateTimeFormat.forPattern("yyyyMMddHHmmss");

    /**
     * 生成规则: 时间戳(14位)+ tradeId(18位, 不足由0填充)
     */
    @Override
    public String generateBatchNo(Date happenedAt, Long tradeId) {
        String prefix = DFT_BATCH.print(new DateTime(happenedAt));
        String suffix = tradeId.toString();
        Integer trackLength = suffix.length();
        String padding = Strings.repeat("0", 18 - trackLength);
        return prefix + padding + suffix;
    }

    @Override
    public String generateBatchNo(Date happenedAt, Long tradeId, int suffixLength) {
        String prefix = DFT_BATCH.print(new DateTime(happenedAt));

        String suffix = tradeId.toString();
        Integer trackLength = suffix.length();

        String padding = Strings.repeat("0", suffixLength - trackLength);

        return prefix + padding + suffix;
    }
}
