package moonstone.web.admin.order.app;

import io.terminus.common.model.BaseUser;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.*;
import moonstone.order.dto.ReturnInfo;
import moonstone.order.dto.fsm.*;
import moonstone.order.enu.RefundReasonType;
import moonstone.order.model.Refund;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.OrderWriteService;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.RefundWriteService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.user.ext.UserTypeBean;
import moonstone.web.core.component.AppStartUUID;
import moonstone.web.core.refund.application.RefundForBuyerApplication;
import moonstone.web.core.refund.application.RefundForSellerApplication;
import moonstone.web.core.refund.model.RefundApplyRequest;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Slf4j
@RestController
@RequestMapping("/api/refund/order")
public record AdminRefundController(AppStartUUID appStartUUID,
                                    OrderWriteService orderWriteService,
                                    ShopOrderReadService shopOrderReadService,
                                    RefundWriteService refundWriteService,
                                    RefundForSellerApplication refundForSellerApplication,
                                    RefundForBuyerApplication refundForBuyerApplication,
                                    RefundReadService refundReadService,
                                    UserTypeBean userTypeBean) {
    /**
     * apply refund for seller
     *
     * @param refundApplyRequest request that contain the refund request information[ OrderId, Type, Reason, Fee]
     * @return refundId or error msg
     */
    @PostMapping
    public APIResp<Long> applyRefund(@RequestBody RefundApplyRequest refundApplyRequest, @RequestHeader(required = false) String password) {
        try {
            BaseUser applier = UserUtil.requireLoginUser();
            if (!Objects.equals(password, appStartUUID.password()) && !userTypeBean.isAdmin(applier)) {
                return APIResp.error(Translate.of("请登录"));
            }
            return APIRespWrapper.wrap(refundForBuyerApplication.apply(applier,
                    refundApplyRequest.getRefundOrderList(),
                    refundApplyRequest.getReason(),
                    refundApplyRequest.getImageUrlList(),
                    Refund.RefundType.from(refundApplyRequest.getRefundType()),
                    RefundReasonType.from(refundApplyRequest.getReasonType()),
                    refundApplyRequest.getFee()));
        } catch (Exception e) {
            log.error("{} fail to apply refund for [{}] for user[{}]", LogUtil.getClassMethodName(), Json.toJson(refundApplyRequest), UserUtil.getUserId(), e);
            return APIRespWrapper.error(e.getMessage());
        }
    }

    @PostMapping("/{refundId}/agree")
    public APIResp<Boolean> agreeRefund(@PathVariable("refundId") Long refundId, @RequestBody(required = false) ReturnInfo returnInfo, @RequestHeader(required = false) String password) {
        try {
            CommonUser operator = UserUtil.requireLoginUser();
            if (!Objects.equals(password, appStartUUID.password()) && !userTypeBean.isAdmin(operator)) {
                return APIResp.error(Translate.of("请登录"));
            }
            return APIRespWrapper.wrap(refundForSellerApplication.acceptRefund(refundId, returnInfo, operator));
        } catch (Exception e) {
            log.error("{} fail to agree refund[Id => {}] for operator[{}]", LogUtil.getClassMethodName()
                    , refundId, UserUtil.getUserId(), e);
            return APIRespWrapper.error(e.getMessage());
        }
    }


    @PostMapping("/{refundId}/reject")
    public APIResp<Boolean> rejectRefund(@PathVariable("refundId") Long refundId, @RequestBody String reason, @RequestHeader(required = false) String password) {
        try {
            CommonUser operator = UserUtil.requireLoginUser();
            if (!Objects.equals(password, appStartUUID.password()) && !userTypeBean.isAdmin(operator)) {
                return APIResp.error(Translate.of("请登录"));
            }
            return APIRespWrapper.wrap(refundForSellerApplication.rejectRefund(refundId, operator, reason));
        } catch (Exception e) {
            log.error("{} fail to agree refund[Id => {}] for operator[{}]", LogUtil.getClassMethodName()
                    , refundId, UserUtil.getUserId(), e);
            return APIRespWrapper.error(e.getMessage());
        }
    }


    @PostMapping("/{refundId}/refund")
    public APIResp<Boolean> refund(@PathVariable("refundId") Long refundId, @RequestHeader(required = false) String password) {
        try {
            CommonUser operator = UserUtil.requireLoginUser();
            if (!Objects.equals(password, appStartUUID.password()) && !userTypeBean.isAdmin(operator)) {
                return APIResp.error(Translate.of("请登录"));
            }
            return APIRespWrapper.wrap(refundForSellerApplication.refund(refundId, operator));
        } catch (Exception e) {
            log.error("{} fail to agree refund[Id => {}] for operator[{}]", LogUtil.getClassMethodName()
                    , refundId, UserUtil.getUserId(), e);
            return APIRespWrapper.error(e.getMessage());
        }
    }


    @PostMapping("/rollback")
    public void rollbackShipment(Long[] orderId, Integer currentStatus, @RequestParam(required = false) String password) {
        if (!password.equals(appStartUUID.password()) && !userTypeBean().isAdmin(UserUtil.getCurrentUser())) {
            return;
        }
        for (Long order : orderId) {
            ShopOrder shopOrder = shopOrderReadService.findById(order).getResult();
            // boolean paid = Stream.of(OrderStatus.NOT_PAID, OrderStatus.REFUND, OrderStatus.RETURN, OrderStatus.PAYMENT_CLOSED_COMPLETE_REFUND, OrderStatus.PAYMENT_CLOSED_COMPLETE_REFUND, OrderStatus.BUYER_CANCEL, OrderStatus.SELLER_CANCEL, OrderStatus.TIMEOUT_CANCEL,
            //         OrderStatus.DELETED, OrderStatus.REFUND_PROCESSING)
            //         .map(OrderStatus::getValue).noneMatch(shopOrder.getStatus()::equals)
            boolean notNormal = shopOrder.getStatus() <= 0;
            if (notNormal) {
                log.info("{} Order[{}] NOT NORMAL[status => {}]", LogUtil.getClassMethodName(), order, shopOrder.getStatus());
                continue;
            }
            boolean updateRes = !orderWriteService.batchShopOrderStatusChanged(Collections.singletonList(order), shopOrder.getStatus(), OrderStatus.PAID.getValue()).getResult().isEmpty();
            log.info("{} Order[{}] Status[{}] Rollback Into PAID [Res => {}]", LogUtil.getClassMethodName(), order, shopOrder.getStatus(), updateRes);
        }
    }

    @PostMapping("/forceAgree")
    public void forceAgreeRefund(Long[] orderId, @RequestParam(required = false) String password) {
        if (!password.equals(appStartUUID.password()) && !userTypeBean.isAdmin(UserUtil.getCurrentUser())) {
            return;
        }
        Flow flow = FlowBook.REFUND_FLOW.getFlow();
        OrderOperation agree = OrderEvent.REFUND_APPLY_AGREE.toOrderOperation();
        Map<Refund, List<ShopOrder>> refundMapOrder = new HashMap<>();
        Set<Long> refundIdSet = new HashSet<>();
        for (Long order : orderId) {
            for (Refund refund : refundReadService.findByShopOrderIds(Collections.singletonList(order)).getResult()) {
                if (!refundIdSet.contains(refund.getId())) {
                    refundMapOrder.put(refund, new ArrayList<>());
                    refundIdSet.add(refund.getId());
                }
                refundMapOrder.get(refund).add(shopOrderReadService.findById(order).getResult());
            }
        }

        for (Map.Entry<Refund, List<ShopOrder>> entry : refundMapOrder.entrySet()) {
            Refund refund = entry.getKey();
            if (flow.operationAllowed(refund.getStatus(), agree)) {
                Integer agreedStatus = flow.target(refund.getStatus(), agree);
                Boolean refundUpdateRes = refundWriteService.updateStatus(refund.getId(), agreedStatus).getResult();
                for (ShopOrder order : entry.getValue()) {
                    try {
                        Boolean orderUpdateRes = !orderWriteService.batchShopOrderStatusChanged(Collections.singletonList(order.getId()), refund.getStatus(), agreedStatus).getResult().isEmpty();
                        log.info("{} forceAgree Order[{}] Refund[{}] Status[{}] into Agree[{}], Res[Refund => {}, Order => {}]", LogUtil.getClassMethodName(),
                                order, refund.getId(), refund.getStatus(), flow.target(refund.getStatus(), agree), refundUpdateRes, orderUpdateRes);
                    } catch (Exception e) {
                        log.error("{} fail to update order status with refund[{}] order[{}]", LogUtil.getClassMethodName(), refund.getId(), order.getId(), e);
                    }
                }
            } else {
                log.warn("{} forceAgree skip Refund[{}]", LogUtil.getClassMethodName(), refund.getId());
            }
        }
    }
}
