package moonstone.web.front.order.web;

import moonstone.web.front.order.service.impl.OrderProfitConfirmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class OrderProfitConfirmAfterShipWeb {
    @Autowired
    OrderProfitConfirmService orderProfitConfirmService;

    @PostMapping("/api/profit/retrigger-confirmed")
    public boolean confirmed(Long orderId) {
        return orderProfitConfirmService.tryConfirm(orderId);
    }

}
