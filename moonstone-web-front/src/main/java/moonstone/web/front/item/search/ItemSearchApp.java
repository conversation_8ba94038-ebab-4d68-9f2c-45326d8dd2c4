package moonstone.web.front.item.search;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.model.AppDataPaging;
import moonstone.common.model.AppPagination;
import moonstone.common.utils.AppResponse;
import moonstone.common.utils.EmptyUtils;
import moonstone.search.dto.AggNav;
import moonstone.search.dto.SearchedItem;
import moonstone.search.dto.SearchedItemWithAggs;
import moonstone.search.item.ItemSearchReadService;
import moonstone.shop.model.Shop;
import moonstone.web.front.item.search.vo.AppCategoryVO;
import moonstone.web.front.item.search.vo.AppItemsSearchVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@ConditionalOnProperty(value = "enable.item.search", havingValue = "true", matchIfMissing = true)
@RestController
@Slf4j
public class ItemSearchApp {

    @RpcConsumer
    private ItemSearchReadService itemSearchReadService;

    @Autowired
    private ShopCacheHolder shopCacheHolder;

    /**
     * APP搜索全局商品
     */
    @RequestMapping(value = "/api/app/v1/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public Object searchItemAllShopItem(@RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                        @RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                        @RequestParam Map<String, String> params) {

        List<Shop> shopList = shopCacheHolder.findByIsSupply(1);
        if (EmptyUtils.isEmpty(shopList) || !shopList.stream().filter(entity -> Objects.equals(1, entity.getIsSupply())).findFirst().isPresent()) {
            return AppResponse.ok();
        }

        params.put("shopIds", shopList.stream().filter(entity -> Objects.equals(1, entity.getIsSupply())).
                map(Shop::getId).map(String::valueOf).collect(Collectors.joining("_")));
        //TODO 检查店铺是否存在和是否冻结?
        String templateName = "search.mustache";
//        Response<? extends SearchedItemInShopWithAggs<SearchedItem>> stm = itemSearchReadService.searchInShopWithAggs(pageNo, pageSize, templateName, params, SearchedItem.class);
        Response<? extends SearchedItemWithAggs<SearchedItem>> stm = itemSearchReadService.searchWithAggs(pageNo, pageSize, templateName, params, SearchedItem.class);
        if (!stm.isSuccess() || stm.getResult().getEntities() == null || stm.getResult().getEntities().getData().isEmpty()) {
            return AppResponse.ok();
        }
        List<AppItemsSearchVO> appItemsSearchVOS = new ArrayList<>();
        for (SearchedItem sim : stm.getResult().getEntities().getData()) {
            AppItemsSearchVO appItemsSearchVO = new AppItemsSearchVO();
            appItemsSearchVO.setItemId(sim.getId());
            BeanUtils.copyProperties(sim, appItemsSearchVO);
            appItemsSearchVO.setName(replaceItemName(appItemsSearchVO.getName()));
            appItemsSearchVOS.add(appItemsSearchVO);
        }
        AppPagination appPagination = new AppPagination(stm.getResult().getEntities().getTotal().intValue(), pageSize, pageNo);
        AppDataPaging appPaging = AppDataPaging.build();
        appPaging.setList(appItemsSearchVOS);
        appPaging.setPagination(appPagination);
        return AppResponse.ok(appPaging);
    }

    /**
     * APP内按类目分类
     */
    @RequestMapping(value = "/api/app/v1/search-in-category", produces = MediaType.APPLICATION_JSON_VALUE)
    public Object searchItemAllShop(@RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                    @RequestParam(required = false, defaultValue = "20") Integer pageSize,
                                    @RequestParam Map<String, String> params) {
        if (EmptyUtils.isEmpty(params) || EmptyUtils.isEmpty(params.get("bcids"))) {
            return AppResponse.error("类目不能为空");
        }

        List<Shop> shopList = shopCacheHolder.findByIsSupply(1);
        if (EmptyUtils.isEmpty(shopList) || !shopList.stream().filter(entity -> Objects.equals(1, entity.getIsSupply())).findFirst().isPresent()) {
            return AppResponse.ok();
        }

        params.put("shopIds", shopList.stream().filter(entity -> Objects.equals(1, entity.getIsSupply())).
                map(Shop::getId).map(String::valueOf).collect(Collectors.joining("_")));
        //TODO 检查店铺是否存在和是否冻结?
        String templateName = "search.mustache";
//        Response<? extends SearchedItemInShopWithAggs<SearchedItem>> stm = itemSearchReadService.searchInShopWithAggs(pageNo, pageSize, templateName, params, SearchedItem.class);
        Response<? extends SearchedItemWithAggs<SearchedItem>> stm = itemSearchReadService.searchWithAggs(pageNo, pageSize, templateName, params, SearchedItem.class);

        if (!stm.isSuccess() || stm.getResult().getEntities() == null || stm.getResult().getEntities().getData().isEmpty()) {
            return AppResponse.ok();
        }
        List<AppItemsSearchVO> appItemsSearchVOS = new ArrayList<>();
        for (SearchedItem sim : stm.getResult().getEntities().getData()) {
            AppItemsSearchVO appItemsSearchVO = new AppItemsSearchVO();
            appItemsSearchVO.setItemId(sim.getId());
            BeanUtils.copyProperties(sim, appItemsSearchVO);
            appItemsSearchVO.setName(replaceItemName(appItemsSearchVO.getName()));
            appItemsSearchVOS.add(appItemsSearchVO);
        }
        AppPagination appPagination = new AppPagination(stm.getResult().getEntities().getTotal().intValue(), pageSize, pageNo);
        AppDataPaging appPaging = AppDataPaging.build();
        appPaging.setList(appItemsSearchVOS);
        appPaging.setPagination(appPagination);
        return AppResponse.ok(appPaging);
    }

    /**
     * APP获取分类列表
     */
    @RequestMapping(value = "/api/app/v1/category", produces = MediaType.APPLICATION_JSON_VALUE)
    public Object searchItemAllShopWithAllCategory() {
        //将供销的店铺id加入商品
        Map<String, String> params = new HashMap<>();

        List<Shop> shopList = shopCacheHolder.findByIsSupply(1);
        if (EmptyUtils.isEmpty(shopList) || !shopList.stream().filter(entity -> Objects.equals(1, entity.getIsSupply())).findFirst().isPresent()) {
            return AppResponse.ok();
        }

        params.put("shopIds", shopList.stream().filter(entity -> Objects.equals(1, entity.getIsSupply())).
                map(Shop::getId).map(String::valueOf).collect(Collectors.joining("_")));

        //TODO 检查店铺是否存在和是否冻结?
        String templateName = "search.mustache";
        Response<? extends SearchedItemWithAggs<SearchedItem>> stm = itemSearchReadService.searchWithAggs(1, 100, templateName, params, SearchedItem.class);

        if (!stm.isSuccess() || stm.getResult().getEntities() == null || stm.getResult().getEntities().getData().isEmpty()) {
            return AppResponse.ok();
        }
        if (stm.getResult().getBackCategories() == null || stm.getResult().getBackCategories().isEmpty()
                || (stm.getResult().getBackCategories().size() == 1 && Objects.equals(stm.getResult().getBackCategories().get(0).getKey(), -1))) {
            return AppResponse.ok();
        }
        List<AggNav> aggNavs = stm.getResult().getBackCategories().stream().filter(sto -> !Objects.equals(sto.getKey() + "", "-1")).collect(Collectors.toList());
        if (EmptyUtils.isEmpty(aggNavs)) {
            return AppResponse.ok();
        }
        List<AppCategoryVO> idNameVOS = AppCategoryVO.build(aggNavs, "key", "name");
//        idNameVOS.stream().sorted((a, b) -> String.valueOf(a.getId() + "").compareTo(String.valueOf(b.getId() + ""))).collect(Collectors.toList());
        return AppResponse.ok(idNameVOS);
    }


    //过滤所有的搜索<em></em>
    private String replaceItemName(String name) {
        return name.replaceAll("<em>", "").replaceAll("</em>", "");
    }


}
