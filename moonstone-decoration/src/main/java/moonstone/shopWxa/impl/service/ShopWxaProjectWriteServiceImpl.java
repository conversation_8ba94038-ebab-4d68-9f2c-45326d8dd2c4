package moonstone.shopWxa.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.Translate;
import moonstone.shopWxa.impl.dao.ShopWxaProjectDao;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;

/**
 * Created by CaiZhy on 2018/11/12.
 */
@Slf4j
@Service
@RpcProvider
public class ShopWxaProjectWriteServiceImpl implements ShopWxaProjectWriteService {
    @Autowired
    private ShopWxaProjectDao shopWxaProjectDao;

    @Override
    public Response<Long> create(ShopWxaProject shopWxaProject) {
        try {
            if (shopWxaProject.getShopWxaId() != null) {
                ShopWxaProject exists = shopWxaProjectDao.findByShopWxaId(shopWxaProject.getShopWxaId()).stream()
                        .filter(valid -> valid.getStatus() != null)
                        .max(Comparator.comparingInt(ShopWxaProject::getStatus)).orElse(null);
                if (exists != null) {
                    if (exists.getStatus() != -1) {
                        return Response.fail(Translate.of("小程序已有对应工程"));
                    }
                    if (exists.getStatus() == -1) {
                        shopWxaProject.setId(exists.getId());
                        update(shopWxaProject);
                        return Response.ok(shopWxaProject.getId());
                    }
                }
            }
            shopWxaProjectDao.create(shopWxaProject);
            return Response.ok(shopWxaProject.getId());
        } catch (Exception e) {
            log.error("fail to create shopWxaProject( {}), cause: {}", shopWxaProject, Throwables.getStackTraceAsString(e));
            return Response.fail("shopWxaProject.create.fail");
        }
    }

    @Override
    public Response<Boolean> update(ShopWxaProject shopWxaProject){
        try {
            return Response.ok(shopWxaProjectDao.update(shopWxaProject));
        } catch (Exception e){
            log.error("fail to update shopWxaProject( {}), cause: {}", shopWxaProject, Throwables.getStackTraceAsString(e));
            return Response.fail("shopWxaProject.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateStatus(Long id, Integer status){
        try {
            return Response.ok(shopWxaProjectDao.updateStatus(id, status));
        } catch (Exception e){
            log.error("fail to update shopWxaProject status by id={}, status={}, cause: {}", id, status, Throwables.getStackTraceAsString(e));
            return Response.fail("shopWxaProject.status.update.fail");
        }
    }

    @Override
    @Transactional
    public Response<Boolean> updateStatusByShopWxaIdAndStatus(Long shopWxaId, Integer fromStatus, Integer toStatus){
        try {
            return Response.ok(shopWxaProjectDao.updateStatusByShopWxaIdAndStatus(shopWxaId, fromStatus, toStatus));
        } catch (Exception e){
            log.error("fail to update shopWxaProject status by shopWxaId={}, fromStatus={}, toStatus={}, cause: {}",
                    shopWxaId, fromStatus, toStatus, Throwables.getStackTraceAsString(e));
            return Response.fail("shopWxaProject.status.update.fail");
        }
    }

    @Override
    public Response<Boolean> delete(Long id){
        try {
            return Response.ok(shopWxaProjectDao.delete(id));
        } catch (Exception e){
            log.error("fail to delete shopWxaProject by id={}, cause: {}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("shopWxaProject.delete.fail");
        }
    }

    @Override
    public ShopWxaProject save(ShopWxaProject shopWxaProject) {
        shopWxaProjectDao.create(shopWxaProject);
        return shopWxaProject;
    }
}
