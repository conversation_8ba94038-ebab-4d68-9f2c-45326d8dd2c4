package moonstone.component.item.component;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import moonstone.component.dto.item.TaxAndOrigin;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

/**
 * 税费检查计算
 */
public interface TaxChecker {
    /**
     * 按sku原始价格计算税费
     *
     * @param sku      单品
     * @param quantity 数量
     * @return
     */
    Long getTax(Sku sku, Integer quantity);

    /**
     * 按sku微分销价格计算税费
     *
     * @param sku      单品
     * @param quantity 数量
     * @return
     */
    @Deprecated
    Long getDistributionTax(Sku sku, Integer quantity, Map<String, String> Extra);

    /**
     * 按折后金额计算税费
     *
     * @param sku              单品
     * @param afterDiscountFee 折扣后价格
     * @return
     */
    Long getRealTax(Sku sku, Long afterDiscountFee);

    TaxAndOrigin getRateAndOrigin(Long skuId, Integer pushSystem);

    /**
     * 拆税结果
     */
    @Getter
    @AllArgsConstructor
    class TaxSplitResult {
        /* 税金 */
        final BigDecimal tax;
        // 货物价格
        final BigDecimal fee;
    }

    /**
     * 拆税   新方式
     *
     * @param skuId      单品数据
     * @param outerSkuId 单品数据(依赖 outerSkuId)
     * @param price      价格 (真实价格,使用BigDecimal表示真实价格, 100.00 元就是100元)
     * @return 拆税结果
     */
    Optional<TaxSplitResult> splitTax(Long shopId, Long skuId, String outerSkuId, BigDecimal price);

    /**
     * 拆税新系统使用
     *
     * @param skuId      单品数据
     * @param outerSkuId 单品数据(映射的outerSkuId 是Y800的SkuCode)
     * @param price      价格
     * @return 拆税结果
     */
    Optional<TaxSplitResult> splitTaxNew(Long shopId, Long skuId, String outerSkuId, BigDecimal price);

    /**
     * 拆税   老方式,仅仅使用旧商品
     *
     * @param skuId      单品数据
     * @param outerSkuId 单品数据(依赖 outerSkuId)
     * @param price      价格 (真实价格,使用BigDecimal表示真实价格, 100.00 元就是100元)
     * @return 拆税结果
     */
    Optional<TaxSplitResult> splitTaxAtOms(Long skuId, String outerSkuId, BigDecimal price);

    @Data
    @NoArgsConstructor
    class TaxEntity {
        String skuSn;
        Long count;
        BigDecimal payAmount;
        BigDecimal goodsAmount;
        BigDecimal tax;
        BigDecimal discountAmount;
        Boolean split;
    }

    /**
     * 拆税逻辑
     * 将一个包含了税金的价格拆为一个货物价格和税金
     *
     * @param calTax 根据价格获取税金的函数
     * @param fee    货物价格
     * @return 如果可拆则返回一个TaxResult
     */
    Optional<TaxSplitResult> splitTaxAtSelf(Function<BigDecimal, BigDecimal> calTax, BigDecimal fee);


    Double getRate(Integer pushSystemId, Sku sku, SkuCustom skuCustom);

    /**
     * 获取税费比率 依赖洋800的wd-support的接口
     *
     * @param pushSystemId 推送系统的Id 以后根据这个id扩展获取税金的位置
     * @param fee          用来计税的费用
     * @param skuCustom    海关编码
     * @return 税费比率
     */
    Double getRate(Integer pushSystemId, Long fee, SkuCustom skuCustom);
}
