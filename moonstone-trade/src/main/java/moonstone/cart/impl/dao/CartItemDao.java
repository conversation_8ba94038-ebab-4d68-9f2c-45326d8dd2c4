/*
 * Copyright (c) 2014 杭州端点网络科技有限公司
 */

package moonstone.cart.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.cart.model.CartItem;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * Copyright (c) 2015 杭州端点网络科技有限公司
 * Date: 12/9/15
 * Time: 6:16 PM
 * Author: 2015年 <a href="mailto:<EMAIL>">张成栋</a>
 */
@Repository
public class CartItemDao extends MyBatisDao<CartItem> {
    public Boolean deleteBy(CartItem cartItem) {
        return getSqlSession().delete(sqlId("deleteBy"), cartItem) >= 0;
    }

    public Boolean batchDeleteBySkuIds(Long buyerId, List<Long> skuIds) {
        return getSqlSession().delete(sqlId("batchDeleteBySkuIds"), ImmutableMap.of("buyerId", buyerId, "skuIds", skuIds)) >= 0;
    }

    public Integer countCartQuantity(Long id) {
        return getSqlSession().selectOne(sqlId("countCartQuantity"), id);
    }

    public Integer countCartQuantityByShopId(Long buyerId, Long shopId) {
        return getSqlSession().selectOne(sqlId("countCartQuantityByShopId"), ImmutableMap.of("buyerId", buyerId, "shopId", shopId));
    }

    public List<Long> listAllSkuId(Long buyerId) {
        return getSqlSession().selectList(sqlId("listAllSkuId"), buyerId);
    }

    public List<CartItem> loadsByBuyer(Collection skuIds, Long buyerId) {
        return getSqlSession().selectList(sqlId("loadsByBuyer"), ImmutableMap.of("skuIds", skuIds, "buyerId", buyerId));
    }

    public  CartItem findById(Long id) {
        return getSqlSession().selectOne(sqlId("findById"), ImmutableMap.of("id", id));
    }

    public List<CartItem> findCartsByIds(List<Long> ids) {
        return getSqlSession().selectList(sqlId("findCartsByIds"), ImmutableMap.of("ids", ids));
    }
}
