package moonstone.user.service;

import moonstone.common.model.Either;
import moonstone.user.model.RoleMenu;
import moonstone.user.model.view.RoleMenuView;

import java.util.List;

public interface RoleMenuManager {


    /**
     * create the user role menu
     *
     * @param userId       userId
     * @param role         the role that user own
     * @param userRoleMenu user role menu
     * @return create
     */
    Either<Boolean> createUserRoleMenu(Long userId, String role, List<String> userRoleMenu);

    /**
     * create default role menu for user, work for the user is not configured with correct userRoleMenu
     *
     * @param role         role
     * @param userRoleMenu default menu
     * @return bool
     */
    Either<Boolean> createDefaultRoleMenu(String role, List<String> userRoleMenu);

    /**
     * find the default allow menu for role
     *
     * @param role user role
     * @return allow
     */
    Either<List<String>> defaultAllowMenuForRole(String role, Long userId);

    /**
     * find all role menu view
     *
     * @return roleMenuView
     */
    Either<List<RoleMenuView>> all();

    /**
     * find role menu by role
     *
     * @param role role
     * @return role menu view
     */
    Either<RoleMenuView> findByRole(String role);

    Either<Boolean> createByView(RoleMenuView roleMenuView);

    Either<RoleMenu> findRoleMenuById(Long id);

    Either<String> createRoleMenu(RoleMenu roleMenu);

    Either<Boolean> updateRoleMenu(RoleMenu roleMenu);
}
