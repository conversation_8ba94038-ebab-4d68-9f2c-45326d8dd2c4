package moonstone.web.front.shop.substore.dto;

import com.google.common.base.Strings;
import lombok.Data;

import java.util.Base64;
import java.util.Optional;

@Data
/// register 使用的DTO
public class RegisterInnerCodeDTO {
    /// 供应商的邀请码
    String code;

    String encode(long id) {
        String coded = Base64.getEncoder().encodeToString((id + "").getBytes()).replaceAll("=", "P");
        if (coded.length() >= 12) {
            return "G" + coded.substring(3, 11) + "D" + id;
        }
        if (coded.length() < 8) {
            return "G" + coded + Strings.repeat("X", 8 - coded.length()) + "D" + id;
        }
        return "G" + coded.substring(0, 8) + "D" + id;
    }

    public Optional<Long> decode() {

        String[] rom = code.split("D");
        if (rom.length < 2) {
            return Optional.empty();
        }
        long id = Long.parseLong(rom[rom.length - 1]);

        return encode(id).equals(code) ? Optional.of(id) : Optional.empty();
    }

    public RegisterInnerCodeDTO() {
    }
}
