package moonstone.web.core.component

import java.math.BigDecimal
import java.util

import moonstone.order.api.Y800OrderIdGenerator
import moonstone.order.model._
import moonstone.order.service.{PaymentReadService, ReceiverInfoReadService}
import moonstone.web.core.component.api.OuterSystemOrderManager
import moonstone.web.core.model.{OutSystemOrder, OutSystemOrderContent}
import org.springframework.context.annotation.Primary
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.query.{Criteria, Query}
import org.springframework.stereotype.Component

/**
 * 这个是老订单序号生成器,目前时间不够所以先使用这个
 */
@Primary
@Component
class SimpleOrderManagerImpl(y800OrderIdGenerator: Y800OrderIdGenerator
                             , paymentReadService: PaymentReadService
                             , receiverInfoReadService: ReceiverInfoReadService
                             , mongoTemplate: MongoTemplate
                            ) extends OuterSystemOrderManager {
  /**
   * 聚合订单
   * 将多个内部订单聚合成多个外部订单
   *
   * @param orderArray 被聚合的订单列表
   * @return 外部订单列表
   */
  override def getOutSystemOrder(orderArray: Array[_ <: OrderBase]): Array[OutSystemOrder] = {
    val shopOrder = orderArray.filter(order => order.isInstanceOf[ShopOrder]).map(_.asInstanceOf[ShopOrder]).head
    val skuOrderList = orderArray.filter(order => order.isInstanceOf[SkuOrder]).map(_.asInstanceOf[SkuOrder])

    val orderId = y800OrderIdGenerator.getDeclareId(shopOrder)
    val payment = paymentReadService.findByOrderIdAndOrderLevel(shopOrder.getId, OrderLevel.SHOP).getResult
      .toArray(Array.empty[Payment]).filter(_.getStatus > 0).head
    val receiverInfo = receiverInfoReadService.findByOrderId(shopOrder.getId, OrderLevel.SHOP).getResult.get(0)

    val contentArray = for (skuOrder <- skuOrderList)
      yield OutSystemOrderContent(orderId = orderId, skuOrderId = skuOrder.getId, skuId = skuOrder.getSkuId
        , skuCode = skuOrder.getOuterSkuId, quantity = skuOrder.getQuantity
        , originPrice = new BigDecimal(skuOrder.getOriginFee.toString).divide(new BigDecimal(100)), price = new BigDecimal(skuOrder.getFee.toString).divide(new BigDecimal(100))
        , shipFee = Option.apply(skuOrder.getShipFee).map(new BigDecimal(_)).getOrElse(BigDecimal.ZERO).divide(new BigDecimal(100)), tax = Option.apply(skuOrder.getTax).map(new BigDecimal(_)).getOrElse(BigDecimal.ZERO).divide(new BigDecimal(100))
        , custom = shopOrder.getDepotCustomName, discount = Option.apply(skuOrder.getDiscount).map(new BigDecimal(_)).getOrElse(BigDecimal.ZERO).divide(new BigDecimal(100))
        , remark = "", info = Map.empty[String, String], _id = null
      )

    contentArray.foreach(outOrder => mongoTemplate.insert(outOrder))

    Array(OutSystemOrder(orderId = orderId, simpleOriginOrderId = shopOrder.getId, payChannel = payment.getChannel, paymentSerial = payment.getPaySerialNo
      , paymentRawRequest = payment.getPayRequest, paymentRawResponse = payment.getPayResponse, payAmount = new BigDecimal(shopOrder.getFee).divide(new BigDecimal(100))
      , payerName = Option.apply(shopOrder.getExtra).getOrElse(new util.HashMap[String, String]()).getOrDefault("payerName", "")
      , payerNo = Option.apply(shopOrder.getExtra).getOrElse(new util.HashMap[String, String]()).getOrDefault("payerNO", "")
      , status = 1, receiverName = receiverInfo.getReceiveUserName, receiverMobile = receiverInfo.getMobile
      , province = receiverInfo.getProvince, city = receiverInfo.getCity, address = receiverInfo.getDetail, remark = ""
      , _id = null
    ))
  }

  /**
   * 解聚合订单
   * todo:不拆
   *
   * @param outSystemOrderArray 外部订单列表
   * @return 内部订单信息
   */
  override def mapOutSystemOrderIntoInside(outSystemOrderArray: Array[OutSystemOrder]): Array[_ <: OrderBase] = {
    Array.empty
  }

  /**
   * 拉取外部订单的内容单
   *
   * @param outSystemOrderId 外部订单Id
   * @return 内容单
   */
  override def getOutSystemOrderContent(outSystemOrderId: String): Array[OutSystemOrderContent] =
    mongoTemplate.find(Query.query(Criteria.where("orderId").is(outSystemOrderId)), classOf[OutSystemOrderContent]).toArray(Array.empty[OutSystemOrderContent])
}
