/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Objects;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import moonstone.common.constants.JacksonType;
import moonstone.promotion.enums.PromotionStatus;

import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * 营销工具的实例, 这里各店铺都会创建一条相应的记录, 且包含营销工具定义需要的参数信息
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-12
 */
@EqualsAndHashCode(of = {"id", "shopId", "name", "promotionDefId", "status"})
@ToString
public class Promotion implements Serializable {

    private static final long serialVersionUID = -4098028779562263231L;

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();


    /**
     * 主键id
     */
    @Getter
    @Setter
    private Long id;

    /**
     * 店铺id, 如果是平台级别的营销, 则为0
     */
    @Getter
    @Setter
    private Long shopId;

    /**
     * 营销名称
     */
    @Getter
    @Setter
    private String name;

    /**
     * 营销描述信息
     */
    @Getter
    @Setter
    private String description;

    /**
     * 对应的营销工具定义key
     */
    @Getter
    @Setter
    private Long promotionDefId;

    /**
     * 营销工具类型,冗余
     *
     * @see moonstone.promotion.enums.PromotionType
     */
    @Getter
    @Setter
    private Integer type;

    /**
     * 营销状态, 0: 初始化, 1,2: 可能生效, 需要根据生效开始和截至时间进一步判断 -1:已过期,,-2:停止
     *
     * @see moonstone.promotion.enums.PromotionStatus
     */
    @Getter
    @Setter
    private Integer status;

    /**
     * 营销生效开始时间
     */
    @Getter
    @Setter
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date startAt;

    /**
     * 营销生效截至时间
     */
    @Getter
    @Setter
    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    private Date endAt;

    /**
     * 传给选择营销用户选择范围的参数, 不存数据库
     */
    @Getter
    private Map<String, String> userScopeParams;


    /**
     * 传给选择营销用户选择范围的参数, json表示, 存数据库
     */
    @Getter
    @JsonIgnore
    private String userScopeParamsJson;

    /**
     * 传给选择营销sku选择范围的参数, 不存数据库
     */
    @Getter
    private Map<String, String> skuScopeParams;

    /**
     * 传给选择营销sku选择范围的参数, json表示, 存数据库
     */
    @Getter
    @JsonIgnore
    private String skuScopeParamsJson;

    /**
     * 传给营销执行前提条件的参数, 不存数据库
     */
    @Getter
    private Map<String, String> conditionParams;


    /**
     * 传给营销执行前提条件的参数, json表示, 存数据库
     */
    @Getter
    @JsonIgnore
    private String conditionParamsJson;


    /**
     * 传给营销执行方式的参数, 不存数据库
     */
    @Getter
    private Map<String, String> behaviorParams;


    /**
     * 传给营销执行方式的参数, json表示, 存数据库
     */
    @Getter
    @JsonIgnore
    private String behaviorParamsJson;


    /**
     * 放营销扩展信息, 建议json字符串, 存数据库
     */
    @Getter
    @JsonIgnore
    private String extraJson;

    /**
     * 放营销扩展信息,不存数据库
     */
    @Getter
    private Map<String, String> extra;


    /**
     * 创建时间
     */
    @Getter
    @Setter
    private Date createdAt;

    /**
     * 更新时间
     */
    @Getter
    @Setter
    private Date updatedAt;

    /**
     * 卷的发放状态, 主要是有些限需要定时发放，如门店限定卷
     *
     * @see moonstone.promotion.enums.PromotionIssueStatusEnum
     */
    @Deprecated
    @Getter
    @Setter
    private Integer issueStatus;


    public void setUserScopeParams(Map<String, String> userScopeParams) {
        this.userScopeParams = userScopeParams;
        if (userScopeParams != null) {
            try {
                this.userScopeParamsJson = objectMapper.writeValueAsString(userScopeParams);
            } catch (Exception e) {
                //ignore this fxxk exception
            }
        }
    }


    public void setUserScopeParamsJson(String userScopeParamsJson) throws Exception {
        this.userScopeParamsJson = userScopeParamsJson;
        if (!Strings.isNullOrEmpty(userScopeParamsJson)) {
            this.userScopeParams = objectMapper.readValue(userScopeParamsJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setSkuScopeParamsJson(String skuScopeParamsJson) throws Exception {
        this.skuScopeParamsJson = skuScopeParamsJson;
        if (!Strings.isNullOrEmpty(skuScopeParamsJson)) {
            this.skuScopeParams = objectMapper.readValue(skuScopeParamsJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setSkuScopeParams(Map<String, String> skuScopeParams) {
        this.skuScopeParams = skuScopeParams;
        if (skuScopeParams != null) {
            try {
                this.skuScopeParamsJson = objectMapper.writeValueAsString(skuScopeParams);
            } catch (Exception e) {
                //ignore this fxxk exception
            }
        }
    }

    public void setConditionParams(Map<String, String> conditionParams) {
        this.conditionParams = conditionParams;
        if (conditionParams != null) {
            try {
                this.conditionParamsJson = objectMapper.writeValueAsString(conditionParams);
            } catch (Exception e) {
                //ignore this fxxk exception
            }
        }
    }

    public void setConditionParamsJson(String conditionParamsJson) throws Exception {
        this.conditionParamsJson = conditionParamsJson;
        if (!Strings.isNullOrEmpty(conditionParamsJson)) {
            this.conditionParams = objectMapper.readValue(conditionParamsJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setBehaviorParams(Map<String, String> behaviorParams) {
        this.behaviorParams = behaviorParams;
        if (behaviorParams != null) {
            try {
                this.behaviorParamsJson = objectMapper.writeValueAsString(behaviorParams);
            } catch (Exception e) {
                //ignore this fxxk exception
            }
        }
    }

    public void setBehaviorParamsJson(String behaviorParamsJson) throws Exception {
        this.behaviorParamsJson = behaviorParamsJson;
        if (!Strings.isNullOrEmpty(behaviorParamsJson)) {
            this.behaviorParams = objectMapper.readValue(behaviorParamsJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        if (extra == null || extra.isEmpty()) {
            this.extraJson = null;
        } else {
            try {
                this.extraJson = objectMapper.writeValueAsString(extra);
            } catch (Exception e) {
                //ignore this fuck exception
            }
        }
    }

    public void setExtraJson(String extraJson) throws Exception {
        this.extraJson = extraJson;
        if (Strings.isNullOrEmpty(extraJson)) {
            this.extra = Collections.emptyMap();
        } else {
            this.extra = objectMapper.readValue(extraJson, JacksonType.MAP_OF_STRING);
        }
    }

    //判断营销活动是否有效
    public boolean inProcess() {
        Date now = new Date();
        return (Objects.equal(status, PromotionStatus.PUBLISHED.getValue())
                || Objects.equal(status, PromotionStatus.ONGOING.getValue()))
                && (now.before(getEndAt()) && now.after(getStartAt()));
    }

    //是否已过期
    public boolean isExpired() {
        return getEndAt() != null && new Date().after(getEndAt());
    }
}
