package moonstone.weShop.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Author:  CaiZhy
 * Date:    2019/1/10
 */
@Data
public class WeWithdrawDetailForSeller implements Serializable {
    private static final long serialVersionUID = 6665308262527250807L;

    private Long id;
    private String weShopName;      //微分销店铺名称
    private String applicantName;   //申请人姓名，实名认证的姓名
    private String mobile;          //手机号
    private Date appliedAt;         //申请提现时间
    private Long preApplicationBalance; //申请前账户余额
    private Long amount;            //申请金额
    private Integer status;         //提现状态
    private String applyRemark;  //申请备注
    private String auditingRemark;  //拒绝原因
    private Date updatedAt;       //更新时间
}
