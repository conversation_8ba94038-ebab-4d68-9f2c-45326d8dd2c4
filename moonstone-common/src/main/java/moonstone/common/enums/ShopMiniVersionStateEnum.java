package moonstone.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * author：书生
 */
@Getter
@AllArgsConstructor
public enum ShopMiniVersionStateEnum {

    /**
     * 构建中
     */
    IN_BUILD(0, "构建中"),

    /**
     * 构建完成
     */
    BUILD_COMPLETED(1, "构建完成"),

    /**
     * 构建失败
     */
    BUILD_FAILED(2, "构建失败"),

    /**
     * 审核中
     */
    IN_AUDIT(3, "审核中"),

    /**
     * 审核通过
     */
    AUDIT_PASSED(4, "审核通过"),


    /**
     * 审核驳回
     */
    AUDIT_REJECTED(5, "审核驳回"),

    /**
     * 下架
     */
    OFFLINE(99, "下架"),

    /**
     * 上架
     */
    ONLINE(100, "上架"),
    ;

    private final int code;

    private final String desc;

    public static String getDesc(Integer versionState) {
        for (ShopMiniVersionStateEnum shopMiniVersionStateEnum : ShopMiniVersionStateEnum.values()) {
            if (shopMiniVersionStateEnum.getCode()  == versionState) {
                return shopMiniVersionStateEnum.getDesc();
            }
        }
        return "未知版本状态";
    }
}
