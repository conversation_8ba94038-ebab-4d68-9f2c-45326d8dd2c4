package moonstone.user.impl.dao;

import com.google.common.collect.ImmutableBiMap;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.model.parameter.UserRelationEntityQueryParameter;
import org.springframework.stereotype.Repository;

import javax.annotation.Nullable;
import javax.validation.constraints.NotNull;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public class UserRelationEntityDao extends MyBatisDao<UserRelationEntity> {
    public List<UserRelationEntity> listByUserId(Long userId) {
        return getSqlSession().selectList(sqlId("listByUserId"), ImmutableMap.of("userId", userId));
    }

    public List<UserRelationEntity> listByUserIdAndType(Long userId, @NotNull UserRelationEntity.UserRelationType type, @Nullable Integer status, @Nullable Integer notStatus) {
        Map<String, Object> query = new HashMap<>(8);
        query.put("userId", userId);
        query.put("type", type.getType());
        query.put("status", status);
        query.put("notStatus", notStatus);
        return getSqlSession().selectList(sqlId("listByUserIdAndTypeAndStatus"), query);
    }

    public List<UserRelationEntity> listByRelationAndType(Long relationId, @NotNull UserRelationEntity.UserRelationType type,
                                                          @Nullable Integer status, @Nullable Integer notStatus,
                                                          Long relationIdA) {
        Map<String, Object> query = new HashMap<>(8);
        query.put("relationId", relationId);
        query.put("type", type.getType());
        query.put("status", status);
        query.put("notStatus", notStatus);
        if (relationIdA != null) {
            query.put("relationIdA", relationIdA);
        }
        return getSqlSession().selectList(sqlId("listByRelationAndTypeAndStatus"), query);
    }

    public long count(Map<String, Object> criteria) {
        return getSqlSession().selectOne(sqlId("count"), criteria);
    }

    public Boolean createWhenNotExists(Long userId, Long shopId, Long relationIdA, Long relationIdB, int type) {
        Map<String, Object> entity = new HashMap<>(8);
        entity.put("userId", userId);
        entity.put("relationId", shopId);
        entity.put("relationIdA", relationIdA);
        entity.put("relationIdB", relationIdB);
        entity.put("type", type);
        return getSqlSession().insert(sqlId("createWhenNotExists"), entity) > 0;
    }

    public Boolean updateByUserIdAndShopIdAndType(Long userId, Long shopId, Long relationIdA, Long relationIdB, int type) {
        Map<String, Object> entity = new HashMap<>(8);
        entity.put("userId", userId);
        entity.put("relationId", shopId);
        entity.put("relationIdA", relationIdA);
        entity.put("relationIdB", relationIdB);
        entity.put("type", type);
        if (relationIdA != null || relationIdB != null) {
            return getSqlSession().update(sqlId("updateByUserIdAndShopIdAndType"), entity) > 0;
        }
        return false;
    }

    public boolean deleteByUserId(Long userId) {
        return getSqlSession().delete(sqlId("deleteByUserId"), ImmutableBiMap.of("userId", userId)) > 0;
    }

    public boolean updateRelationId(UserRelationEntity parameter) {
        return getSqlSession().update(sqlId("updateRelationId"), parameter) > 0;
    }

    public boolean deleteByUserIdAndShopIdAndType(Long userId, Long shopId, Integer type) {
        return getSqlSession().delete(sqlId("deleteByUserIdAndShopIdAndType"), ImmutableBiMap.of("userId", userId,
                "relationIdA", shopId, "type", type)) > 0;
    }

    public List<UserRelationEntity> findByShopIdAndUserIdList(UserRelationEntityQueryParameter parameter) {
        return getSqlSession().selectList(sqlId("findByShopIdAndUserIdList"), parameter);
    }

    public List<UserRelationEntity> pageList(Long shopId, UserRelationEntity.UserRelationType type, List<Long> relationIds) {
        return getSqlSession().selectList(sqlId("paging"), ImmutableMap.of("relationIdA", Lists.newArrayList(shopId),
                "type", type.getType(),
                "relationIdList", relationIds));
    }

    public List<UserRelationEntity> findAllByShopId(Long shopId, int type) {
        return getSqlSession().selectList(sqlId("paging"), ImmutableMap.of("relationIdA", Lists.newArrayList(shopId),
                "type", type));
    }

    public List<UserRelationEntity> pageList(Map<String, Object> map) {
        return getSqlSession().selectList(sqlId("paging"), map);
    }

    public List<UserRelationEntity> pageList(Long shopId, UserRelationEntity.UserRelationType type, Long userId) {
        return getSqlSession().selectList(sqlId("paging"), ImmutableMap.of("relationIdA", Lists.newArrayList(shopId),
                "type", type.getType(),
                "userId", userId));
    }

    public List<UserRelationEntity> listByShopId(long shopId) {
        return getSqlSession().selectList(sqlId("listByShopId"), ImmutableMap.of("shopId", shopId));
    }

    public UserRelationEntity findByUserIdAndShopIdAndType(Long userId, Long shopId, int type) {
        return getSqlSession().selectOne(sqlId("findByUserIdAndShopIdAndType"), ImmutableMap.of("userId", userId,
                "shopId", shopId,
                "type", type));
    }

}
