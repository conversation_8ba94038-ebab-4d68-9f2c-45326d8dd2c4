package moonstone.promotion.enums;

public enum PromotionExtraIndex {
    activityItemId("activityItemId", "门店限定卷，活动商品id列表"),
    activityTimeStart("activityTimeStart", "门店限定卷，活动开始时间"),
    activityTimeEnd("activityTimeEnd", "门店限定卷，活动结束时间"),
    ;

    private String code;
    private String description;

    PromotionExtraIndex(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
