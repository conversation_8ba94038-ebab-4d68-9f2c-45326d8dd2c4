package moonstone.web.core.order.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class RefundItemVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 2740246714371978001L;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 商品单价
     */
    private BigDecimal originUnitFee;

    /**
     * 数量
     */
    private Long quantity;

    /**
     * 商品主图
     */
    private String skuImage;
}
