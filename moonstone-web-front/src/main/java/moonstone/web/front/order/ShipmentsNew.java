package moonstone.web.front.order;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EmptyUtils;
import moonstone.common.utils.R;
import moonstone.order.api.FlowPicker;
import moonstone.order.dto.fsm.Flow;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.ShopOrderReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * Date: 2019/8/7 15:15
 */
@Slf4j
@RestController
@RequestMapping("/api/new")
public class ShipmentsNew {

    @Autowired
    private ShopOrderReadService shopOrderReadService;

    @Autowired
    private FlowPicker flowPicker;

    @Autowired
    private Shipments shipments;

    @RequestMapping(value = "/buyer/confirm/{orderId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public R confirm(@PathVariable("orderId") Long orderId) {
        if (orderId == null) {
            return R.error(-1, "订单号为空");
        }
        Response<List<ShopOrder>> shopOrderRes = shopOrderReadService.findByIds(Collections.singletonList(orderId));

        var confirmResult = shipments.confirm(orderId, 1);
        if (Boolean.TRUE.equals(confirmResult.getData())) {
            if (shopOrderRes.isSuccess() && EmptyUtils.isNotEmpty(shopOrderRes.getResult())) {
                ShopOrder shopOrder = shopOrderRes.getResult().get(0);
                Flow flow = flowPicker.pick(shopOrder, OrderLevel.SHOP);
                Integer targetStatus = flow.target(shopOrder.getStatus(), OrderEvent.CONFIRM.toOrderOperation());
                return R.ok().add("data", targetStatus);
            }
            return R.ok();
        } else {
            return R.error(-1, "确认收货失败！");
        }
    }
}
