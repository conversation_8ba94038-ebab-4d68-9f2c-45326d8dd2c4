package moonstone.web.front.profit.view;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class WaitingWithdrawOrderDetailVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -5237998995868437944L;

    /**
     * 订单id
     */
    private Long shopOrderId;

    /**
     * 申报单号
     */
    private String declaredId;

    /**
     * 订单金额（单位：分）
     */
    private Long fee;

    /**
     * 门店角色的分佣金额（单位：分）
     */
    private Long subStoreProfit;

    /**
     * 下单时间
     */
    private String createdAt;

    /**
     * 发货时间
     */
    private String shipmentAt;

    /**
     * 确认收货
     */
    private String confirmAt;

    /**
     * 订单的商品信息
     */
    private List<WaitingWithdrawOrderSkuVO> skuInfoList;
}
