package moonstone.common.model.rpcAPI.y800Storage;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class Y800StorageSku {
    private String skuNo;//商品代码	string	是
    private String skuName;//商品名称	string	是
    private String unit;//单位	string	是
    private Object status;//状态	int	是		0未启用 1启用
    private String barCode;//商品条码	string	否
    private Integer isBatch;//是否启用批次	int	否		0 否 1 是
    private Integer isMaterial;//是否耗材	int	否		0 否 1 是
    private BigDecimal length;//长	Decimal(18,4)	否		单位(毫米:mm)
    private BigDecimal width;//宽	Decimal(18,4)	否		单位(毫米:mm)
    private BigDecimal height;//高	Decimal(18,4)	否		单位(毫米:mm)
    private BigDecimal volume;//体积	Decimal(18,4)	否		单位(立方毫米:mm)
    private BigDecimal grossWeight;//毛重	Decimal(18,4)	否		单位(千克:kg)
    private BigDecimal weight;//净重	Decimal(18,4)	否		单位(千克:kg)
    private Integer shelfLife;//保质期 (天)	int	否

    /**
     * 商品交易类型 <br/>
     * @see moonstone.common.model.rpcAPI.enums.Y800StorageSkuTradeTypeEnum
     */
    private Integer tradeType;

    /**
     * @see moonstone.web.core.fileNew.enums.SourceTypeEnum
     */
    private Integer source;

    private List<ImageInfo> imageInfos;

    private SkuBigFieldInfo skuBigFieldInfo;

    /**
     * 产品说明。当规格参数、扩展属性都无返回值，才会返回产品说明
     */
    private String wReadMe;

    private SkuBaseInfo skuBaseInfo;

    @Data
    public static class SkuBigFieldInfo implements Serializable {
        private String pcCssContent;

        private String pcJsContent;

        private String pcWdis;
    }

    @Data
    public static class ImageInfo implements Serializable {

        public static final int SerialVersionUID = -1;

        private String path;

        private Integer orderSort;

        /**
         * 是否主图 0：否 1：是
         */
        private Integer isPrimary;
    }

    @Data
    public static class SkuBaseInfo implements Serializable{

        public static final int SerialVersionUID = -2;

        private String unit;

        private String tax;

        private String placeOfProduction;
    }
}
