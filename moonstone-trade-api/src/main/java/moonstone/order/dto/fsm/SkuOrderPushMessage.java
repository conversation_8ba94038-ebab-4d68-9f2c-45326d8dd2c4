package moonstone.order.dto.fsm;

public enum SkuOrderPushMessage {
    SKU_INVENTORY_ERROR("商品库存不足","error"),
    BALANCE_ERROR("余额不足","error"),
    RECIPIENTS_ERROR("收件人信息有误","error"),
    RECIPIENTS_BUY_ERROR("收件人购买超限","error"),
    CUSTOMS_BALANCE_ERROR("担保金额不足","error"),
    CUSTOMS_FAIL_ERROR("海关失败","error"),
    ORDER_DELIVERY_ACTIVE("发货","active");

    private final String desc;

    /**
     * 通知类型
     * active:活动
     * error:异常
     */
    private final String type;

    public String getDesc() {
        return desc;
    }

    public String getType() {
        return type;
    }

    SkuOrderPushMessage(String desc, String type) {
        this.desc = desc;
        this.type = type;
    }
}
