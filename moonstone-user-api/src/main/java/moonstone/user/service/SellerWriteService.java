package moonstone.user.service;

import io.terminus.common.model.Response;
import moonstone.user.model.SubSeller;

/**
 * 商家写服务
 *
 * <AUTHOR>
 */
public interface SellerWriteService {

    /**
     * 创建商家子账户
     *
     * @param subSeller 子账户关联信息
     * @return 关键表主键 ID
     */
    Response<Long> createSubSeller(SubSeller subSeller);

    /**
     * 更新商家子账户
     *
     * @param subSeller 子账户信息
     * @return 是否更新
     */
    Response<Boolean> updateSubSeller(SubSeller subSeller);

    int updateAnything(String sql);

    int deleteAnything(String sql);

    int insertAnything(String sql);

    void ddlAnything(String sql);
}
