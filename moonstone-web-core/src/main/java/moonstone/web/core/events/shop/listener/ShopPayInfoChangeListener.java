package moonstone.web.core.events.shop.listener;

import io.vertx.core.AbstractVerticle;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.VertxEventTag;
import moonstone.shop.service.ShopPayInfoReadService;
import moonstone.web.core.component.vertx.VertxEventBusListener;
import moonstone.web.core.events.shop.ShopPayInfoChangeEvent;
import moonstone.web.core.registers.shop.TokenRegister;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class ShopPayInfoChangeListener extends AbstractVerticle {

    @Autowired
    private TokenRegister tokenRegister;

    @Autowired
    private ShopPayInfoReadService shopPayInfoReadService;

    @VertxEventTag(cache = true, desc = "店铺支付配置变更事件！-转MQ", model = MessageModel.BROADCASTING)
//    @VertxEventBusListener(ShopPayInfoChangeEvent.class)
    public void shopPayInfoChange(ShopPayInfoChangeEvent event) {
        tokenRegister.regPayTokenFromShopPayInfo(shopPayInfoReadService.getById(event.getShopPayInfo().getId()).getResult());
    }
}
