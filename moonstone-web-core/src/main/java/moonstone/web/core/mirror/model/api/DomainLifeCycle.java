package moonstone.web.core.mirror.model.api;


import java.lang.annotation.*;

/**
 * mark the Domain is action by Domain Life Cycle
 */
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DomainLifeCycle {
    Class<? extends Enum<? extends DomainStatus>>[] statusFactory() default {};

    /**
     * should method be discovery
     */
    boolean autoDiscovery() default false;

    /**
     * enable at autoDiscovery mode, should we skip void?
     */
    boolean skipVoid() default true;

    /**
     * enable at autoDiscovery mode, any return type in this array won't be register
     */
    Class<?>[] skipReturn() default {Class.class};

    /**
     * enable at autoDiscovery mode, if method name in this, will skip the rule
     */
    String[] methodExceptForRule() default {};
}
