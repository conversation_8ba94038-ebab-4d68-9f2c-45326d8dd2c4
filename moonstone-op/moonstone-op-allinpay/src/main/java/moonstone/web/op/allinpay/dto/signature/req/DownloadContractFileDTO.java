package moonstone.web.op.allinpay.dto.signature.req;

import lombok.Data;
import moonstone.web.op.allinpay.AllinPayMethodEnum;
import moonstone.web.op.allinpay.dto.signature.BaseRequest;
import moonstone.web.op.allinpay.dto.signature.res.DownloadContractFileResponse;

import java.io.Serializable;

@Data
public class DownloadContractFileDTO extends BaseRequest implements Serializable {
    /**
     * 统一客户号
     */
    private String merId;

    /**
     * 商户订单号（由商户生成），不重复值
     */
    private String custOrderId;

    /**
     * 操作者用户帐号，必须要指定一个用户帐号作为操作者
     */
    private String account;

    /**
     * 文件ID，文件编号
     */
    private String fid;

    @Override
    public Class responseClass() {
        return DownloadContractFileResponse.class;
    }

    @Override
    public AllinPayMethodEnum method() {
        // "allinpay.uas.downloadContractFile"
        return AllinPayMethodEnum.SIGNATURE_DOWNLOAD_CONTRACT_FILE;
    }

}
