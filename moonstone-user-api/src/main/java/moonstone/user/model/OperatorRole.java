package moonstone.user.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.*;
import moonstone.common.constants.JacksonType;
import moonstone.user.auth.CustomRole;

import java.io.Serializable;
import java.util.*;

/**
 * 运营动态角色
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(of = {"id"})
@ToString(of = {"id", "name", "desc", "status"})
public class OperatorRole implements Serializable, CustomRole {

    private static final long serialVersionUID = 0;

    private static final ObjectMapper OBJECT_MAPPER = JsonMapper.nonEmptyMapper().getMapper();

    /**
     * 主键
     */
    private Long id;

    /**
     * 角色名
     */
    private String name;

    /**
     * 角色描述
     */
    private String desc;

    /**
     * 角色状态:
     *
     * 0. 未生效(冻结), 1. 生效, -1. 删除
     */
    private Integer status;

    @Override
    public boolean isActive() {
        return Objects.equals(status, 1);
    }

    /**
     * 角色对应选中权限树节点列表, 不存数据库
     */
    @Setter(AccessLevel.NONE)
    private List<String> allow;

    /**
     * 角色对应选中权限树节点列表 JSON, 存数据库
     */
    @Setter(AccessLevel.NONE)
    @JsonIgnore
    private String allowJson;

    /**
     * 扩展信息, 不存数据库
     */
    @Setter(AccessLevel.NONE)
    private Map<String, String> extra;

    /**
     * 扩展信息 JSON, 存数据库
     */
    @Setter(AccessLevel.NONE)
    @JsonIgnore
    private String extraJson;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    @SneakyThrows
    public void setAllow(List<String> allow) {
        this.allow = allow;
        if (allow == null) {
            this.allowJson = null;
        } else {
            this.allowJson = OBJECT_MAPPER.writeValueAsString(allow);
        }
    }

    @SneakyThrows
    public void setAllowJson(String allowJson) {
        this.allowJson = allowJson;
        if (allowJson == null) {
            this.allow = null;
        } else if (allowJson.length() == 0) {
            this.allow = Collections.emptyList();
        } else {
            this.allow = OBJECT_MAPPER.readValue(allowJson, new TypeReference<List<String>>() {
            });
        }
    }

    @SneakyThrows
    public void setExtra(Map<String, String> extra) {
        this.extra = extra;
        if (extra == null || extra.isEmpty()) {
            this.extraJson = null;
        } else {
            this.extraJson = OBJECT_MAPPER.writeValueAsString(extra);
        }
    }

    @SneakyThrows
    public void setExtraJson(String extraJson) {
        this.extraJson = extraJson;
        if (Strings.isNullOrEmpty(extraJson)) {
            this.extra = Collections.emptyMap();
        } else {
            this.extra = OBJECT_MAPPER.readValue(extraJson, JacksonType.MAP_OF_STRING);
        }
    }

    @Override
    public Map<String, String> getContext() {
        return Collections.emptyMap();
    }
}
