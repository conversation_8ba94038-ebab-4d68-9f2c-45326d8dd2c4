/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.spu.impl.dao;

import com.google.common.collect.ImmutableList;
import moonstone.BaseDaoTest;
import moonstone.attribute.dto.GroupedSkuAttribute;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.spu.model.SpuAttribute;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.hamcrest.core.Is.is;
import static org.hamcrest.core.IsNull.notNullValue;
import static org.hamcrest.core.IsNull.nullValue;
import static org.junit.Assert.assertThat;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-20
 */
public class SpuAttributeDaoTest extends BaseDaoTest{

    @Autowired
    private SpuAttributeDao spuAttributeDao;

    private SpuAttribute spuAttribute;

    @Before
    public void setUp() throws Exception {
        spuAttribute = new SpuAttribute();
        spuAttribute.setSpuId(1L);
        spuAttribute.setSkuAttrsJson("[]");
        spuAttribute.setOtherAttrsJson("[]");
        assertThat(spuAttributeDao.create(spuAttribute), is(true));

    }

    @Test
    public void testFindBySpuId() throws Exception {
        assertThat(spuAttributeDao.findBySpuId(spuAttribute.getSpuId()), notNullValue());
    }

    @Test
    public void testUpdateBySpuId() throws Exception {
        SpuAttribute u = new SpuAttribute();
        u.setSpuId(spuAttribute.getSpuId());
        SkuAttribute red = new SkuAttribute();
        red.setAttrKey("color");
        red.setAttrVal("red");

        SkuAttribute green = new SkuAttribute();
        green.setAttrKey("color");
        green.setAttrVal("green");

        final List<SkuAttribute> colors = ImmutableList.of(red,green);
        final List<GroupedSkuAttribute> skuAttrs = ImmutableList.of(new GroupedSkuAttribute("color", colors));
        u.setSkuAttrs(skuAttrs);
        spuAttributeDao.updateBySpuId(u);
        final SpuAttribute ia = spuAttributeDao.findBySpuId(spuAttribute.getSpuId());
        assertThat(ia.getSkuAttrs(), is(skuAttrs));
    }

    @Test
    public void testDeleteBySpuId() throws Exception {
        spuAttributeDao.deleteBySpuId(spuAttribute.getSpuId());
        assertThat(spuAttributeDao.findBySpuId(spuAttribute.getSpuId()), nullValue());
    }
}