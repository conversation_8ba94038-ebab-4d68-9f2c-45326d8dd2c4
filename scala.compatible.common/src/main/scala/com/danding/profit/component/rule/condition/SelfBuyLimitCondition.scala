package com.danding.profit.component.rule.condition

import java.lang

import com.danding.profit.api.rule.base.RuleBase
import com.danding.profit.component.rule.RuleConstant
import moonstone.common.model.JudgeBean
import moonstone.order.model.ShopOrder

/**
 * 限制是否允许自购
 */
class SelfBuyLimitCondition extends RuleBase("self_buy_limit", RuleConstant.ProfitRulerCondition.toString) with JudgeBean[ShopOrder] {
  /**
   * 是否允许自购
   * return false if rule not set and buyerId=ReferenceId
   *
   * @param target 被裁决实体
   * @return 判断结果
   */
  override def judge(target: ShopOrder, environment: collection.Map[String, String]): lang.Boolean = (target.getRefererId != null)
    .&(target.getRefererId.equals(target.getBuyerId).&(environment.get(RuleConstant.SelfBuyAllow.toString).exists("true".eq)))
}
