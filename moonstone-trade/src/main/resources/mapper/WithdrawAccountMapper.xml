<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="WithdrawAccount">

    <resultMap id="WithdrawAccountMap" type="WithdrawAccount">
        <id column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="shop_id" property="shopId"/>
        <result column="user_id" property="userId"/>
        <result column="type" property="type"/>
        <result column="extra_str" property="extraStr"/>
        <result column="name" property="name"/>
        <result column="account" property="account"/>
        <result column="from" property="from"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_withdraw_account
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `status`,`user_id`,`shop_id`,`name`,`from`,`account`,
        `extra_str`,`type`,`created_at`,`updated_at`
    </sql>

    <sql id="vals">
        #{status},#{userId},#{shopId},#{name},#{from},#{account},
        #{extraStr},#{type},now(),now()
    </sql>

    <insert id="create" parameterType="WithdrawAccount" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>
    <select id="findByShopIdAndUserId" parameterType="map" resultMap="WithdrawAccountMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            `shop_id`= #{shopId}
            and `user_id`= #{userId}
            and `status` != -1
        </where>
    </select>
    <select id="findAccount" parameterType="map" resultMap="WithdrawAccountMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            `shop_id`= #{shopId}
            and `user_id`= #{userId}
            and `from` = #{from}
            and `account` = #{account}
            and `status` != -1
        </where>
    </select>
    <select id="findById" parameterType="long" resultMap="WithdrawAccountMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByIds" parameterType="list" resultMap="WithdrawAccountMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="update" parameterType="WithdrawAccount">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now()
            <if test="status != null">,`status` = #{status}</if>
            <if test="userId!= null">,`user_id` = #{userId}</if>
            <if test="shopId!= null">,`shop_id` = #{shopId}</if>
            <if test="name!= null">,`name` = #{name}</if>
            <if test="account!= null">,`account` = #{account}</if>
            <if test="from!= null">,`from` = #{from}</if>
            <if test="type!= null">,`type` = #{type}</if>
            <if test="extraStr != null">,`extra_str` = #{extraStr}</if>
        </set>
        WHERE id=#{id}
    </update>


    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <update id="revokeDefault" parameterType="map">
        update
        <include refid="tb"/>
        <set>
            `status`=`status` <![CDATA[ & ~2 ]]>
        </set>
        <where>
            `shop_id`=#{shopId}
            and `user_id`=#{userId}
        </where>
    </update>

</mapper>