package moonstone.showcase;

import moonstone.web.core.component.RequestVerify;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;


@Configuration
public class RequestVerifyCheckConfiguration {
    @Bean
    @Profile("online")
    public RequestVerify.VerifyOn verifyOn(){
        return new RequestVerify.VerifyOn();
    }
}
