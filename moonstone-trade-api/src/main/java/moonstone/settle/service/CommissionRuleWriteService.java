package moonstone.settle.service;

import io.terminus.common.model.Response;
import moonstone.settle.model.CommissionRule;

/**
 * Code generated by terminus code gen
 * Desc: 写服务
 * Date: 2016-07-25
 */

public interface CommissionRuleWriteService {

    /**
     * 创建CommissionRule
     * @param commissionRule
     * @return 主键id
     */
    Response<Long> createCommissionRule(CommissionRule commissionRule);

    /**
     * 更新CommissionRule
     * @param commissionRule
     * @return 是否成功
     */
    Response<Boolean> updateCommissionRule(CommissionRule commissionRule);

    /**
     * 根据主键id删除CommissionRule
     * @param commissionRuleId
     * @return 是否成功
     */
    Response<Boolean> deleteCommissionRuleById(Long commissionRuleId);
}