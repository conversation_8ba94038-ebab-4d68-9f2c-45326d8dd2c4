/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.spu.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.spu.dto.FullSpu;
import moonstone.spu.model.Spu;

/**
 * Spu 读服务
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-22
 */
public interface SpuReadService {

    /**
     * 分页查询某个类目下的spu列表
     *
     * @param categoryId   类目id
     * @param keyword  名称关键字, 前缀查询, 可以为空
     * @param pageNo   起始页码, 从1开始
     * @param pageSize 分页大小
     * @return  spu列表
     */
    Response<Paging<Spu>> findByCategoryId(Long categoryId, String keyword, Integer pageNo,
                                                 Integer pageSize);


    /**
     * 根据Spu id查找对应的Spu 信息及sku, 也可以给编辑Spu 信息用的
     *
     * @param spuId  Spu id
     * @return 如果找到对应的Spu , success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    Response<FullSpu> findFullInfoBySpuId(Long spuId);

    /**
     * 根据id查找对应的spu
     *
     * @param spuId   spu id
     * @return  如果找到对应的Spu , success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    Response<Spu> findById(Long spuId);

    /**
     * 根据spu id查找对应的spu详情富文本
     *
     * @param spuId  spu id
     * @return  如果找到对应的spu , success为true, 且返回对应的数据,
     * 否则返回success为false
     */
    Response<String> findRichTextById(Long spuId);
}
