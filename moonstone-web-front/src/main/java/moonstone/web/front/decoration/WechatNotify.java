package moonstone.web.front.decoration;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.kefu.WxMpKefuMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.open.bean.message.WxOpenXmlMessage;
import moonstone.web.front.component.decoration.WxMpMessageCenter;
import moonstone.wxOpen.service.WxMpKefuParanaService;
import moonstone.wxOpen.service.WxOpenParanaComponentService;
import moonstone.wxToken.service.WxTokenReadService;
import moonstone.wxToken.service.WxTokenWriteService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2018/10/30.
 */
@Slf4j
@RestController
@RequestMapping("/api/wechat/notify")
public class WechatNotify {
    @Autowired
    private WxMpMessageCenter wxMpMessageCenter;

    @RpcConsumer
    private WxOpenParanaComponentService wxOpenParanaComponentService;

    @RpcConsumer
    private WxMpKefuParanaService wxMpKefuParanaService;

    @RpcConsumer
    private WxTokenReadService wxTokenReadService;

    @RpcConsumer
    private WxTokenWriteService wxTokenWriteService;

    @RequestMapping("/receive_ticket")
    public Object receiveTicket(@RequestBody(required = false) String requestBody, @RequestParam("timestamp") String timestamp,
                                @RequestParam("nonce") String nonce, @RequestParam("signature") String signature,
                                @RequestParam(name = "encrypt_type", required = false) String encType,
                                @RequestParam(name = "msg_signature", required = false) String msgSignature) {
        log.info("[op:receiveTicket] \n接收微信请求：[signature=[{}], encType=[{}], msgSignature=[{}]," + " timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
                signature, encType, msgSignature, timestamp, nonce, requestBody);

        if (!StringUtils.equalsIgnoreCase("aes", encType)
                || !wxOpenParanaComponentService.checkSignature(timestamp, nonce, signature)) {
            throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
        }

        // aes加密的消息
        String plainText = wxOpenParanaComponentService.decryptToXml(requestBody, timestamp, nonce, msgSignature);
        WxOpenXmlMessage inMessage = WxOpenXmlMessage.fromXml(plainText);
        log.debug("[op:receiveTicket] \n消息解密后内容为：\n{} ", inMessage.toString());

        try {
            String out = wxOpenParanaComponentService.route(inMessage);
            log.debug("[op:receiveTicket] \n组装回复信息：{}", out);
        } catch (Exception e) {
            log.error("[op:receiveTicket] receiveTicket failed, error: {}", e);
        }

        return "success";
    }

    @RequestMapping("/{appId}/callback")
    public Object callback(@RequestBody(required = false) String requestBody,
                           @PathVariable("appId") String appId,
                           @RequestParam("signature") String signature,
                           @RequestParam("timestamp") String timestamp,
                           @RequestParam("nonce") String nonce,
                           @RequestParam("openid") String openid,
                           @RequestParam("encrypt_type") String encType,
                           @RequestParam("msg_signature") String msgSignature) {
        log.info("[op:callback] \n接收微信请求：[appId=[{}], openid=[{}], signature=[{}], encType=[{}], msgSignature=[{}],"
                        + " timestamp=[{}], nonce=[{}], requestBody=[\n{}\n] ",
                appId, openid, signature, encType, msgSignature, timestamp, nonce, requestBody);
        if (!StringUtils.equalsIgnoreCase("aes", encType)
                || !wxOpenParanaComponentService.checkSignature(timestamp, nonce, signature)) {
            throw new IllegalArgumentException("非法请求，可能属于伪造的请求！");
        }

        String out = "";
        // aes加密的消息
        String plainText = wxOpenParanaComponentService.decryptToXml(requestBody, timestamp, nonce, msgSignature);
        WxMpXmlMessage inMessage = WxMpXmlMessage.fromXml(plainText);
        log.debug("[op:callback] \n消息解密后内容为：\n{} ", inMessage.toString());
        // 全网发布测试用例
        if (StringUtils.equalsIgnoreCase(appId, "wxd101a85aa106f53e") || StringUtils.equalsIgnoreCase(appId, "wx570bc396a51b8ff8")) {
            try {
                if (StringUtils.equals(inMessage.getMsgType(), "text")) {
                    if (StringUtils.equals(inMessage.getContent(), "TESTCOMPONENT_MSG_TYPE_TEXT")) {
                        String plainXml = WxMpXmlOutMessage.TEXT().content("TESTCOMPONENT_MSG_TYPE_TEXT_callback")
                                .fromUser(inMessage.getToUser()).toUser(inMessage.getFromUser()).build().toXml();
                        out = wxOpenParanaComponentService.encryptFromXml(plainXml);
                    } else if (StringUtils.startsWith(inMessage.getContent(), "QUERY_AUTH_CODE:")) {
                        String msg = inMessage.getContent().replace("QUERY_AUTH_CODE:", "") + "_from_api";
                        WxMpKefuMessage kefuMessage = WxMpKefuMessage.TEXT().content(msg).toUser(inMessage.getFromUser()).build();
                        wxMpKefuParanaService.sendKefuMessage(kefuMessage, appId);
                    }
                } else if (StringUtils.equals(inMessage.getMsgType(), "event")) {
                    WxMpKefuMessage kefuMessage = WxMpKefuMessage.TEXT().content(inMessage.getEvent() + "from_callback").toUser(inMessage.getFromUser()).build();
                    wxMpKefuParanaService.sendKefuMessage(kefuMessage, appId);
                }
            } catch (Exception e) {
                log.error("[op:callback] fail to handle callback by appId={}, inMessage={}", appId, inMessage, e);
            }
        } else {
            WxMpXmlOutMessage outMessage = wxMpMessageCenter.route(inMessage, plainText);

            if (outMessage != null) {
                out = wxOpenParanaComponentService.encryptFromXml(outMessage.toXml());
            }
        }
        return out;
    }
}
