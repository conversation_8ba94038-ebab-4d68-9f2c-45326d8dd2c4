/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.user.service;

import io.terminus.common.model.BaseUser;
import io.terminus.common.model.Response;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.enums.UserRole;
import moonstone.common.model.Either;
import moonstone.user.model.User;
import moonstone.user.model.UserWx;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-28
 */
public interface UserWriteService<T extends BaseUser> {

    /**
     * 创建新用户
     *
     * @param user 用户
     * @return 新用户id
     */
    Response<Long> create(T user);

    /**
     * 更新用户
     *
     * @param user 用户
     * @return 是否更新成功
     */
    Response<Boolean> update(T user);

    User createByWx(UserWx userWx);

    User createByWxNew(UserWx userWx, String mobile, String nickName);

    /**
     * 添加用户的身份
     *
     * @param id       用户Id
     * @param userRole 用户身份
     * @return 是否添加成功
     */
    Either<Boolean> addRole(Long id, UserRole userRole);

    /**
     * 初始化微信用户信息
     *
     * @param openId openId
     * @param appId  appId
     * @return 用户信息
     */
    Either<UserWx> initUserWx(String openId, String sessionKey, String unionId, String appId);

    /**
     * 初始化微信用户信息
     *
     * @param openId openId
     * @param appId  appId
     * @return 用户信息
     */
    Either<UserWx> initUserWx(String openId, String sessionKey, String unionId, String appId, String mobile);

    Either<UserWx> initUserWx(String openId, String sessionKey, String unionId, String appId, String mobile, AppTypeEnum appType);

    /**
     * 根据appId和openId定位userWx 以更新userWx
     *
     * @param appId  小程序appId
     * @param openId 用户的openId通常唯一
     * @param userWx 用户信息
     * @return 是否更新成功
     */
    Either<Boolean> updateUserWx(String appId, String openId, UserWx userWx);

}
