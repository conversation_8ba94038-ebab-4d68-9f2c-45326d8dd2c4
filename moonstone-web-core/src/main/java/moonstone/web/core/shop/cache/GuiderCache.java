package moonstone.web.core.shop.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import io.terminus.common.model.Paging;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.dto.SubStoreTStoreGuiderCriteria;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;

@Component
@Slf4j
public class GuiderCache {
    @Resource
    SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;

    LoadingCache<ShopIdAndUserId, Optional<SubStoreTStoreGuider>> cache
            = Caffeine.newBuilder().maximumSize(1000).expireAfterWrite(15, TimeUnit.SECONDS)
            .build(this::cacheByShopIdAndUserId);
    LoadingCache<Long, List<SubStoreTStoreGuider>> guiderListCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .maximumSize(50)
            .build(this::loadGuider);

    LoadingCache<ShopIdAndSubStoreId, List<SubStoreTStoreGuider>> guiderListBySubStoreCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .maximumSize(100)
            .build(this::loadGuider);

    private Optional<SubStoreTStoreGuider> cacheByShopIdAndUserId(@NonNull ShopIdAndUserId shopIdAndUserId) {
        return subStoreTStoreGuiderReadService.findByStoreGuiderUserId(shopIdAndUserId.getUserId(), shopIdAndUserId.getShopId())
                .getResult().stream().findFirst();
    }

    public Optional<SubStoreTStoreGuider> findByShopIdAndUserId(long shopId, long userId) {
        return cache.get(new ShopIdAndUserId(shopId, userId));
    }

    public List<SubStoreTStoreGuider> findByShopIdAndSubStoreId(long shopId, long subStoreId) {
        return guiderListBySubStoreCache.get(new ShopIdAndSubStoreId(shopId, subStoreId));
    }

    public void invalidate(long shopId, Long subStoreId, long userId) {
        cache.invalidate(new ShopIdAndUserId(shopId, userId));
        guiderListCache.invalidate(shopId);
        guiderListBySubStoreCache.invalidate(new ShopIdAndSubStoreId(shopId, subStoreId));
    }

    public List<SubStoreTStoreGuider> cacheGuider(Long shopId) {
        return guiderListCache.get(shopId);
    }

    List<SubStoreTStoreGuider> loadGuider(Long shopId) {
        SubStoreTStoreGuiderCriteria search = new SubStoreTStoreGuiderCriteria();
        search.setShopId(shopId);
        search.setPageSize(5000);
        return subStoreTStoreGuiderReadService.paging(search).getResult().getData();
    }

    List<SubStoreTStoreGuider> loadGuider(ShopIdAndSubStoreId parameter) {
        SubStoreTStoreGuiderCriteria search = new SubStoreTStoreGuiderCriteria();
        search.setShopId(parameter.getShopId());
        search.setSubStoreIds(Lists.newArrayList(parameter.getSubStoreId()));
        search.setPageSize(Integer.MAX_VALUE);

        return subStoreTStoreGuiderReadService.paging(search).getResult().getData();
    }

    public Paging<SubStoreTStoreGuider> cacheGuider(SubStoreTStoreGuiderCriteria criteria) {
        String name = criteria.getStoreGuiderNickname();
        String mobile = criteria.getStoreGuiderMobile();
        criteria.setStoreGuiderMobile(null);
        criteria.setStoreGuiderNickname(null);
        List<SubStoreTStoreGuider> guiderList = cacheGuider(criteria.getShopId());
        List<SubStoreTStoreGuider> match = new ArrayList<>();
        long count = 0;
        for (SubStoreTStoreGuider guider : guiderList) {
            List<Boolean> eq = new LinkedList<>();
            if (!ObjectUtils.isEmpty(name)) {
                eq.add(guider.getStoreGuiderNickname() != null
                        && guider.getStoreGuiderNickname().contains(name));
            }
            if (!ObjectUtils.isEmpty(mobile)) {
                eq.add(guider.getStoreGuiderMobile() != null
                        && guider.getStoreGuiderMobile().contains(mobile));
            }
            if (!CollectionUtils.isEmpty(criteria.getSubStoreIds())) {
                eq.add(criteria.getSubStoreIds().contains(guider.getSubStoreId()));
            }

            if (eq.stream().allMatch(Predicate.isEqual(Boolean.TRUE))) {
                if (count++ >= Math.max(criteria.getPageSize() * (criteria.getPageNo() - 1), 0) &&
                        match.size() < criteria.getPageSize()) {
                    match.add(guider);
                }
            }
        }
        return new Paging<>(count, match);
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    private static class ShopIdAndUserId {
        Long shopId;
        Long userId;
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    private static class ShopIdAndSubStoreId {
        Long shopId;
        Long subStoreId;
    }
}
