package moonstone.settle.service;

import io.terminus.common.model.Response;
import moonstone.settle.model.PayChannelDetail;

/**
 * Code generated by terminus code gen
 * Desc: 写服务
 * Date: 2016-07-24
 */

public interface PayChannelDetailWriteService {

    /**
     * 创建PayChannelDetail
     * @param payChannelDetail
     * @return 主键id
     */
    Response<Long> createPayChannelDetail(PayChannelDetail payChannelDetail);

    /**
     * 更新PayChannelDetail
     * @param payChannelDetail
     * @return 是否成功
     */
    Response<Boolean> updatePayChannelDetail(PayChannelDetail payChannelDetail);

    /**
     * 根据主键id删除PayChannelDetail
     * @param payChannelDetailId
     * @return 是否成功
     */
    Response<Boolean> deletePayChannelDetailById(Long payChannelDetailId);
}