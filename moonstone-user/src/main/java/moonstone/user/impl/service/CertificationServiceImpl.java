package moonstone.user.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.user.impl.dao.CertificationDao;
import moonstone.user.model.Certification;
import moonstone.user.service.CertificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 功能描述:   认证业务实现类
 * 创建时间:  2020/7/1 2:41 下午
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CertificationServiceImpl implements CertificationService {

    @Autowired
    private CertificationDao certificationDao;

    /**
     * 功能描述:  根据用户登录的userId查询认证的进度。
     * 创建时间:  2020/7/1 2:43 下午
     *
     * @param userId: 用户id
     * @return moonstone.user.model.Certification
     * <AUTHOR>
     */
    @Override
    public Certification findByUserId(Long userId, String userName, String mobile) {
        //根据userId查询认证记录。
        Certification certification = certificationDao.findByUserId(userId);
        //查询不到认证记录，则初始化。
        if (certification == null) {
            certification = new Certification();
            certification.setUserId(userId);
            certification.setUserName(userName);
            certification.setMobile(mobile);
            certification.setStatus(0);
            certification.setCreatedAt(new Date());
            certification.setUpdatedAt(new Date());
            certificationDao.create(certification);
        }
        //查询到认证记录后判断状态是否是认证成功，如果是认证成功状态，则去修改一下认证状态，以后就直接跳转首页。
        //这样就实现了认证成功后第一次必须要跳转到认证成功界面，后续的直接跳转到首页。
        //11, 21 31,是供应商，分销商实体店，分销商微商的认证成功状态码
        if (certification.getStatus() == 11 || certification.getStatus() == 21 || certification.getStatus() == 31) {
            Certification updateCertification = new Certification();
            updateCertification.setId(certification.getId());
            //把状态码+2 = 13, 23, 33  是已经跳转过认证成功界面的记录，以后直接跳转到首页。
            updateCertification.setStatus(certification.getStatus() + 2);
            certificationDao.update(updateCertification);
        }
        return certification;
    }

    /**
     * 功能描述:  认证失败-重新认证接口
     * 创建时间:  2020/7/2 11:04 上午
     *
     * @param id: 主键id
     * @return moonstone.user.model.Certification
     * <AUTHOR>
     */
    @Override
    public Integer again(Long id) {
        Certification updateCertification = new Certification();
        //根据用户id修改认证的状态
        updateCertification.setId(id);
        //把认证状态重置为0 未认证，让用户重新选择注册供应商还是分销商。
        updateCertification.setStatus(0);
        Boolean flag = certificationDao.update(updateCertification);
        return updateCertification.getStatus();
    }


}
