package moonstone.order.vo;

import lombok.Data;
import moonstone.order.dto.IdAndName;

import java.io.Serializable;
import java.util.List;

@Data
public class ReceiverPreViewAPPVO implements Serializable {

    /**
     * 地址ID
     */
    Long id;
    /**
     * 用户ID
     */
    Long userId;
    /**
     * 是否默认
     */
    Boolean isDefault;
    /**
     * 手机号
     */
    String mobile;
    /**
     * 省份
     */
    List<IdAndName> province;
    /**
     * 城市
     */
    List<IdAndName> city;
    /**
     * 区域
     */
    List<IdAndName> region;

    /**
     * 街道地址
     */
    String street;
    /**
     * 街道ID
     */
    Integer streetId;
    /**
     * 收货人姓名
     */
    String receiveUserName;
    /**
     * 详细地址
     */
    String detail;

}
