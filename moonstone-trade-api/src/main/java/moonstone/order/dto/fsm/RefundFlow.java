package moonstone.order.dto.fsm;

/**
 * <AUTHOR>
 * Status Flow for Refund
 */
class RefundFlow extends Flow {
    public RefundFlow(String name) {
        super(name);
    }

    /**
     * 配置流程
     */
    @Override
    protected void configure() {
        // 正常退款逻辑
        refundNormalFlow();
        // 正常退货逻辑
        returnNormalFlow();
        // 退款被拒绝逻辑
        refundRejectFlow();
    }

    private void returnNormalFlow() {
        // 退款退货-> 商家同意退款退货
        addTransition(OrderStatus.RETURN_APPLY.getValue(),
                OrderEvent.RETURN_APPLY_AGREE.toOrderOperation(),
                OrderStatus.RETURN_APPLY_AGREED.getValue());

        // 买家申请退货 -> 商家强制同意退款 （货由商家自己线下处理）
        addTransition(OrderStatus.RETURN_APPLY.getValue(),
                OrderEvent.FORCE_REFUND_AGREE.toOrderOperation(),
                OrderStatus.REFUND_APPLY_AGREED.getValue());

        // 退款退货 -> 商家拒绝退款退货
        addTransition(OrderStatus.RETURN_APPLY.getValue(),
                OrderEvent.RETURN_APPLY_REJECT.toOrderOperation(),
                OrderStatus.RETURN_APPLY_REJECTED.getValue());
        // 申请退款退货 -> 已经取消
        addTransition(OrderStatus.RETURN_APPLY.getValue(),
                OrderEvent.RETURN_APPLY_CANCEL.toOrderOperation(),
                OrderStatus.REFUND_CANCEL.getValue());
        // 同意退款退货 -> 已经取消
        addTransition(OrderStatus.RETURN_APPLY_AGREED.getValue(),
                OrderEvent.RETURN_APPLY_CANCEL.toOrderOperation(),
                OrderStatus.REFUND_CANCEL.getValue());
        // 拒绝退款退货 -> 已经取消
        addTransition(OrderStatus.RETURN_APPLY_REJECTED.getValue(),
                OrderEvent.RETURN_APPLY_CANCEL.toOrderOperation(),
                OrderStatus.REFUND_CANCEL.getValue());
        // 商家同意退款退货 -> 买家已退货
        addTransition(OrderStatus.RETURN_APPLY_AGREED.getValue(),
                OrderEvent.RETURN.toOrderOperation(),
                OrderStatus.RETURN.getValue());
        // 买家已退货 -> 商家已确认收退货
        addTransition(OrderStatus.RETURN.getValue(),
                OrderEvent.RETURN_CONFIRM.toOrderOperation(),
                OrderStatus.RETURN_CONFIRMED.getValue());
        // 商家已确定收退货 ->  已退款
        addTransition(OrderStatus.RETURN_CONFIRMED.getValue(),
                OrderEvent.REFUND.toOrderOperation(),
                OrderStatus.REFUND_PROCESSING.getValue());

        // 商家同意退款退货 ->  已退款
        addTransition(OrderStatus.RETURN_APPLY_AGREED.getValue(),
                OrderEvent.REFUND.toOrderOperation(),
                OrderStatus.REFUND_PROCESSING.getValue());
    }

    private void refundNormalFlow() {
        // 在付款后发货前, 买家可申请退款, 商家也可主动退款
        // 正常退款逻辑
        // 已付款 -> 买家申请退款
        addTransition(OrderStatus.PAID.getValue(),
                OrderEvent.REFUND_APPLY.toOrderOperation(),
                OrderStatus.REFUND_APPLY.getValue());
        // 已确定收货 -> 卖家申请退款
        addTransition(OrderStatus.CONFIRMED.getValue(),
                OrderEvent.REFUND_APPLY.toOrderOperation(),
                OrderStatus.REFUND_APPLY.getValue());
        // 买家申请退款 -> 商家同意退款
        addTransition(OrderStatus.REFUND_APPLY.getValue(),
                OrderEvent.REFUND_APPLY_AGREE.toOrderOperation(),
                OrderStatus.REFUND_APPLY_AGREED.getValue());

        // 买家申请退款 -> 商家强制同意退款
        addTransition(OrderStatus.REFUND_APPLY.getValue(),
                OrderEvent.FORCE_REFUND_AGREE.toOrderOperation(),
                OrderStatus.REFUND_APPLY_AGREED.getValue());

        // 商家同意退款 -> 退款处理中
        addTransition(OrderStatus.REFUND_APPLY_AGREED.getValue(),
                OrderEvent.REFUND.toOrderOperation(),
                OrderStatus.REFUND_PROCESSING.getValue());
        // 退款处理中 -> 商家已退款
        addTransition(OrderStatus.REFUND_PROCESSING.getValue(),
                OrderEvent.REFUND_SUCCESS.toOrderOperation(),
                OrderStatus.REFUND.getValue());
        // 取消退款逻辑
        // 买家申请退款 -> 已付款 (买家撤销退款)
        addTransition(OrderStatus.REFUND_APPLY.getValue(),
                OrderEvent.REFUND_APPLY_CANCEL.toOrderOperation(),
                OrderStatus.REFUND_CANCEL.getValue());
        // 商家同意退款 -> 已付款 (买家撤销退款)
        addTransition(OrderStatus.REFUND_APPLY_AGREED.getValue(),
                OrderEvent.REFUND_APPLY_CANCEL.toOrderOperation(),
                OrderStatus.REFUND_CANCEL.getValue());
        // 商家拒绝退款 -> 已付款
        addTransition(OrderStatus.REFUND_APPLY_REJECTED.getValue(),
                OrderEvent.REFUND_APPLY_CANCEL.toOrderOperation(),
                OrderStatus.REFUND_CANCEL.getValue());
        // 重新发起退款
        // 商家拒绝退款 -> 再次申请退款
        addTransition(OrderStatus.REFUND_APPLY_REJECTED.getValue(),
                OrderEvent.REFUND_APPLY.toOrderOperation(),
                OrderStatus.REFUND_APPLY.getValue());

    }

    private void refundRejectFlow() {
        // 拒绝退货
        // 买家已退货 -> 商家拒绝退货
        addTransition(OrderStatus.RETURN.getValue(),
                OrderEvent.RETURN_REJECT.toOrderOperation(),
                OrderStatus.RETURN_REJECTED.getValue());


        // 如果发货了 那就自然走发货流程
        // 商家同意退款 -> 发货
        addTransition(OrderStatus.REFUND_APPLY_AGREED.getValue(),
                OrderEvent.SHIP.toOrderOperation(),
                OrderStatus.REFUND_APPLY_REJECTED.getValue());


        // 退款申请中 允许进入待收货状态, 让用户重新申请退款就好了
        addTransition(OrderStatus.REFUND_APPLY.getValue(),
                OrderEvent.SHIP.toOrderOperation(),
                OrderStatus.REFUND_APPLY_REJECTED.getValue()
        );

        // 拒绝退款
        // 拒绝退款 :: 买家申请退款 -> 已付款
        addTransition(OrderStatus.REFUND_APPLY.getValue(),
                OrderEvent.REFUND_APPLY_REJECT.toOrderOperation(),
                OrderStatus.REFUND_APPLY_REJECTED.getValue());
    }
}
