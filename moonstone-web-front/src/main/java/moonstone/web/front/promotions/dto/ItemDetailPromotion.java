package moonstone.web.front.promotions.dto;

import lombok.Data;
import moonstone.promotion.model.Promotion;

import java.io.Serializable;
import java.util.List;

/**
 * 商品详情要展示的营销活动
 * Created by cp on 4/25/17.
 */
@Data
public class ItemDetailPromotion implements Serializable {

    private static final long serialVersionUID = -103423856197479335L;

    /**
     * 商品优惠券
     */
    private List<CouponState> itemCoupons;

    /**
     * 店铺优惠券
     */
    private List<CouponState> shopCoupons;

    /**
     * 商品(sku)营销
     */
    private List<SkuPromotion> skuPromotions;

    /**
     * 店铺营销(含运费营销，不含用户营销)
     */
    private List<Promotion> shopPromotions;

    /**
     * 每个sku采用默认营销时的价格
     */
    private List<SkuPromotionPrice> skuPricesByDefaultPromotion;

}
