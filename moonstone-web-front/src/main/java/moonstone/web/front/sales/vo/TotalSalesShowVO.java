package moonstone.web.front.sales.vo;


import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TotalSalesShowVO implements Serializable {

    /**
     * 总成交金额GMV
     */
    private Long totalSalesGmv;

    /**
     * 总订单量
     */
    private Long totalSalesOrderQuantity;


    /**
     * 待付款 GMV
     */
    private Long waittingForPaySalesGmv;

    /**
     * 待付款 订单量
     */
    private Long waittingForPaySalesOrderQuantity;

    /**
     * 已付款 GMV
     */
    private Long paidSalesGmv;

    /**
     * 已付款 订单量
     */
    private Long paidSalesOrderQuantity;

    /**
     * 已发货 GMV
     */
    private Long shippedSalesGmv;

    /**
     * 已发货 订单量
     */
    private Long shippedSalesOrderQuantity;

    /**
     * 更新时间
     */
    private Date updateTime;

    //防止add()方法出现空指针异常
    public TotalSalesShowVO() {
        totalSalesGmv = 0L;
        totalSalesOrderQuantity = 0L;
        waittingForPaySalesGmv = 0L;
        waittingForPaySalesOrderQuantity = 0L;
        paidSalesGmv = 0L;
        paidSalesOrderQuantity = 0L;
        shippedSalesGmv = 0L;
        shippedSalesOrderQuantity = 0L;
    }

    public void addTotalSales(long fee){
        totalSalesGmv += fee;
        totalSalesOrderQuantity += 1L;
    }

    public void addWaittingForPaySales(long fee){
        waittingForPaySalesGmv += fee;
        waittingForPaySalesOrderQuantity += 1L;
    }

    public void addPaidSales(long fee){
        paidSalesGmv += fee;
        paidSalesOrderQuantity += 1L;
    }

    public void addShippedSales(long fee){
        shippedSalesGmv += fee;
        shippedSalesOrderQuantity += 1L;
    }
}
