
ALTER TABLE `parana_brands` ADD COLUMN `outer_id` VARCHAR(256) NULL COMMENT '外部 id' AFTER `status`;

alter table parana_delivery_fee_templates add column `low_price` int(11) NULL COMMENT '订单不满该金额时，运费为lowFee' after `fee`,
add column `low_fee` int(11) NULL COMMENT '订单不满low_price时，运费为lowFee' after `low_price`,
add column  `high_price` int(11) NULL COMMENT '订单高于该金额时，运费为highFee' after `low_fee`,
add column  `high_fee` int(11) NULL COMMENT '订单高于high_price时，运费为highFee' after `high_price`,
add column  `middle_fee` int(11) NULL COMMENT '订单价格在lowFee，highFee之间时，运费为middleFee' after `high_fee`;

alter table parana_special_delivery_fees add column `low_price` int(11) NULL COMMENT '订单不满该金额时，运费为lowFee' after `fee`,
add column `low_fee` int(11) NULL COMMENT '订单不满low_price时，运费为lowFee' after `low_price`,
add column  `high_price` int(11) NULL COMMENT '订单高于该金额时，运费为highFee' after `low_fee`,
add column  `high_fee` int(11) NULL COMMENT '订单高于high_price时，运费为highFee' after `high_price`,
add column  `middle_fee` int(11) NULL COMMENT '订单价格在lowFee，highFee之间时，运费为middleFee' after `high_fee`;


ALTER TABLE parana_payments ADD COLUMN `stage` INT NULL COMMENT '分批支付中,表示当前是第几批支付所生成的支付单' AFTER `pay_serial_no`;

ALTER TABLE parana_sku_orders ADD COLUMN `commission_rate` INT default 0 COMMENT '电商平台佣金费率, 万分之一' AFTER `commented`;

ALTER TABLE parana_settle_refund_order_details ADD COLUMN `sum_at` DATETIME  NULL COMMENT '汇总时间' after `check_at`;
ALTER TABLE parana_settle_order_details ADD COLUMN `sum_at` DATETIME  NULL COMMENT '汇总时间' after `check_at`;

ALTER TABLE parana_refunds ADD COLUMN `refund_type`  SMALLINT NOT  NULL default 0 COMMENT '0: 售中退款, 1: 售后退款' after `id`;

ALTER TABLE parana_sku_orders ADD COLUMN `has_apply_after_sale` SMALLINT NULL COMMENT '是否申请过售后' AFTER `commented`;

ALTER TABLE parana_order_comments ADD COLUMN `images_json` TEXT NULL COMMENT '图片信息' AFTER `status`;

alter table parana_settlements add column `extra_json` VARCHAR(2048) NULL COMMENT '附加字段' after `channel_account`;
alter table parana_settle_order_details add column `extra_json` VARCHAR(2048) NULL COMMENT '附加字段' after `sum_at`;
alter table parana_settle_refund_order_details add column `extra_json` VARCHAR(2048) NULL COMMENT '附加字段' after `channel_account`;
alter table parana_pay_channel_details add column `extra_json` VARCHAR(2048) NULL COMMENT '附加字段' after `trade_finished_at`;
alter table parana_pay_channel_daily_summarys add column `extra_json` VARCHAR(2048) NULL COMMENT '附加字段' after `sum_at`;
alter table parana_platform_trade_daily_summarys add column `extra_json` VARCHAR(2048) NULL COMMENT '附加字段' after `sum_at`;
alter table parana_seller_trade_daily_summarys add column `extra_json` VARCHAR(2048) NULL COMMENT '附加字段' after `sum_at`;

alter table parana_settle_order_details add column `check_status`  int  NULL COMMENT '对账状态' after `check_at`;
alter table parana_settle_refund_order_details add column `check_status`  int  NULL COMMENT '对账状态' after `check_at`;

-- 结算重构新增字段
alter table parana_settlements add column `diff_fee` BIGINT(20) NULL COMMENT '差价' after `extra_json`;
alter table parana_settlements add column `commission1` BIGINT(20) NULL COMMENT '附加佣金1' after `diff_fee`;
alter table parana_settlements add column `commission2` BIGINT(20) NULL COMMENT '附加佣金2' after `commission1`;
alter table parana_settlements add column `commission3` BIGINT(20) NULL COMMENT '附加佣金3' after `commission2`;
alter table parana_settlements add column `commission4` BIGINT(20) NULL COMMENT '附加佣金4' after `commission3`;
alter table parana_settlements add column `commission5` BIGINT(20) NULL COMMENT '附加佣金5' after `commission4`;

alter table parana_settle_order_details add column `diff_fee` BIGINT(20) NULL COMMENT '差价' after `extra_json`;
alter table parana_settle_order_details add column `commission1` BIGINT(20) NULL COMMENT '附加佣金1' after `diff_fee`;
alter table parana_settle_order_details add column `commission2` BIGINT(20) NULL COMMENT '附加佣金2' after `commission1`;
alter table parana_settle_order_details add column `commission3` BIGINT(20) NULL COMMENT '附加佣金3' after `commission2`;
alter table parana_settle_order_details add column `commission4` BIGINT(20) NULL COMMENT '附加佣金4' after `commission3`;
alter table parana_settle_order_details add column `commission5` BIGINT(20) NULL COMMENT '附加佣金5' after `commission4`;

alter table parana_settle_refund_order_details add column `diff_fee` BIGINT(20) NULL COMMENT '差价' after `extra_json`;
alter table parana_settle_refund_order_details add column `commission1` BIGINT(20) NULL COMMENT '附加佣金1' after `diff_fee`;
alter table parana_settle_refund_order_details add column `commission2` BIGINT(20) NULL COMMENT '附加佣金2' after `commission1`;
alter table parana_settle_refund_order_details add column `commission3` BIGINT(20) NULL COMMENT '附加佣金3' after `commission2`;
alter table parana_settle_refund_order_details add column `commission4` BIGINT(20) NULL COMMENT '附加佣金4' after `commission3`;
alter table parana_settle_refund_order_details add column `commission5` BIGINT(20) NULL COMMENT '附加佣金5' after `commission4`;

alter table parana_platform_trade_daily_summarys add column `diff_fee` BIGINT(20) NULL COMMENT '差价' after `extra_json`;
alter table parana_platform_trade_daily_summarys add column `commission1` BIGINT(20) NULL COMMENT '附加佣金1' after `diff_fee`;
alter table parana_platform_trade_daily_summarys add column `commission2` BIGINT(20) NULL COMMENT '附加佣金2' after `commission1`;
alter table parana_platform_trade_daily_summarys add column `commission3` BIGINT(20) NULL COMMENT '附加佣金3' after `commission2`;
alter table parana_platform_trade_daily_summarys add column `commission4` BIGINT(20) NULL COMMENT '附加佣金4' after `commission3`;
alter table parana_platform_trade_daily_summarys add column `commission5` BIGINT(20) NULL COMMENT '附加佣金5' after `commission4`;

alter table parana_seller_trade_daily_summarys add column `diff_fee` BIGINT(20) NULL COMMENT '差价' after `extra_json`;
alter table parana_seller_trade_daily_summarys add column `commission1` BIGINT(20) NULL COMMENT '附加佣金1' after `diff_fee`;
alter table parana_seller_trade_daily_summarys add column `commission2` BIGINT(20) NULL COMMENT '附加佣金2' after `commission1`;
alter table parana_seller_trade_daily_summarys add column `commission3` BIGINT(20) NULL COMMENT '附加佣金3' after `commission2`;
alter table parana_seller_trade_daily_summarys add column `commission4` BIGINT(20) NULL COMMENT '附加佣金4' after `commission3`;
alter table parana_seller_trade_daily_summarys add column `commission5` BIGINT(20) NULL COMMENT '附加佣金5' after `commission4`;

alter TABLE parana_shop_orders ADD  COLUMN `diff_fee` INT NULL COMMENT '改价金额' AFTER `commission_rate`;

ALTER TABLE parana_sku_orders ADD COLUMN `diff_fee` INT NULL COMMENT '改价金额' AFTER `commission_rate`;

ALTER TABLE parana_refunds ADD COLUMN `diff_fee` INT NULL COMMENT '改价金额' AFTER `fee`;

alter table parana_settlements add column `trade_business_type` SMALLINT  NULL COMMENT '交易业务类型';

-- 结算字段设计不合理处调整
ALTER TABLE  parana_commission_rules MODIFY COLUMN `business_name` varchar(256) DEFAULT NULL COMMENT '业务名称';

alter table parana_settle_refund_order_details add column `refund_detail_type`  SMALLINT  NULL COMMENT '退款单明细类型';

alter table parana_shop_categories add column `logo` varchar(128) DEFAULT NULL COMMENT '店铺类目logo' AFTER `name`;
