package moonstone.order.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.common.model.Either;
import moonstone.order.dto.WithDrawProfitApplyCriteria;
import moonstone.order.model.WithDrawProfitApply;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WithDrawProfitApplyReadService {

    Response<WithDrawProfitApply> findByPaySerialId(String paySerialId);

    Response<List<WithDrawProfitApply>> findByUserIdAndSourceId(long userId, long sourceId);

    Response<List<WithDrawProfitApply>> findByUserId(long userId);

    Response<WithDrawProfitApply> findById(Long id);

    Response<List<WithDrawProfitApply>> findByIds(List<Long> ids);

    Response<Paging<WithDrawProfitApply>> paging(WithDrawProfitApplyCriteria criteria);

    /**
     * 单独查询分页数据，不计算记录总数
     *
     * @param criteria
     * @return
     */
    Response<List<WithDrawProfitApply>> pageList(WithDrawProfitApplyCriteria criteria);

    Response<Paging<WithDrawProfitApply>> pagingBySearch(WithDrawProfitApplyCriteria criteria);

    /**
     * 查找需要回调查询的
     *
     * @param sourceId 可为null
     * @param limit    可为null
     * @return 需要查询的
     */
    Either<List<WithDrawProfitApply>> getRequireQuery(Long sourceId, Integer limit);

    /**
     * 查询待处理的提现申请数量
     * @param shopId
     * @param subUserId
     * @return
     */
    long getPendingWithdrawalRequestCount(Long shopId, Long subUserId);
}
