package moonstone.web.op.allinpay.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.allinpay.sdk.OpenClient;
import com.allinpay.sdk.bean.BizParameter;
import io.terminus.common.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.utils.CopyUtil;
import moonstone.web.op.allinpay.util.AllinPayUtil;
import moonstone.web.op.allinpay.util.NacosAutoConfiguration;
import moonstone.web.op.allinpay.*;
import moonstone.web.op.allinpay.constant.AllinPayConstant;
import moonstone.web.op.allinpay.dto.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 通联支付实现类
 */
@Service
@Slf4j
@AllArgsConstructor
public class AllinpayServiceImpl implements IAllinpayService {
	private final NacosAutoConfiguration configuration;

	private final AllinPayFactory allinPayFactory;

	@Override
	public Result<String> createMember(Long shopId, CreateMemberDTO dto) {
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.CREATE_MEMBER.getKey();
		String methodDesc = AllinPayMethodEnum.CREATE_MEMBER.getDesc();
		Result<String> response = allinPayFactory.execute(shopId, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		JSONObject obj = JSON.parseObject(response.getData());
		return Result.data(obj.getString("userId"));
	}

	@Override
	public Result<String> createCustomerMember(Long shopId, CreateCustomerMemberDTO dto) {
		//创建会员
		Result<String> member = createMember(shopId, CopyUtil.copy(dto, CreateMemberDTO.class));
		//绑定账户标识
		ApplyBindAcctDTO acctDTO = CopyUtil.copy(dto, ApplyBindAcctDTO.class);
		if(acctDTO == null){
			throw new ServiceException("没有找到绑定信息");
		}
		// 固定为设置支付标识
		if(StringUtils.isNotEmpty(acctDTO.getAcct())){
			acctDTO.setOperationType("set");
			Result<String> acctStr = applyBindAcct(shopId, acctDTO);
		}else{
			log.info("acct为空 不绑定支付标识");
		}
		return member;
	}

	@Override
	public Result<String> sendVerificationCode(Long shopId, VerificationCodeDTO dto) {
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.SEND_VERIFICATION_CODE.getKey();
		String methodDesc = AllinPayMethodEnum.SEND_VERIFICATION_CODE.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> bindPhone(Long shopId, BindPhoneDTO dto) {
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.BIND_PHONE.getKey();
		String methodDesc = AllinPayMethodEnum.BIND_PHONE.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> setRealName(Long shopId, RealNameDTO dto) {
		OpenClient openClient = allinPayFactory.get(shopId);
		// 身份证号加密
		dto.setIdentityNo(openClient.encrypt(dto.getIdentityNo()));
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.SET_REAL_NAME.getKey();
		String methodDesc = AllinPayMethodEnum.SET_REAL_NAME.getDesc();
		return allinPayFactory.execute(openClient, methodName, methodDesc, param);
	}

	@Override
	public Result<String> signAcctProtocol(Long shopId, SignContractDTO dto, AppTypeEnum appTypeEnum) {
		//dto.setJumpUrl(configuration.getValue(AllinPayConstant.SIGN_CONTRACT_JUMP_URL));
		// 签约类型固定为小程序
//		dto.setJumpPageType(AllinPayConstant.JumpPageType.XCX);
		Integer type = getJumpPageType(appTypeEnum);
		dto.setJumpPageType(type);
		dto.setBackUrl(configuration.getValue(AllinPayConstant.SIGN_ACCT_PROTOCOL_URL));
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.SIGN_ACCT_PROTOCOL.getKey();
		String methodDesc = AllinPayMethodEnum.SIGN_ACCT_PROTOCOL.getDesc();
		Result<String> response = allinPayFactory.execute(shopId, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		Map map = JSON.parseObject(response.getData(), Map.class);
		String url = (String) map.get("url");
		return Result.data(url);
	}

	@Override
	public Result<String> signContractQuery(Long shopId, SignContractQueryDTO dto, AppTypeEnum appTypeEnum) {
		Integer type = getJumpPageType(appTypeEnum);
		dto.setJumpPageType(type);

		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.SIGN_CONTRACT_QUERY.getKey();
		String methodDesc = AllinPayMethodEnum.SIGN_CONTRACT_QUERY.getDesc();
		Result<String> response = allinPayFactory.execute(shopId, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		Map map = JSON.parseObject(response.getData(), Map.class);
		String url = (String) map.get("url");
		return Result.data(url);
	}

	private Integer getJumpPageType(AppTypeEnum appTypeEnum) {
		if(AppTypeEnum.ALIPAY.equals(appTypeEnum)){
			// 支付宝不支持小程序
			return AllinPayConstant.JumpPageType.H5;
		}else{
			return AllinPayConstant.JumpPageType.XCX;
		}
	}

	@Override
	public Result<String> getMemberInfo(Long shopId, GetMemberInfoDTO infoDTO) {
		BizParameter param = AllinPayUtil.getMap(infoDTO);
		String methodName = AllinPayMethodEnum.GET_MEMBER_INFO.getKey();
		String methodDesc = AllinPayMethodEnum.GET_MEMBER_INFO.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}


	@Override
	public Result<String> setCompanyInfo(Long shopId, CompanyInfoDTO dto) {
		OpenClient openClient = allinPayFactory.get(shopId);
		dto.setBackUrl(configuration.getValue(AllinPayConstant.COMPANY_BACK_URL));
		// 取消isAuth参数了
//		dto.setIsAuth(Boolean.valueOf(configuration.getValue(AllinPayConstant.COMPANY_AUTH_TYPE)));
		// 只支持统一社会信用（一证）
		dto.getCompanyBasicInfo().setAuthType(2L);
		dto.getCompanyBasicInfo().setAccountNo(openClient.encrypt(dto.getCompanyBasicInfo().getAccountNo()));
		dto.getCompanyBasicInfo().setLegalIds(openClient.encrypt(dto.getCompanyBasicInfo().getLegalIds()));
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.SET_COMPANY_INFO.getKey();
		String methodDesc = AllinPayMethodEnum.SET_COMPANY_INFO.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}
	@Override
	public Result<String> updateCompanyInfo(Long shopId, CompanyInfoDTO dto) {
		OpenClient openClient = allinPayFactory.get(shopId);
		dto.setBackUrl(configuration.getValue(AllinPayConstant.COMPANY_BACK_URL));
		// 取消isAuth参数了
//		dto.setIsAuth(Boolean.valueOf(configuration.getValue(AllinPayConstant.COMPANY_AUTH_TYPE)));
		// 只支持统一社会信用（一证）
		dto.getCompanyBasicInfo().setAuthType(2L);
		dto.getCompanyBasicInfo().setAccountNo(openClient.encrypt(dto.getCompanyBasicInfo().getAccountNo()));
		dto.getCompanyBasicInfo().setLegalIds(openClient.encrypt(dto.getCompanyBasicInfo().getLegalIds()));
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.SET_COMPANY_INFO.getKey();
		String methodDesc = AllinPayMethodEnum.SET_COMPANY_INFO.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> verifyResult(Long shopId, CompanyInfoDTO dto) {
		OpenClient openClient = allinPayFactory.get(shopId);

		dto.setBackUrl(configuration.getValue(AllinPayConstant.COMPANY_BACK_URL));
//		dto.setIsAuth(Boolean.getBoolean(configuration.getValue(AllinPayConstant.COMPANY_AUTH_TYPE)));
		// 只支持统一社会信用（一证）
		dto.getCompanyBasicInfo().setAuthType(2L);
		dto.getCompanyBasicInfo().setAccountNo(openClient.encrypt(dto.getCompanyBasicInfo().getAccountNo()));
		dto.getCompanyBasicInfo().setLegalIds(openClient.encrypt(dto.getCompanyBasicInfo().getLegalIds()));
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.VERIFY_RESULT.getKey();
		String methodDesc = AllinPayMethodEnum.VERIFY_RESULT.getDesc();
		return allinPayFactory.execute(openClient, methodName, methodDesc, param);
	}

	@Override
	public Result<String> getBankCardBin(Long shopId, String cardNo) {
		OpenClient openClient = allinPayFactory.get(shopId);
		BizParameter param = new BizParameter();
		param.put("cardNo", openClient.encrypt(cardNo));
		String methodName = AllinPayMethodEnum.GET_BANK_CARD_BIN.getKey();
		String methodDesc = AllinPayMethodEnum.GET_BANK_CARD_BIN.getDesc();
		return allinPayFactory.execute(openClient, methodName, methodDesc, param);
	}

	@Override
	public Result<String> applyBindBankCard(Long shopId, ApplyBindBankCardDTO dto) {
		OpenClient openClient = allinPayFactory.get(shopId);
		dto.setCardNo(openClient.encrypt(dto.getCardNo()));
		dto.setIdentityNo(openClient.encrypt(dto.getIdentityNo()));
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.APPLY_BIND_BANK_CARD.getKey();
		String methodDesc = AllinPayMethodEnum.APPLY_BIND_BANK_CARD.getDesc();
		Result<String> response = allinPayFactory.execute(openClient, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		JSONObject obj = JSON.parseObject(response.getData());
		return Result.data(obj.getString("tranceNum"));
	}

	@Override
	public Result<String> bindBankCard(Long shopId, BindBankCardDTO dto) {
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.BIND_BANK_CARD.getKey();
		String methodDesc = AllinPayMethodEnum.BIND_BANK_CARD.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> queryBankCard(Long shopId, QueryBankCardDTO dto) {
		OpenClient openClient = allinPayFactory.get(shopId);
		dto.setCardNo(openClient.encrypt(dto.getCardNo()));
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.QUERY_BANK_CARD.getKey();
		String methodDesc = AllinPayMethodEnum.QUERY_BANK_CARD.getDesc();
		return allinPayFactory.execute(openClient, methodName, methodDesc, param);
	}

	@Override
	public OpenClient getOpenClient(Long shopId) {
		OpenClient openClient = allinPayFactory.get(shopId);
		return openClient;
	}

	@Override
	public Result<String> unbindBankCard(Long shopId, UnbindBankCardDTO dto) {
		OpenClient openClient = allinPayFactory.get(shopId);
		dto.setCardNo(openClient.encrypt(dto.getCardNo()));
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.UNBIND_BANK_CARD.getKey();
		String methodDesc = AllinPayMethodEnum.UNBIND_BANK_CARD.getDesc();
		return allinPayFactory.execute(openClient, methodName, methodDesc, param);
	}

	@Override
	public Result<String> lockMember(Long shopId, String bizUserId) {
		BizParameter param = getBizParameter(bizUserId);
		String methodName = AllinPayMethodEnum.LOCK_MEMBER.getKey();
		String methodDesc = AllinPayMethodEnum.LOCK_MEMBER.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> unlockMember(Long shopId, String bizUserId) {
		BizParameter param = getBizParameter(bizUserId);
		String methodName = AllinPayMethodEnum.UNLOCK_MEMBER.getKey();
		String methodDesc = AllinPayMethodEnum.UNLOCK_MEMBER.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	private BizParameter getBizParameter(String bizUserId){
		BizParameter parameter = new BizParameter();
		parameter.put("bizUserId",bizUserId);
		return parameter;
	}

	@Override
	public Result<String> setPayPwd(Long shopId) {
		BizParameter param = AllinPayUtil.getMap(null);
		String methodName = AllinPayMethodEnum.SET_PAY_PWD.getKey();
		String methodDesc = AllinPayMethodEnum.SET_PAY_PWD.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> updatePayPwd(Long shopId) {
		BizParameter param = AllinPayUtil.getMap(null);
		String methodName = AllinPayMethodEnum.UPDATE_PAY_PWD.getKey();
		String methodDesc = AllinPayMethodEnum.UPDATE_PAY_PWD.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> resetPayPwd(Long shopId) {
		BizParameter param = AllinPayUtil.getMap(null);
		String methodName = AllinPayMethodEnum.RESET_PAY_PWD.getKey();
		String methodDesc = AllinPayMethodEnum.RESET_PAY_PWD.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> updatePhoneByPayPwd(Long shopId) {
		BizParameter param = AllinPayUtil.getMap(null);
		String methodName = AllinPayMethodEnum.UPDATE_PHONE_BY_PAY_PWD.getKey();
		String methodDesc = AllinPayMethodEnum.UPDATE_PHONE_BY_PAY_PWD.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> unbindPhone(Long shopId, UnBindPhoneDTO dto) {
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.UNBIND_PHONE.getKey();
		String methodDesc = AllinPayMethodEnum.UNBIND_PHONE.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> applyBindAcct(Long shopId, ApplyBindAcctDTO dto) {
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.APPLY_BIND_ACCT.getKey();
		String methodDesc = AllinPayMethodEnum.APPLY_BIND_ACCT.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> vspTermidService(Long shopId) {
		BizParameter param = AllinPayUtil.getMap(null);
		String methodName = AllinPayMethodEnum.VSP_TERMID_SERVICE.getKey();
		String methodDesc = AllinPayMethodEnum.VSP_TERMID_SERVICE.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> getOrderDetail(Long shopId, String bizOrderNo) {
		BizParameter param = new BizParameter();
		param.put("bizOrderNo",bizOrderNo);
		String methodName = AllinPayMethodEnum.GET_ORDER_DETAIL.getKey();
		String methodDesc = AllinPayMethodEnum.GET_ORDER_DETAIL.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> queryMerchantBalance(Long shopId, String accountSetNo) {
		BizParameter param = new BizParameter();
		param.put("accountSetNo",accountSetNo);
		String methodName = AllinPayMethodEnum.QUERY_MERCHANT_BALANCE.getKey();
		String methodDesc = AllinPayMethodEnum.QUERY_MERCHANT_BALANCE.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> consumeApply(Long shopId, ConsumeApplyDTO dto) {
		dto.setBackUrl(configuration.getValue(AllinPayConstant.PAY_BACK_URL));
		dto.setIndustryCode(AllinPayConfigConstant.INDUSTRY_CODE);
		dto.setIndustryName(AllinPayConfigConstant.INDUSTRY_NAME);
		dto.setSource(AllinPayConstant.Source.PC);
		BizParameter param = AllinPayUtil.getMap(dto);
		final HashMap<String, Object> payMethod = new HashMap<>();
		String keyName = dto.getPayMethod().getKeyName();
		dto.getPayMethod().setKeyName(null);
		payMethod.put(keyName, AllinPayUtil.getMap(dto.getPayMethod()));
		param.put("payMethod", payMethod);
		String methodName = AllinPayMethodEnum.CONSUME_APPLY.getKey();
		String methodDesc = AllinPayMethodEnum.CONSUME_APPLY.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

//	@Override
//	@Transactional(rollbackFor = Exception.class)
//	public Result<String> withdrawApply(AccountWithdrawDTO dto, PmsUser pmsUser) {
//		Long source = Func.toInt(pmsUser.getDeviceType(), 1) == DeviceTypeEnum.WEB.getCode() ?
//			AllinPayConstant.Source.PC : AllinPayConstant.Source.MOBILE;
//		WithdrawApplyDTO withdrawApplyDTO = generateWithdrawApplyDTO(pmsUser, dto);
//		withdrawApplyDTO.setSource(source);
//		BizParameter param = AllinPayUtil.getMap(withdrawApplyDTO);
//		String methodName = AllinPayMethodEnum.WITHDRAW_APPLY.getKey();
//		String methodDesc = AllinPayMethodEnum.WITHDRAW_APPLY.getDesc();
//		Result<String> response = allinPayFactory.execute(methodName, methodDesc, param);
//		if(response == null || !response.isSuccess()){
//			return response;
//		}
//		Map responseMap = JSON.parseObject(response.getData(), Map.class);
//		String payStatus = (String) responseMap.get("payStatus");
//		boolean withdrawing = StringUtils.equals(payStatus, "success") ||
//			StringUtils.equals(payStatus, "pending");
//		if(withdrawing){
//			AccountWithdraw accountWithdraw = new AccountWithdraw();
//			accountWithdraw.setWithdrawId(dto.getWithdrawId());
//			accountWithdraw.setStatus(WithdrawStatus.WITHDRAWING.getKey());
//			accountWithdraw.setThirdOrderNo((String) responseMap.get("orderNo"));
//			LambdaQueryWrapper<AccountWithdraw> updateWrapper = Wrappers.<AccountWithdraw>query().lambda()
//				.eq(AccountWithdraw::getWithdrawId, dto.getWithdrawId());
//			accountWithdrawMapper.update(accountWithdraw, updateWrapper);
//		}
//		return Result.data(response.getData());
//		/*try {
//			final OpenResponse response = AllinPayConnection.client.execute(AllinPayConstant.WITHDRAW_APPLY, param);
//			if (AllinPayConstant.SUBCODE_OK.equals(response.getSubCode())) {
//				String responseData = response.getData();
//				log.info(responseData);
//				Map responseMap = JSON.parseObject(responseData, Map.class);
//				String payStatus = (String) responseMap.get("payStatus");
//				boolean withdrawing = StringUtils.equals(payStatus, "success") ||
//					StringUtils.equals(payStatus, "pending");
//				if(withdrawing){
//					AccountWithdraw accountWithdraw = new AccountWithdraw();
//					accountWithdraw.setWithdrawId(dto.getWithdrawId());
//					accountWithdraw.setRemark((String) responseMap.get("orderNo"));
//					LambdaQueryWrapper<AccountWithdraw> updateWrapper = Wrappers.<AccountWithdraw>query().lambda()
//						.eq(AccountWithdraw::getWithdrawId, dto.getWithdrawId());
//					accountWithdrawMapper.update(accountWithdraw, updateWrapper);
//				}
//				return R.data(responseData);
//			}
//			log.warn("提现申请异常，msg={}",JSONObject.toJSONString(response));
//			return R.fail(response.getMsg() + ":" + response.getSubMsg());
//		} catch (Exception e) {
//			log.warn("提现申请失败,msg={}",e.getMessage());
//			throw new ServiceException("提现申请失败");
//		}*/
//	}
//
//	private WithdrawApplyDTO generateWithdrawApplyDTO(PmsUser pmsUser, AccountWithdrawDTO accountWithdrawDTO){
//		String withdrawId = accountWithdrawDTO.getWithdrawId();
//		String bizUserId = getAccountBindAllinPay(pmsUser).getBizUserId();
//
//		List<AccountWithdraw> withdrawList = accountWithdrawMapper.selectList(Wrappers.<AccountWithdraw>query().lambda()
//			.eq(AccountWithdraw::getWithdrawId, withdrawId));
//		if(CollectionUtil.isEmpty(withdrawList)){
//			throw new ServiceException("没有找到提现申请单");
//		}
//		String tenantId = withdrawList.get(0).getTenantId();
//		Long amt = accountWithdrawDTO.getAmt();
//		String accountSetNo = configuration.getValue(AllinPayConfigConstant.ACCOUNT_SET_NO);
//		Long fee = payBrandTenantService.calculateWithdrawFee(BrandTypeEnum.ALLIN_PAY.getKey(), tenantId, amt);
//		List<AccountBindCard> accountBindCardList = accountBindCardMapper.selectList(Wrappers.<AccountBindCard>query().lambda()
//			.eq(AccountBindCard::getId, accountWithdrawDTO.getCardNo()));
//		if(CollectionUtil.isEmpty(accountBindCardList)){
//			throw new ServiceException("没有找到提现需要的银行卡");
//		}
//		AccountBindCard accountBindCard = accountBindCardList.get(0);
//		String bankCardNo = accountBindCard.getCardNo();
//		long bankCardPro = accountBindCard.getType();
//		WithdrawApplyDTO withdrawApplyDTO = new WithdrawApplyDTO();
//		withdrawApplyDTO.setBizOrderNo(accountWithdrawDTO.getWithdrawId());
//		withdrawApplyDTO.setBizUserId(bizUserId);
//		withdrawApplyDTO.setAccountSetNo(accountSetNo);
//		withdrawApplyDTO.setAmount(amt);
//		withdrawApplyDTO.setFee(fee);
//		withdrawApplyDTO.setValidateType(NumberConstant.ZERO.longValue());
//		withdrawApplyDTO.setBackUrl(configuration.getValue(AllinPayConstant.WITHDRAW_BACK_URL));
//		withdrawApplyDTO.setIndustryCode(AllinPayConfigConstant.INDUSTRY_CODE);
//		withdrawApplyDTO.setIndustryName(AllinPayConfigConstant.INDUSTRY_NAME);
//		withdrawApplyDTO.setWithdrawType(AllinPayConstant.WithdrawType.D0);
//		withdrawApplyDTO.setBankCardNo(AllinPayConnector.client.encrypt(bankCardNo));
//		withdrawApplyDTO.setBankCardPro(bankCardPro);
//		withdrawApplyDTO.setWithdrawType(AllinPayConstant.WithdrawType.D0);
//		return withdrawApplyDTO;
//	}
//
//	private AccountBindAllinPay getAccountBindAllinPay(PmsUser pmsUser){
//		AccountBindAllinPay accountBindAllinPay = accountBindAllinPayService.getAllinPayAccount(pmsUser, false);
//		if(null == accountBindAllinPay){
//			throw new ServiceException("没有开通通联帐户");
//		}
//		return accountBindAllinPay;
//	}

	@Override
	public Result<String> queryBalance(Long shopId, String bizUserId, String accountSetNo) {
//		String accountSetNo = configuration.getValue(AllinPayConfigConstant.ACCOUNT_SET_NO);
		QueryBalanceDTO queryBalanceDTO = new QueryBalanceDTO();
		queryBalanceDTO.setBizUserId(bizUserId);
		queryBalanceDTO.setAccountSetNo(accountSetNo);
		BizParameter param = AllinPayUtil.getMap(queryBalanceDTO);
		String methodName = AllinPayMethodEnum.QUERY_BALANCE.getKey();
		String methodDesc = AllinPayMethodEnum.QUERY_BALANCE.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> orderRefund(Long shopId) {
		OrderRefundDTO orderRefundDTO = new OrderRefundDTO();
		orderRefundDTO.setRefundType("D0");
		orderRefundDTO.setBackUrl("");
		BizParameter param = AllinPayUtil.getMap(orderRefundDTO);
		String methodName = AllinPayMethodEnum.ORDER_REFUND.getKey();
		String methodDesc = AllinPayMethodEnum.ORDER_REFUND.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}

	@Override
	public Result<String> payByPwd(Long shopId, PayByPwdDTO dto) {
		dto.setJumpUrl(configuration.getValue(AllinPayConstant.PAY_BY_PWD_BACK_URL));
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.PAY_BY_PWD.getKey();
		String methodDesc = AllinPayMethodEnum.PAY_BY_PWD.getDesc();
		Result<String> response =  allinPayFactory.execute(shopId, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		Map responseMap = JSON.parseObject(response.getData(), Map.class);
		String payStatus = (String) responseMap.get("payStatus");
		boolean paying = StringUtils.equals(payStatus, "success") ||
			StringUtils.equals(payStatus, "pending");
		if(paying){
			// todo 更改支付状态，回调之后还要改支付状态
		}
		return Result.data("支付中");
	}












	@Override
	public Result<String> registerCompanyMember(Long shopId, RegisterCompanyMemberDTO dto) {
		dto.setBackUrl(configuration.getValue(AllinPayConstant.COMPANY_BACK_URL_H5));
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.REGISTER_COMPANY_MEMBER.getKey();
		String methodDesc = AllinPayMethodEnum.REGISTER_COMPANY_MEMBER.getDesc();
		Result<String> response = allinPayFactory.execute(shopId, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		Map map = JSON.parseObject(response.getData(), Map.class);
		String url = (String) map.get("regInviteLink");
		return Result.data(url);
	}

	@Override
	public Result<String> queryReserveFundBalance(Long shopId, String sysId) {
		QueryReserveFundBalanceDTO queryReserveFundBalanceDTO = new QueryReserveFundBalanceDTO();
		queryReserveFundBalanceDTO.setSysid(sysId);
		BizParameter param = AllinPayUtil.getMap(queryReserveFundBalanceDTO);

		String methodName = AllinPayMethodEnum.QUERY_RESERVE_FUND_BALANCE.getKey();
		String methodDesc = AllinPayMethodEnum.QUERY_RESERVE_FUND_BALANCE.getDesc();
		Result<String> response = allinPayFactory.execute(shopId, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		return response;
	}



	@Override
	public Result<String> queryInExpDetail(Long shopId, QueryInExpDetailDTO dto) {
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.QUERY_IN_EXP_DETAIL.getKey();
		String methodDesc = AllinPayMethodEnum.QUERY_IN_EXP_DETAIL.getDesc();
		Result<String> response =  allinPayFactory.execute(shopId, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		return response;
	}

	@Override
	public Result<String> getOrderSplitRuleListDetail(Long shopId, GetOrderSplitRuleListDetailDTO dto) {
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.GET_ORDER_SPLIT_RULE_LIST_DETAIL.getKey();
		String methodDesc = AllinPayMethodEnum.GET_ORDER_SPLIT_RULE_LIST_DETAIL.getDesc();
		Result<String> response =  allinPayFactory.execute(shopId, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		return response;
	}

	@Override
	public Result<String> getPaymentInformationDetail(Long shopId, GetPaymentInformationDetailDTO dto) {
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.GET_PAYMENT_INFORMATION_DETAIL.getKey();
		String methodDesc = AllinPayMethodEnum.GET_PAYMENT_INFORMATION_DETAIL.getDesc();
		Result<String> response =  allinPayFactory.execute(shopId, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		return response;
	}

	@Override
	public Result<String> getPayeeFundsInTransitDetail(Long shopId, GetPayeeFundsInTransitDetailDTO dto) {
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.GET_PAYEE_FUNDS_IN_TRANSIT_DETAIL.getKey();
		String methodDesc = AllinPayMethodEnum.GET_PAYEE_FUNDS_IN_TRANSIT_DETAIL.getDesc();
		Result<String> response =  allinPayFactory.execute(shopId, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		return response;
	}

	@Override
	public Result<String> queryVSPFund(Long shopId, QueryVSPFundDTO dto) {
		BizParameter param = AllinPayUtil.getMap(dto);
		String methodName = AllinPayMethodEnum.QUERY_VSP_FUND.getKey();
		String methodDesc = AllinPayMethodEnum.QUERY_VSP_FUND.getDesc();
		Result<String> response =  allinPayFactory.execute(shopId, methodName, methodDesc, param);
		if(response == null || !response.isSuccess()){
			return response;
		}
		return response;
	}

	@Override
	public Result<String> getCheckAccountFile(Long shopId, String date, Long fileType) {
		BizParameter param = new BizParameter();
		param.put("date",date);
		param.put("fileType",fileType);
		String methodName = AllinPayMethodEnum.GET_CHECK_ACCOUNT_FILE.getKey();
		String methodDesc = AllinPayMethodEnum.GET_CHECK_ACCOUNT_FILE.getDesc();
		return allinPayFactory.execute(shopId, methodName, methodDesc, param);
	}


}
