import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.model.Shop;
import moonstone.showcase.ShowcaseApplication;
import moonstone.user.impl.dao.StoreProxyDao;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.User;
import moonstone.web.core.CoreWebConfiguration;
import moonstone.web.core.component.UserSessionManager;
import moonstone.web.core.user.StoreProxyManager;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.HashMap;
import java.util.concurrent.CyclicBarrier;

@Slf4j
@ActiveProfiles("dev")
@SpringBootTest(classes = ShowcaseApplication.class)
@RunWith(SpringRunner.class)
@ImportAutoConfiguration(CoreWebConfiguration.class)
public class JedisManagerTest {
    @Autowired
    private
    UserSessionManager userSessionManager;
    @Autowired
    private
    JedisPool jedisPool;
    @Value("${session.redis.prefix:afsession}")
    private
    String prefix;
    @Autowired
    private StoreProxyDao storeProxyDao;
    @Autowired
    private StoreProxyManager storeProxyManager;

    @Test
    public void init() {
        String sessionId = "fuck";
        try (Jedis jedis = jedisPool.getResource()) {
            jedis.set(prefix + ":" + sessionId, "{\"userId\":123}");
        }
        userSessionManager.kickTheUserByUserId(123L);
        assert !userSessionManager.getUserSessionId(123L).isPresent();
    }

    /**
     * 代理读取测试
     */
    @Test
    public void readStoreProxy() {
        for (StoreProxy storeProxy : storeProxyDao.findByShopIdAndLevel(82L, 1)) {
            if (storeProxy.getStatus() > 0) {
                //noinspection OptionalGetWithoutIsPresent
                StoreProxy find = storeProxyManager.getStoreProxyByShopIdAndUserId(storeProxy.getShopId(), storeProxy.getUserId()).get();
                assert JSON.toJSONString(find.toString()).equals(JSON.toJSONString(storeProxy.toString()));
            }
        }
    }

    @Test
    public void regTest() {
        int turn = 500;
        CyclicBarrier barrier = new CyclicBarrier(turn);
        for (int i = 0; i < turn; i++) {
            new Thread(() -> {
                Shop shop = new Shop();
                shop.setId(85L);
                User user = new User();
                user.setId(444L);
                try {
                    barrier.await();
                } catch (Exception ignored) {
                }
                storeProxyManager.regStoreProxy(shop, user, new HashMap<>());
            }).start();
        }
        try {
            Thread.sleep(800);
        } catch (Exception ignored) {
        }
        Assert.assertEquals(1, storeProxyDao.findByShopIdAndLevel(83L, null).size());
    }
}
