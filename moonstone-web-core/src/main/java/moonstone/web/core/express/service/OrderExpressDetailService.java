package moonstone.web.core.express.service;

import moonstone.order.api.ExpressTrackInfo;

import java.util.List;

/**
 * query the order detail service
 */
public interface OrderExpressDetailService {

    default int order(){
        return 9999;
    }
    boolean queryAble(Long orderId);

    List<? extends ExpressTrackInfo> queryOrderExpressDetail(Long orderId);

    default boolean confirmAble(List<? extends ExpressTrackInfo> orderExpressDetails) {
        return false;
    }
}
