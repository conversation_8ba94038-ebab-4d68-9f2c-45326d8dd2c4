package moonstone.web.core.mirror.model.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.model.Either;
import moonstone.common.utils.EventSender;
import moonstone.shop.model.Shop;
import moonstone.user.model.User;
import moonstone.weShop.model.WeShop;
import moonstone.web.core.events.shop.WeShopAuthEvent;
import moonstone.web.core.mirror.model.ShopDetail;
import moonstone.web.core.mirror.model.api.DomainLifeCycle;
import moonstone.web.core.mirror.model.api.DomainStatus;
import moonstone.web.core.mirror.model.api.MirrorShopPersistService;
import org.springframework.beans.BeanUtils;

import java.util.Optional;

@DomainLifeCycle(autoDiscovery = true)
public class ShopMirrorDomain implements MirrorDomain {
    @Setter
    UserMirrorDomain userMirrorDomain;
    @Setter
    Shop shop = new Shop();
    @Setter
    WeShop weShop = new WeShop();
    @Setter
    String source;
    @Getter
    @Setter
    String outShopId;

    @Getter
    ShopStatus status;

    // inject
    MirrorShopPersistService mirrorShopPersistService;
    WeShopCacheHolder weShopCacheHolder;

    @Override
    public String getSource() {
        return source;
    }

    /**
     * 创建店铺
     *
     * @param shopDetail 店铺
     * @return 店铺Id
     */
    public Either<Long> create(ShopDetail shopDetail) {
        // sync the user first
        User user = userMirrorDomain.sync(shopDetail.getUserId()).take();
        wrapWeShopDetail(shopDetail);
        weShop.setUserId(user.getId());
        mirrorShopPersistService.create(weShop, shop, source).take();
        mirrorShopPersistService.updateAccessCode(shop, Optional.ofNullable(shopDetail.getAccessCode()).orElse("webB2C" + shopDetail.getUserId()), shopDetail.getSecret(), source);
        //        .take();
        return Either.ok(shop.getId());
    }

    private void wrapWeShopDetail(ShopDetail shopDetail) {
        weShop.setName(shopDetail.getName());
        // make the appId fulfill at shopPersist
        // weShop.setAppId(null)
        weShop.setStatus(0);
    }

    /**
     * 更新店铺信息
     * 理论上不允许移交店铺
     *
     * @param shopDetail 店铺信息
     * @return 结果
     */
    public Either<Boolean> update(ShopDetail shopDetail) {
        User user = userMirrorDomain.sync(shopDetail.getUserId()).take();
        if (weShop.getId() == null) {
            try {
                weShop = weShopCacheHolder.findByUserIdAndShopId(user.getId(), shop.getId()).orElse(weShop);
            } catch (Exception ignore) {
            }
        }
        if (weShop.getId() == null) {
            return create(shopDetail).map(id -> id > 0);
        }
        // judge should we send the auth event
        if (weShop.getStatus() != null && weShop.getStatus() <= 0) {
            EventSender.sendApplicationEvent(WeShopAuthEvent.builder()
                    .type(mirrorShopPersistService.findTypeByUserId(weShop.getUserId(), source).take())
                    .weShopId(weShop.getId())
                    .userId(weShop.getUserId())
                    .source(source)
                    .targetStatus(shopDetail.getStatus())
                    .reason(shopDetail.getReason()).build());
        }
        // update action
        weShop.setName(shopDetail.getName());
        weShop.setStatus(shopDetail.getStatus());
        if (shopDetail.getStatus() != null && shopDetail.getStatus() < 0) {
            weShop.setReason(shopDetail.getReason());
        }
        weShop.setOutShopCode(outShopId);
        return mirrorShopPersistService.update(shop, weShop, source)
                .ifSuccess(res -> mirrorShopPersistService.findWeShopByOutShopCode(Optional.ofNullable(shopDetail.getShopId())
                        .orElseGet(weShop::getOutShopCode), source)
                        .ifSuccess(info -> BeanUtils.copyProperties(info, weShop)));
    }

    public Either<Boolean> die() {
        weShop.setStatus(ShopStatus.DIE.getStatus());
        return mirrorShopPersistService.update(shop, weShop, source);
    }

    @AllArgsConstructor
    public enum ShopStatus implements DomainStatus {
        ALIVE(1), DIE(-1);

        @Getter
        final int status;
    }
}