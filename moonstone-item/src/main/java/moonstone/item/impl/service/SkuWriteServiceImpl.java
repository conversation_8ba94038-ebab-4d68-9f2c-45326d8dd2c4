package moonstone.item.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.impl.dao.SkuDao;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Author:cp
 * Created on 25/11/2016.
 */
@Service
@Slf4j
@RpcProvider
public class SkuWriteServiceImpl implements SkuWriteService {

    private final SkuDao skuDao;

    @Autowired
    public SkuWriteServiceImpl(SkuDao skuDao) {
        this.skuDao = skuDao;
    }

    @Override
    public Response<Boolean> updateSku(Sku sku) {
        try {
            skuDao.update(sku);
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to update sku:{},cause:{}",
                    sku, Throwables.getStackTraceAsString(e));
            return Response.fail("sku.update.fail");
        }
    }

    @Override
    public Response<Boolean> create(Sku sku) {
        if (!skuDao.create(sku)) {
            throw new RuntimeException("sku创建失败");
        }

        return Response.ok(true);
    }
}
