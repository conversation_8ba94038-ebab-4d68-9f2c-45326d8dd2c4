package moonstone.user.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.ImmutableBiMap;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EncryptHelper;
import moonstone.user.impl.dao.UserCertificationDao;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.UserCertification;
import moonstone.user.service.UserCertificationReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import java.util.*;

/**
 * Created by <PERSON>aiZhy on 2018/11/14.
 */
@Slf4j
@Service
@RpcProvider
public class UserCertificationReadServiceImpl implements UserCertificationReadService {
    @Autowired
    private UserCertificationDao userCertificationDao;

    void decode(@Nullable UserCertification userCertification) {
        if (userCertification == null || userCertification.getPaperName() == null) {
            return;
        }
        // update the old data
        if (!userCertification.getPaperName().startsWith("@")) {
            Map<String, String> map = ImmutableBiMap.of("paperName", userCertification.getPaperName(),
                    "paperNo", userCertification.getPaperNo());
            PayerInfo.Helper.Data data = PayerInfo.Helper.create(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey), map);
            UserCertification update = JsonObject.mapFrom(data.getData()).mapTo(UserCertification.class);
            update.setPaperName("@" + update.getPaperName());
            Map<String, String> e = new HashMap<>(Optional.ofNullable(userCertification.getExtra()).orElse(Collections.emptyMap()));
            e.put("P", data.getPassword());
            update.setExtra(e);
            update.setId(userCertification.getId());
            userCertificationDao.update(update);
            return;
        }
        Map<String, String> map = ImmutableBiMap.of("paperName", userCertification.getPaperName().substring(1),
                "paperNo", userCertification.getPaperNo());
        PayerInfo.Helper.Data data = PayerInfo.Helper.decode(EncryptHelper.instance.exportKey(EncryptHelper.KeyEnu.CommonKey), new PayerInfo.Helper.Data(userCertification.getExtra().get("P"), map));
        UserCertification b = JsonObject.mapFrom(data.getData()).mapTo(UserCertification.class);
        userCertification.setPaperNo(b.getPaperNo());
        userCertification.setPaperName(b.getPaperName());
    }

    @Override
    public Response<List<UserCertification>> findByUserId(Long userId) {
        try {
            List<UserCertification> userCertifications = userCertificationDao.findByUserId(userId);
            for (UserCertification userCertification : userCertifications) {
                decode(userCertification);
            }
            return Response.ok(userCertifications);
        } catch (Exception e) {
            log.error("failed to find user certification by userId = {}, cause: {}", userId,
                    Throwables.getStackTraceAsString(e));
            return Response.fail("user.certification.find.fail");
        }
    }

    @Override
    public Response<UserCertification> findById(Long id) {
        try {
            final UserCertification userCertification = userCertificationDao.findById(id);
            if (userCertification == null) {
                log.error("no user certification (id={}) found ", id);
                return Response.fail("user.certification.not.found");
            }
            decode(userCertification);
            return Response.ok(userCertification);
        } catch (Exception e) {
            log.error("failed to find certification by id = {}, cause: {}", id,
                    Throwables.getStackTraceAsString(e));
            return Response.fail("user.certification.find.fail");
        }
    }

    @Override
    public Response<Optional<UserCertification>> findDefaultByUserId(Long userId) {
        try {
            UserCertification userCertification = userCertificationDao.findDefaultByUserId(userId);
            decode(userCertification);
            return Response.ok(Optional.ofNullable(userCertification));
        } catch (Exception e) {
            log.error("fail to find default user certification by userId={},cause:{}",
                    userId, Throwables.getStackTraceAsString(e));
            return Response.fail("default.user.certification.find.fail");
        }
    }

    @Override
    public Response<Boolean> existsDefault(Long userId) {
        try {
            UserCertification userCertification = userCertificationDao.findDefaultByUserId(userId);
            return Response.ok(userCertification != null);
        } catch (Exception e) {
            log.error("UserCertificationReadServiceImpl.existsDefault error, userId={} ", userId, e);
            return Response.fail(e.getMessage());
        }
    }
}
