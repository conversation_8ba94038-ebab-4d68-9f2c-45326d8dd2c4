<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="OrderInvoice">

    <resultMap id="OrderInvoiceMap" type="OrderInvoice">
        <id column="id" property="id"/>
        <result column="invoice_id" property="invoiceId"/>
        <result column="order_id" property="orderId"/>
        <result column="order_type" property="orderType"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_order_invoices
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `invoice_id`,`order_id`,`order_type`,`status`,`created_at`,`updated_at`
    </sql>

    <sql id="vals">
        #{invoiceId},#{orderId},#{orderType},#{status},now(),now()
    </sql>

    <insert id="create" parameterType="OrderInvoice" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findById" parameterType="long" resultMap="OrderInvoiceMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByOrderIdAndOrderType" parameterType="map" resultMap="OrderInvoiceMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE order_id = #{orderId} and order_type=#{orderType}
        ORDER BY `id` DESC
    </select>


    <update id="updateStatus" parameterType="OrderInvoice">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now()
            <if test="status != null">,`status` = #{status}</if>
        </set>
        WHERE id=#{id}
    </update>


    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <delete id="deleteByOrderIdAndOrderType" parameterType="map">
        DELETE FROM
        <include refid="tb"/>
        WHERE order_id = #{orderId} and order_type=#{orderType}
    </delete>


</mapper>