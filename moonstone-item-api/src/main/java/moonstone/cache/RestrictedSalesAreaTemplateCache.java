package moonstone.cache;

import moonstone.item.model.RestrictedSalesAreaTemplate;
import moonstone.item.service.RestrictedSalesAreaTemplateReadService;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 都搞成单实例了，随便了
 */
@Component
public class RestrictedSalesAreaTemplateCache {

    @Resource
    private RestrictedSalesAreaTemplateReadService restrictedSalesAreaTemplateReadService;

    @Resource
    private RedissonClient redisson;

    private static final String BUCKET_NAME = "parana:restrictedSalesAreaTemplate:%s";

    public RestrictedSalesAreaTemplate findById(Long id) {
        RBucket<RestrictedSalesAreaTemplate> bucket = redisson.getBucket(BUCKET_NAME.formatted(id));
        var target = bucket.get();
        if (target != null) {
            return target;
        }

        target = restrictedSalesAreaTemplateReadService.findById(id).getResult();
        if (target != null) {
            bucket.set(target, 15, TimeUnit.SECONDS);
        }


        return target;
    }

    public void invalidate(Long id) {
        redisson.getBucket(BUCKET_NAME.formatted(id)).delete();
    }
}
