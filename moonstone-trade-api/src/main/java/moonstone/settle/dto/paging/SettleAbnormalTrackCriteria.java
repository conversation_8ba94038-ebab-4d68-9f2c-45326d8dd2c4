package moonstone.settle.dto.paging;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;

/**
 * Created with IntelliJ IDEA
 * Author: songrenfei
 * Date: 7/30/16
 * Time: 9:48 AM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SettleAbnormalTrackCriteria extends PagingCriteria {
    private static final long serialVersionUID = -8079096857473407085L;

    /**
     * 异常类型 {@link moonstone.settle.enums.AbnormalType}
     */
    private Integer abnormalType;


}
