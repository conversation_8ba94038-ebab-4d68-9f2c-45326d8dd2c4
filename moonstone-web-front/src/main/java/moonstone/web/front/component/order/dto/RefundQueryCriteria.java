package moonstone.web.front.component.order.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class RefundQueryCriteria implements Serializable {
    @Serial
    private static final long serialVersionUID = -5661261704406972740L;

    /**
     * 服务商平台id
     */
    private Long shopId;

    /**
     * 前端标签页的退款状态（退款中=REFUND_IN_PROGRESS，已退款=REFUND_SUCCESS，退款失败=REFUND_FAIL）
     */
    private String refundStatus;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 分页大小
     */
    private Integer pageSize;
}
