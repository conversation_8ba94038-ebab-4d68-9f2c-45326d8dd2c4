package moonstone.web.core.shop.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.Translate;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.user.criteria.UserRelationEntityCriteria;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.model.view.UserLevelView;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.user.service.UserRelationEntityWriteService;
import moonstone.web.core.shop.cache.event.ServiceProviderUpdateEvent;
import moonstone.web.core.shop.model.ServiceProvider;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ServiceProviderCache {
    LoadingCache<ShopIdAndUserId, Optional<UserLevelView>> cache = Caffeine
            .newBuilder().maximumSize(1000)
            .expireAfterWrite(3, TimeUnit.MINUTES)
            .build(this::cacheServiceProviderByShopIdAndUserId);

    LoadingCache<ShopIdAndUserId, Optional<List<Long>>> subStoreIdBelongCache =
            Caffeine.newBuilder().maximumSize(100)
                    .expireAfterWrite(3, TimeUnit.MINUTES)
                    .build(this::cacheSubStoreBelongByShopIdAndUserId);

    LoadingCache<ShopIdAndUserId, ServiceProvider> serviceProviderCache =
            Caffeine.newBuilder().maximumSize(100)
                    .expireAfterWrite(3, TimeUnit.MINUTES).build(this::findByMongo);

    private ServiceProvider findByMongo(ShopIdAndUserId shopIdAndUserId) {
        return mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(shopIdAndUserId.getShopId()))
                        .addCriteria(Criteria.where("userId").is(shopIdAndUserId.getUserId()))
                , ServiceProvider.class);
    }

    @Autowired
    MongoTemplate mongoTemplate;
    @Resource
    UserRelationEntityWriteService writeService;

    public ServiceProvider findServiceProviderByUserIdAndShopId(Long userId, Long shopId) {
        return serviceProviderCache.get(new ShopIdAndUserId(shopId, userId));
    }

    public List<Long> findServiceProviderUserIdByShopId(Long shopId) {
        return sql.selectList("UserRelationEntity.findUserIdForServiceProviderByShopId",
                Map.of("shopId", shopId,
                        "level", 1,
                        "type", 4));
    }

    private Optional<List<Long>> cacheSubStoreBelongByShopIdAndUserId(ShopIdAndUserId shopIdAndUserId) {
        UserRelationEntityCriteria criteria = new UserRelationEntityCriteria();
        criteria.setRelationId(shopIdAndUserId.getUserId());
        criteria.setRelationIdA(Collections.singletonList(shopIdAndUserId.getShopId()));
        criteria.setType(4);
        criteria.setPageSize(Integer.MAX_VALUE);
        return Optional.ofNullable(userRelationEntityReadService.paging(criteria))
                .map(Response::getResult)
                .map(Paging::getData)
                .map(list -> list.stream().map(UserRelationEntity::getUserId).collect(Collectors.toList()));
    }

    @Resource
    UserRelationEntityReadService userRelationEntityReadService;

    @Autowired
    SqlSession sql;
    @Resource
    ShopReadService shopReadService;

    private Optional<UserLevelView> cacheServiceProviderByShopIdAndUserId(@NonNull ShopIdAndUserId shopIdAndUserId) {
        Shop shop = shopReadService.findById(shopIdAndUserId.getShopId()).getResult();
        if (shop == null) {
            throw Translate.exceptionOf("店铺[%s]不存在", shopIdAndUserId.getShopId());
        }
        UserRelationEntityCriteria criteria = new UserRelationEntityCriteria();
        criteria.setType(UserRelationEntity.UserRelationType.SUPER.getType());
        criteria.setUserId(shopIdAndUserId.getUserId());
        criteria.setRelationId(shop.getUserId());
        criteria.setRelationIdA(Collections.singletonList(shop.getId()));
        var userLevel = userRelationEntityReadService.paging(criteria).getResult().getData().stream()
                .map(UserLevelView::new)
                .findFirst();
        if (userLevel.isPresent()) {
            var provider = mongoTemplate.findOne(Query.query(Criteria.where("userId").is(shopIdAndUserId.getUserId()))
                            .addCriteria(Criteria.where("shopId").is(shopIdAndUserId.getShopId()))
                    , ServiceProvider.class);
            if (provider != null) {
                var update = new UserRelationEntity();
                update.setId(userLevel.get().getRelation().getId());
                update.setExtra(userLevel.get().getRelation().getExtra());
                update.setStatus(null);
                update.getExtra().put("name", provider.getName());
                writeService.update(update);
                userLevel.ifPresent(view -> view.getRelation().getExtra().put("name", provider.getName()));
            }
        }
        return userLevel;
    }

    public void invalidate(long shopId, long userId) {
        subStoreIdBelongCache.invalidate(new ShopIdAndUserId(shopId, userId));
        cache.invalidate(new ShopIdAndUserId(shopId, userId));
        serviceProviderCache.invalidate(new ShopIdAndUserId(shopId, userId));
    }

    public Optional<UserLevelView> findServiceProviderByShopIdAndUserId(long shopId, long userId) {
        return cache.get(new ShopIdAndUserId(shopId, userId));
    }

    public Optional<List<Long>> findBelongSubStoreUserId(Long shopId, Long userId) {
        return subStoreIdBelongCache.get(new ShopIdAndUserId(shopId, userId));
    }

    @EventListener(ServiceProviderUpdateEvent.class)
    public void updateCache(ServiceProviderUpdateEvent event) {
        UserLevelView view = new UserLevelView(userRelationEntityReadService.findById(event.getId()).getResult());
        invalidate(view.getShopId(), view.getUserId());
        serviceProviderCache.get(new ShopIdAndUserId(view.getShopId(), view.getUserId()));
    }

    public Optional<UserLevelView> findByShopIdAndUserId(Long shopId, Long userId) {
        UserRelationEntityCriteria criteria = new UserRelationEntityCriteria();
        criteria.setType(UserRelationEntity.UserRelationType.SUPER.getType());
        criteria.setUserId(userId);
        criteria.setRelationIdA(Collections.singletonList(shopId));
        return userRelationEntityReadService.paging(criteria).getResult().getData().stream()
                .map(UserLevelView::new)
                .findFirst();
    }

    public List<ServiceProvider> pagingCache(Long shopId, String name, String mobile) {
        Query query = Query.query(Criteria.where("shopId").is(shopId));
        if (!ObjectUtils.isEmpty(name)) {
            query.addCriteria(Criteria.where("name").regex(name));
        }
        if (!ObjectUtils.isEmpty(mobile)) {
            query.addCriteria(Criteria.where("mobile").regex(mobile));
        }
        return mongoTemplate.find(query, ServiceProvider.class);
    }

    /**
     * 根据 门店的用户id 和 shopId 查询其对应的服务提供商
     */
    public ServiceProvider findBySubStoreUserIdAndShopId(Long subStoreUserId, Long shopId) {
        var userLevelView = findByShopIdAndUserId(shopId, subStoreUserId);
        if (userLevelView.isEmpty()) {
            return null;
        }

        return findServiceProviderByUserIdAndShopId(userLevelView.get().getSupperUserId(), shopId);
    }

    public ServiceProvider findByMongo(Long userId, Long shopId) {
        return findByMongo(new ShopIdAndUserId(shopId, userId));
    }

    public List<ServiceProvider> findByMongo(List<Long> userIdList, Long shopId) {
        return mongoTemplate.find(Query.query(Criteria.where("shopId").is(shopId))
                        .addCriteria(Criteria.where("userId").in(userIdList))
                , ServiceProvider.class);
    }

    public boolean delete(Long userId, Long shopId) {
        Query query = Query.query(Criteria.where("shopId").is(shopId))
                .addCriteria(Criteria.where("userId").is(userId));
        return mongoTemplate.remove(query, ServiceProvider.class).getDeletedCount() > 0;
    }

    /**
     * 根据名称进行模糊查询
     *
     * @param shopId
     * @param name
     * @return
     */
    public List<ServiceProvider> findByName(Long shopId, String name) {
        if (shopId == null && StringUtils.isBlank(name)) {
            return Collections.emptyList();
        }

        Pattern pattern = Pattern.compile("^.*" + name + ".*$", Pattern.CASE_INSENSITIVE);
        Query query = Query.query(Criteria.where("shopId").is(shopId))
                .addCriteria(Criteria.where("name").regex(pattern));

        return mongoTemplate.find(query, ServiceProvider.class);
    }

    /**
     * 根据id查询
     *
     * @param idList
     * @return
     */
    public List<ServiceProvider> findByIds(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }

        return mongoTemplate.find(Query.query(Criteria.where("_id").in(idList)), ServiceProvider.class);
    }

    /**
     * 根据名称进行模糊查询，返回对应的userId
     *
     * @param shopId
     * @param name
     * @return
     */
    public List<Long> findUserIdsByName(Long shopId, String name) {
        var list = findByName(shopId, name);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream().map(ServiceProvider::getUserId).collect(Collectors.toList());
    }

    /**
     * 查询服务商的名称
     *
     * @param serviceProviderUserIds
     * @param shopId
     * @return key=服务商的userId, value=服务商名称
     */
    public Map<Long, String> findServiceProviderMap(List<Long> serviceProviderUserIds, Long shopId) {
        if (CollectionUtils.isEmpty(serviceProviderUserIds)) {
            return Collections.emptyMap();
        }

        var list = mongoTemplate.find(Query.query(Criteria.where("shopId").is(shopId)
                .andOperator(Criteria.where("userId").in(serviceProviderUserIds))), ServiceProvider.class);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(ServiceProvider::getUserId, ServiceProvider::getName, (k1, k2) -> k1));
    }

    /**
     * 查询服务商的名称
     *
     * @param serviceProviderUserIds
     * @param shopId
     * @return key=服务商的userId, value=服务商名称
     */
    public Map<Long, ServiceProvider> findMapByUserIds(List<Long> serviceProviderUserIds, Long shopId) {
        if (CollectionUtils.isEmpty(serviceProviderUserIds)) {
            return Collections.emptyMap();
        }

        var list = mongoTemplate.find(Query.query(Criteria.where("shopId").is(shopId)
                .andOperator(Criteria.where("userId").in(serviceProviderUserIds))), ServiceProvider.class);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(ServiceProvider::getUserId, o -> o, (k1, k2) -> k1));
    }

    @AllArgsConstructor
    @Data
    @NoArgsConstructor
    private static class ShopIdAndUserId {
        Long shopId;
        Long userId;
    }
}
