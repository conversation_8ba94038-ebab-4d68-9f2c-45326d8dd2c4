package moonstone.order.service;

import io.terminus.common.model.Response;
import moonstone.order.dto.AgentPayOrderCriteria;
import moonstone.order.model.AgentPayOrder;

import java.util.List;
import java.util.Map;

public interface AgentPayOrderReadService {

    Response<List<AgentPayOrder>> findByRelatedOrderId(Long relatedOrderId);

    Response<List<AgentPayOrder>> findByRelatedOrderIds(List<Long> relatedOrderIds);

    Response<AgentPayOrder> findById(Long agentPayOrderId);

    Response<List<AgentPayOrder>> pageList(AgentPayOrderCriteria criteria);

    /**
     * @param shopOrderIds
     * @return key = shopOrderIds
     */
    Map<Long, AgentPayOrder> findMapByShopOrderIds(List<Long> shopOrderIds);
}
