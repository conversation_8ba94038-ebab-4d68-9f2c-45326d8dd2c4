package moonstone.web.front.order;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.R;
import moonstone.common.utils.UserUtil;
import moonstone.order.dto.OrderCriteria;
import moonstone.web.core.exports.common.Exporter;
import moonstone.web.front.component.dto.OrderExportIntoDepot;
import moonstone.web.front.order.component.CustomOrderExporter;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

@RestController
@Slf4j
@RequestMapping("/api/order/export")
@AllArgsConstructor
public class CustomDepotOrderExportController {
    private final CustomOrderExporter customOrderExporter;
    private final Exporter exporter;

    @GetMapping("/custom-depot-order")
    public void export(OrderCriteria orderCriteria, HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws Exception {
        if (Objects.isNull(orderCriteria.getShopId())) {
            CommonUser commonUser = UserUtil.getCurrentUser();
            if (Objects.isNull(commonUser)) {
                httpServletResponse.getOutputStream().write(JSON.toJSONBytes(R.error(-1001, "未登录")));
                return;
            }
            orderCriteria.setShopId(commonUser.getShopId());
        }
        exporter.export(customOrderExporter.queryOrderExport(orderCriteria), OrderExportIntoDepot.class, httpServletRequest, httpServletResponse);
    }
}
