package moonstone.order.vo;

import lombok.Data;
import moonstone.common.model.TotalModelVO;

import java.util.List;

@Data
public class OrderListAPPVO {

    Long id;
    Integer status;
    String operatra;//操作码
    List<String> operatraName;//操作url和方法
    List<ItemsVO> itemList;
    TotalModelVO sumTotal;//包含合计-quantity : 3 ,//件数price : 100 ,//总价格 tax : 100 //税费 //shipFee 运费
    String showDetail;
}
