package moonstone.web.front.component.user;

import com.google.common.base.Objects;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.ParanaConstants;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.enums.UserStatus;
import moonstone.user.model.User;
import moonstone.user.model.UserWx;
import moonstone.user.service.AdminUserService;
import moonstone.user.service.UserRelationEntityWriteService;
import moonstone.user.service.UserWriteService;
import moonstone.user.service.UserWxWriteService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * Author:  CaiZhy
 * Date:    2019/2/12
 */
@Slf4j
@Component
public class UserWriteLogic {
    @RpcConsumer
    private UserWriteService<User> userWriteService;

    @RpcConsumer
    private UserWxWriteService userWxWriteService;

    @RpcConsumer
    private AdminUserService adminUserService;

    @Resource
    private UserRelationEntityWriteService userRelationEntityWriteService;

    /**
     *
     * @param userWxByOpenId    当前登录信息对应的user_wx信息
     * @param userByWx          userWxByOpenId指向的user信息
     * @param userByMobile      要绑定的手机号被其他用户持有时，持有者的user信息
     * @param mobile            要绑定的手机号
     * @return
     */
    @Transactional
    public Long wxUserBindUser(UserWx userWxByOpenId, User userByWx, User userByMobile, String mobile) {
        Long resultUserId;
        if (userByMobile == null) {
            log.info("绑定手机号 该手机号无其他用户在用 {}", mobile);
            User user = new User();
            user.setId(userWxByOpenId.getUserId());
            user.setMobile(mobile);
            Response<Boolean> rUpdateUser = userWriteService.update(user);
            if (!rUpdateUser.isSuccess() || !rUpdateUser.getResult()) {
                log.error("failed to update user mobile by id={}, mobile={}, error code: {}", userWxByOpenId.getUserId(), mobile, rUpdateUser.getError());
                throw new JsonResponseException("user.update.fail");
            }
            resultUserId = user.getId();
        } else if (Objects.equal(userByMobile.getId(), userWxByOpenId.getUserId())) {
            log.info("绑定手机号 该手机号无其他用户在用 且是当前登录用户 {}", mobile);
            resultUserId = userByMobile.getId();
        } else {
            log.info("绑定手机号 该手机号被其他用户持有 且非当前登录用户 {}", mobile);
            //删除之前绑定的数据
            // 删除手机号持有者 在当前登录的appId下的user_wx信息
            userWxWriteService.removeByUserIdAndAppIdAndAppType(userByMobile.getId(), userWxByOpenId.getAppId(), AppTypeEnum.from(userWxByOpenId.getAppType()));

            //微信用户改绑手机用户
            // 修改当前登录信息的user_wx的userId为：原手机号持有者的userId
            UserWx toUpdateUserWx = new UserWx();
            toUpdateUserWx.setId(userWxByOpenId.getId());
            toUpdateUserWx.setUserId(userByMobile.getId());
            Response<Boolean> rUpdateUserWx = userWxWriteService.update(toUpdateUserWx);
            if (!rUpdateUserWx.isSuccess() || !rUpdateUserWx.getResult()) {
                log.error("failed to update userWx by id={}, userId={}, error code: {}", userWxByOpenId.getId(), userByMobile.getId(), rUpdateUserWx.getError());
                throw new JsonResponseException(rUpdateUserWx.getError());
            }
            //删除微信用户绑定的用户
            // 根据当前登录的user_wx指向的user_id禁用user信息
            User user = new User();
            user.setId(userWxByOpenId.getUserId());
            user.setStatus(UserStatus.DELETED.value());
            Response<Boolean> rUpdateUser = userWriteService.update(user);
            if (!rUpdateUser.isSuccess() || !rUpdateUser.getResult()) {
                log.error("failed to delete user by id={}, error code: {}", userWxByOpenId.getUserId(), rUpdateUser.getError());
            }
            userRelationEntityWriteService.deleteByUserId(user.getId());
            resultUserId = userByMobile.getId();
        }
        Map<String, String> tags = userByWx.getTags();
        if (tags == null) {
            tags = new HashMap<>();
        }
        tags.put(ParanaConstants.USER_IS_WX_USER, "true");
        tags.put(ParanaConstants.USER_IS_NEW_WX_USER, "false");
        Response<Boolean> response = adminUserService.updateTags(resultUserId, tags);
        if (!response.isSuccess() || !response.getResult()) {
            log.error("failed to update user(id={}) tags to ({}), error code: {}", resultUserId, tags, response.getError());
            throw new JsonResponseException(response.getError());
        }
        return resultUserId;
    }
}
