package moonstone.weDistributionApplication.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.weDistributionApplication.model.WeDistributionApplication;

import java.util.List;

/**
 * Created by <PERSON>ai<PERSON>hy on 2018/12/3.
 */
public interface WeDistributionApplicationReadService {
    Response<WeDistributionApplication> findById(Long id);

    Response<List<WeDistributionApplication>> findByWeShopId(Long weShopId);

    Response<List<WeDistributionApplication>> findByWeShopIdAuditing(Long weShopId);

    Response<List<WeDistributionApplication>> findByUserId(Long userId);

    Response<List<WeDistributionApplication>> findByRecommendShopId(Long recommendShopId);

    Response<List<WeDistributionApplication>> findByRecommendUserId(Long recommendUserId);

    Response<WeDistributionApplication> findByWeShopIdRecommendAuditing(Long weShopId, Integer recommendType,
                                                                Long recommendShopId, Long recommendUserId);

    Response<Paging<WeDistributionApplication>> paging(List<Long> ids,
                                                       Long weShopId,
                                                       List<Long> userIds,
                                                       String mobile,
                                                       Long shopId,
                                                       Integer recommendType,
                                                       Long recommendShopId,
                                                       Long recommendUserId,
                                                       Integer status,
                                                       String statuses,
                                                       Integer pageNo,
                                                       Integer pageSize);
}
