/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.interceptors;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Objects;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.enums.UserStatus;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.SimpleMetricsHelper;
import moonstone.common.utils.UserOpenIdUtil;
import moonstone.common.utils.UserUtil;
import moonstone.shop.model.Shop;
import moonstone.user.cache.SubSellerCacheHolder;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.ext.UserTypeBean;
import moonstone.user.model.SubSeller;
import moonstone.user.model.User;
import moonstone.web.core.AppConstants;
import moonstone.web.core.component.UserSessionManager;
import moonstone.web.core.component.user.LoginUserWrapper;
import moonstone.web.core.component.wx.ShopIdComposerByWxOpenId;
import moonstone.web.core.util.ParanaUserMaker;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.Optional;

@Slf4j
public class ParanaLoginInterceptor implements HandlerInterceptor {

    @Autowired
    private UserTypeBean userTypeBean;

    @Autowired(required = false)
    private LoginUserWrapper loginUserWrapper;

    @Autowired
    private ShopIdComposerByWxOpenId shopIdComposerByWxOpenId;
    @Autowired
    private UserSessionManager userSessionManager;

    @Autowired
    private UserCacheHolder userCacheHolder;
    @Autowired
    private ShopCacheHolder shopCacheHolder;
    @Autowired
    private SubSellerCacheHolder subSellerCacheHolder;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        SimpleMetricsHelper.requestStart();
        // Session and UserSession judge
        HttpSession session = request.getSession(false);
        if (session == null) {
            return true;
        }
        UserUtil.putCurrentSession(session);
        Object userIdInSession = session.getAttribute(AppConstants.SESSION_USER_ID);
        log.debug("ParanaLoginInterceptor.preHandle, uri={}, method={}, userId={}, parameterMap={}", request.getRequestURI(),
                request.getMethod(), userIdInSession, JSON.toJSONString(request.getParameterMap()));
        if (userIdInSession == null) {
            return true;
        }
        try {
            Long.parseLong(userIdInSession.toString());
        } catch (Exception e) {
            session.invalidate();
            return true;
        }
        userSessionManager.login(session);
        final Long userId = Long.valueOf(userIdInSession.toString());
        User user = userCacheHolder.findByUserId(userId).orElse(null);
        if (user == null) {
            return true;
        }
        // user valid judge
        if (Objects.equal(user.getStatus(), UserStatus.DELETED.value()) ||
                Objects.equal(user.getStatus(), UserStatus.FROZEN.value()) ||
                Objects.equal(user.getStatus(), UserStatus.LOCKED.value())) {
            session.invalidate();
            return false;
        }
        // user entity pack
        CommonUser commonUser = ParanaUserMaker.from(user);
        Long userIdOfShop = Optional.of(user).filter(userTypeBean::isSeller).map(User::getId)
                .orElseGet(() -> Optional.of(user).filter(userTypeBean::isSubAccount).map(User::getId).flatMap(subSellerCacheHolder::findSubSellerByUserId)
                        .map(SubSeller::getMasterUserId).orElse(null));

        Optional.ofNullable(userIdOfShop).map(shopCacheHolder::findShopByUserId).map(Shop::getId).ifPresent(commonUser::setShopId);

        Object openIdInSession = session.getAttribute(AppConstants.SESSION_OPEN_ID);
        if (openIdInSession != null) {
            UserOpenIdUtil.putOpenId(openIdInSession.toString());
            shopIdComposerByWxOpenId.setCurrentShopId(commonUser, openIdInSession.toString());
        }
        UserUtil.putCurrentUser(wrap(commonUser));
        UserUtil.putCurrentSession(session);
        return true;
    }

    private CommonUser wrap(CommonUser commonUser) {
        if (loginUserWrapper == null) {
            return commonUser;
        }
        return loginUserWrapper.wrap(commonUser);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        UserUtil.clearCurrentUser();
        UserOpenIdUtil.clearOpenId();
        if (SimpleMetricsHelper.passed() > 200) {
            String bean = handler instanceof HandlerMethod handlerMethod ?
                    handlerMethod.getBean().getClass().getName() : handler.getClass().getName();
            if (ObjectUtils.isEmpty(bean) && bean.contains("$")) {
                bean = bean.split("\\$")[0];
            }
            String method = handler instanceof HandlerMethod handlerMethod ?
                    handlerMethod.getMethod().getName() : "???";
            log.warn("Api[{} by {}] cost[{}] is not cool now"
                    , request.getRequestURI()
                    , bean + "#" + method
                    , SimpleMetricsHelper.passed());
        }

        SimpleMetricsHelper.clear();
    }
}
