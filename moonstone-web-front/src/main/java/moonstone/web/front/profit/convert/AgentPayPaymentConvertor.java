package moonstone.web.front.profit.convert;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.DataValidEnum;
import moonstone.common.utils.DateUtil;
import moonstone.order.enu.AgentPayPaymentStatusEnum;
import moonstone.order.model.AgentPayOrder;
import moonstone.order.model.AgentPayPayment;
import org.springframework.stereotype.Component;

import java.util.Date;

@Slf4j
@Component
public class AgentPayPaymentConvertor {

    public AgentPayPayment convert(AgentPayOrder agentPayOrder) {
        var target = new AgentPayPayment();

        target.setAgentPayOrderId(agentPayOrder.getId());
        target.setAmount(agentPayOrder.getAmount());
        target.setCreatedBy(0L);
        target.setIsValid(DataValidEnum.VALID.getCode());

        target.setPayChannel(agentPayOrder.getPayChannel());
        target.setShopId(agentPayOrder.getShopId());
        target.setStatus(AgentPayPaymentStatusEnum.NOT_PAID.getCode());
        target.setUpdatedBy(0L);

        return target;
    }

    public String buildOrderNo(Long agentPayPaymentId) {
        return "APP" + DateUtil.toString(new Date(), DateUtil.YYYY_MM_DD_HH_MM_SS_SSS) + agentPayPaymentId;
    }
}
