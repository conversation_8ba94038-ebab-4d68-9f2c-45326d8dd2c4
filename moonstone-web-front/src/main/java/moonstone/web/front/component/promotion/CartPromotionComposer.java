package moonstone.web.front.component.promotion;

import moonstone.cart.dto.RichCart;
import moonstone.common.model.CommonUser;

import java.util.List;

/**
 * Created by cp on 4/27/17.
 */
public interface CartPromotionComposer {

    /**
     * 设置购物车营销活动信息
     *
     * @param buyer     买家
     * @param richCarts 购物车信息
     */
    void composePromotions(CommonUser buyer, List<RichCart> richCarts);

    void appendFlag(List<RichCart> richCarts, Long shopId);
}
