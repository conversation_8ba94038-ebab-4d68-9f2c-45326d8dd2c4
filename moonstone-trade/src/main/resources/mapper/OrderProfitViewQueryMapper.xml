<?xml version="1.0" encoding="UTF-8" ?>
<!-- ~Simple XML Generate By moonstone.common.util.MybatisGenerator~ -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace='OrderProfitView'>
    <select id="selectProfitByTime" parameterType="map" resultMap="OrderProfitViewMap">
        select A.* from
        <include refid="tb"/> A left join
        parana_shop_orders B on A.id = B.id left join
        profit_withdraw_record C on A.B_ID = C.profit_id
        <where>
            B.created_at >= #{from}
            AND #{to} >= B.created_at
            AND B.shop_id = #{shopId}
            AND A.user_id = #{userId}
            <if test="status > 0">
                AND A.`status` = 3 AND 512 = <![CDATA[ (512 & A.B_status) ]]>
            </if>
            <if test="status == 1">
                AND C.`status` is NULL
            </if>
            <if test="status == 2">
                AND C.`status` = 2
            </if>
            <if test="status == 3">
                AND C.`status` != 2 AND C.status is not NULL
            </if>
            <if test="status == 0">
                AND A.`status` > 0 AND 3 > A.`status` AND 512 != <![CDATA[ (512 & A.B_status) ]]>
            </if>
        </where>
    </select>

    <select id="findByOrderId" parameterType="map" resultMap="OrderProfitViewMap">
        <![CDATA[select * from]]>
        <include refid="tb"/>
        <![CDATA[ where order_id = #{orderId} and type = #{type} and `status` = #{status} ]]>
    </select>
</mapper>
