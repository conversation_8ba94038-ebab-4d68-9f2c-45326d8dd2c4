<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2015 杭州端点网络科技有限公司
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="PayChannelDailySummary">
    <resultMap id="PayChannelDailySummaryMap" type="PayChannelDailySummary">
        <id column="id" property="id" />
        <result column="channel" property="channel"/>
		<result column="trade_fee" property="tradeFee"/>
		<result column="gateway_commission" property="gatewayCommission"/>
		<result column="net_income_fee" property="netIncomeFee"/>
		<result column="sum_at" property="sumAt"/>
        <result column="extra_json" property="extraJson"/>
		
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="table_name">
        parana_pay_channel_daily_summarys
    </sql>

    <sql id="columns_exclude_id">
        `channel`,`trade_fee`,`gateway_commission`,`net_income_fee`,`sum_at`,
		`extra_json`,
        created_at, updated_at
    </sql>

    <sql id="columns">
        id,
        <include refid="columns_exclude_id"/>
    </sql>

    <sql id="values">
        #{channel},#{tradeFee},#{gatewayCommission},#{netIncomeFee},#{sumAt},
		#{extraJson},
        now(),now()
    </sql>

    <sql id="criteria">
        1 = 1
        <if test="channel != null"> and `channel` = #{channel}</if>
		<if test="tradeFee != null"> and `trade_fee` = #{tradeFee}</if>
		<if test="gatewayCommission != null"> and `gateway_commission` = #{gatewayCommission}</if>
		<if test="netIncomeFee != null"> and `net_income_fee` = #{netIncomeFee}</if>
		<if test="sumAtStart != null"> and `sum_at` &gt;= #{sumAtStart}</if>
        <if test="sumAtEnd != null"> and `sum_at` &lt;= #{sumAtEnd}</if>

    </sql>

    <insert id="create" parameterType="PayChannelDailySummary" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="table_name"/>
        (<include refid="columns_exclude_id"/>)
        VALUES (<include refid="values"/>)
    </insert>

    <update id="update" parameterType="PayChannelDailySummary">
        UPDATE
        <include refid="table_name"/>
        <set>
            <if test="channel != null"> `channel` = #{channel}, </if>
			<if test="tradeFee != null"> `trade_fee` = #{tradeFee}, </if>
			<if test="gatewayCommission != null"> `gateway_commission` = #{gatewayCommission}, </if>
			<if test="netIncomeFee != null"> `net_income_fee` = #{netIncomeFee}, </if>
			<if test="sumAt != null"> `sum_at` = #{sumAt}, </if>
            <if test="extraJson != null">`extra_json` =  #{extraJson},</if>
            updated_at=now()
        </set>
        WHERE id=#{id}
    </update>

    <select id="findById" parameterType="long" resultMap="PayChannelDailySummaryMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="PayChannelDailySummaryMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        WHERE id IN
        <foreach collection="list" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="table_name"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="PayChannelDailySummaryMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER BY id DESC
        <if test="limit != null">
            LIMIT
            <if test="offset != null">#{offset},</if>
            #{limit}
        </if>
    </select>

    <select id="list" parameterType="map" resultMap="PayChannelDailySummaryMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        <where>
            <include refid="criteria"/>
        </where>
        ORDER BY id DESC
    </select>

    <delete id="delete" parameterType="map">
        delete from <include refid="table_name"/>
        where id=#{id}
    </delete>

    <select id="findByChannelAndSumAt" parameterType="map" resultMap="PayChannelDailySummaryMap">
        SELECT
        <include refid="columns"/>
        FROM
        <include refid="table_name"/>
        WHERE channel=#{channel} and sum_at=#{sumAt}
        LIMIT 1
    </select>

</mapper>