
package moonstone.web.op.allinpay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 支付方式
 *

 * @since 2021-02-19
 */
@Data
@ApiModel(value = "PayMethodDTO对象", description = "支付方式")
public class PayMethodDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	private String keyName;

	@ApiModelProperty(value = "支付金额(分)")
	private Long amount;

	@ApiModelProperty(value = "扩展参数(json格式)")
	private String extendParams;

	@ApiModelProperty(value = "非贷记卡：no_credit 借、贷记卡：””，需要传空字符串，不能不传")
	private String limitPay;

	@ApiModelProperty(value = "微信端应用ID：appid")
	private String subAppid;

	@ApiModelProperty(value = "微信用户标识openid——微信分配")
	private String acct;

	@ApiModelProperty(value = "用户下单及调起支付的终端IP")
	private String cusip;

	@ApiModelProperty(value = "场景信息")
	private String sceneInfo;
}
