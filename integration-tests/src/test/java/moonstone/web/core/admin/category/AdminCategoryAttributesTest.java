/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.admin.category;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import configuration.admin.AdminWebConfiguration;
import moonstone.attribute.dto.AttributeMetaKey;
import moonstone.category.model.CategoryAttribute;
import moonstone.web.core.BaseWebTest;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.springframework.test.context.ContextConfiguration;

import java.util.Arrays;
import java.util.List;

import static org.hamcrest.CoreMatchers.hasItem;
import static org.hamcrest.core.IsNull.notNullValue;
import static org.junit.Assert.assertThat;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-27
 */
@ContextConfiguration(classes = AdminWebConfiguration.class)
public class AdminCategoryAttributesTest extends BaseWebTest {

    private CategoryAttribute categoryAttribute;

    @Before
    public void setUp() throws Exception {
        //Building the Request body data
        categoryAttribute = new CategoryAttribute();
        categoryAttribute.setCategoryId(1L);
        categoryAttribute.setGroup("基本属性");
        categoryAttribute.setAttrKey("attrKey1");
        categoryAttribute.setAttrVals(ImmutableList.of("val1","val2","val3"));
        categoryAttribute.setAttrMetas(ImmutableMap.of(
                AttributeMetaKey.REQUIRED,"true",
                AttributeMetaKey.SKU_CANDIDATE,"false",
                AttributeMetaKey.VALUE_TYPE, "STRING"));
        Long attributeId = restTemplate.postForObject("http://localhost:{port}/api/attributes",
                categoryAttribute, Long.class, ImmutableMap.of("port", port));
        assertThat(attributeId, notNullValue());
        categoryAttribute.setId(attributeId);
    }

    @Test
    public void testFindByCategoryId() throws Exception {
        CategoryAttribute[] result = restTemplate.getForObject(
                "http://localhost:{port}/api/attributes?categoryId={categoryId}",
                CategoryAttribute[].class,
                ImmutableMap.of("port", port, "categoryId", categoryAttribute.getCategoryId()));

        List<CategoryAttribute> categoryAttributes = Arrays.asList(result);

        assertThat(categoryAttributes, hasItem(categoryAttribute));
    }

    @Test
    public void testUpdate() throws Exception {
        CategoryAttribute u = new CategoryAttribute();
        u.setAttrKey("attrKey2");
        u.setId(categoryAttribute.getId());
        u.setCategoryId(categoryAttribute.getCategoryId());
        u.setGroup("基本属性");
        restTemplate.put("http://localhost:{port}/api/attributes", u,
                ImmutableMap.of("port", port));

        CategoryAttribute[] result = restTemplate.getForObject(
                "http://localhost:{port}/api/attributes?categoryId={categoryId}",
                CategoryAttribute[].class,
                ImmutableMap.of("port", port, "categoryId", categoryAttribute.getCategoryId()));

        List<CategoryAttribute> categoryAttributes = Arrays.asList(result);

        assertThat(categoryAttributes, hasItem(u));

    }

    @After
    public void tearDown() throws Exception {
        restTemplate.delete("http://localhost:{port}/api/attributes/{id}",
                ImmutableMap.of("port", port, "id", categoryAttribute.getId()));
    }
}
