package moonstone.web.front.shop.jifen;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.cache.ItemCacheHolder;
import moonstone.common.enums.IntegralStatus;
import moonstone.common.enums.OpBusinessType;
import moonstone.common.model.Either;
import moonstone.common.model.IsIntegral;
import moonstone.common.model.IsPersistAble;
import moonstone.common.model.IsPresent;
import moonstone.common.utils.*;
import moonstone.integral.model.IntegralActivity;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.user.model.*;
import moonstone.user.service.*;
import moonstone.web.core.component.cache.IntegralActivityCacheHolder;
import moonstone.web.core.component.integral.IntegralActivityManager;
import moonstone.web.core.constants.ParanaConfig;
import moonstone.web.core.events.Integral.IntegralGenImageEvent;
import moonstone.web.core.files.utils.ZipUtils;
import moonstone.web.core.integral.IntegralAccount;
import moonstone.web.core.integral.IntegralOperationLogs;
import moonstone.web.core.shop.application.CompleteDataActivityCacheHolder;
import moonstone.web.core.shop.application.CompleteDataActivityExecutor;
import moonstone.web.core.shop.dto.CompleteDataActivity;
import moonstone.web.core.third.ThirdIntermediateInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Predicate;
import java.util.zip.ZipOutputStream;

/**
 * 积分扫描处理controller
 *
 * @Author: wuxian-yjp
 * @Date: 2019/6/19 17:12
 */
@RestController
@RequestMapping("/api/integral")
@Slf4j
public class IntegralGenImage {


    @RpcConsumer
    IntegralDataStatusReadService integralDataStatusReadService;

    @RpcConsumer
    IntegralDataStatusWriteService integralDataStatusWriteService;

    @RpcConsumer
    StoreIntegralRecordWriteService storeIntegralRecordWriteService;

    @RpcConsumer
    private ShopReadService shopReadService;

    @RpcConsumer
    IntegralUseRecordReadService integralUseRecordReadService;

    @RpcConsumer
    IntegralUseRecordWriteService integralUseRecordWriteService;

    @RpcConsumer
    StoreIntegralWriteService storeIntegralWriteService;

    @RpcConsumer
    StoreIntegralReadService storeIntegralReadService;
    @RpcConsumer
    StoreIntegralRecordReadService storeIntegralRecordReadService;


    @RpcConsumer
    StoreIntegralLogWriteService storeIntegralLogWriteService;

    @RpcConsumer
    StoreIntegralLogReadService storeIntegralLogReadService;

    @RpcConsumer
    IntegralGoodsWriteService integralGoodsWriteService;

    @RpcConsumer
    HonestFanDataReadService honestFanDataReadService;

    @RpcConsumer
    HonestFanDataWriteService honestFanDatWriteService;

    @Autowired
    private ParanaConfig paranaConfig;

    @RpcConsumer
    private StoreProxyReadService storeProxyReadService;

    @RpcConsumer
    private BalanceDetailWriteService balanceDetailWriteService;

    @Autowired
    IntegralAccount integralAccount;

    @Autowired
    private ItemCacheHolder itemCacheHolder;

    @Autowired
    private IntegralActivityManager integralActivityManager;

    @RpcConsumer
    IntegralMallCodesWriteService integralMallCodesWriteService;

    @RpcConsumer
    IntegralMallCodesReadService integralMallCodesReadService;

    @RpcConsumer
    BalanceDetailManager balanceDetailManager;

    @RpcConsumer
    StoreIntegralGoodsWriteService storeIntegralGoodsWriteService;

    @RpcConsumer
    StoreIntegralGoodsReadService storeIntegralGoodsReadService;

    @RpcConsumer
    StoreIntegralCodeWriteService storeIntegralCodeWriteService;

    @RpcConsumer
    ThirdIntermediateInfo thirdIntermediateInfo;

    @Autowired
    IntegralOperationLogs integralOperationLogs;

    @Autowired
    RiskManagement riskManagement;

    @RpcConsumer
    IntegralParanaDataStatusReadService integralParanaDataStatusReadService;

    @Autowired
    private CompleteDataActivityExecutor completeDataActivityExecutor;
    @Autowired
    private CompleteDataActivityCacheHolder completeDataActivityCacheHolder;
    @Autowired
    private IntegralActivityCacheHolder integralActivityCacheHolder;


    /**
     * 是否可以参加新扫描积分奖励活动
     *
     * @param userId 用户Id
     * @param shopId 店铺Id
     * @return 是否具有
     */
    private boolean canEnjoyFirstReward(Long userId, Long shopId) {
        Either<Optional<StoreIntegral>> storeIntegral = storeIntegralReadService.findAvailableGradeByUserIdAndShopId(userId, shopId);
        if (!storeIntegral.isSuccess()) {
            log.error("{} fail to query user[{}] shop[{}] integral", LogUtil.getClassMethodName(), userId, shopId, storeIntegral.getError());
            throw new IllegalStateException(new Translate("查找积分账户失败").toString());
        }

        // 没有积分账户
        if (storeIntegral.take().isPresent()) {
            return integralActivityManager.findFirstActivity(shopId, userId)
                    .filter(activity -> integralActivityManager.isFirstTrigger(activity, userId))
                    .isPresent();
        }
        return true;
    }

    /**
     * 生成二维码信息存入mongodb
     *
     * @param shopId    店铺id
     * @param num       数量
     * @param faceValue 积分面值
     * @return
     */
    @GetMapping("/genCode")
    @MyLog(title = "积分管理-商品码管理", value = "商家生成积分码", opBusinessType = OpBusinessType.INSERT)
    public R genImageCode(@RequestParam long shopId, @RequestParam long num, @RequestParam long faceValue, @RequestParam long thirdId, HttpServletRequest request) throws Exception {

        Long userId = getLoginUserId();//检测是否登录，登录用户才有生成二维码
        if (ObjectUtils.isEmpty(shopId) || StringUtils.isEmpty(num) || StringUtils.isEmpty(thirdId) || StringUtils.isEmpty(faceValue)) {
            log.error("{} num:{} shopId:{} categoriesId:{} faceValue:{}", LogUtil.getClassMethodName(), num, shopId, thirdId, faceValue);
            return R.error(-1, "缺少必要参数");
        }
        if (num > 100000) {
            return R.error(-1, "数量不能超过100000");
        }

        //生成二维码-----使用enentBus异步生成
        EventSender.sendApplicationEvent(new IntegralGenImageEvent(shopId, num, faceValue, thirdId));
        log.info("[genImageCode.success-first-one] shopId:{}", shopId);

        IntegralOperationLog integralOperationLog = new IntegralOperationLog();
        integralOperationLog.setDesc("生成商品码");
        integralOperationLog.setUrl("/genCode");
        integralOperationLogs.recordWriteOperationLog(request, integralOperationLog);

        return R.ok();
    }


    /**
     * 积分二维码扫码处理
     *
     * @return todo
     * 注：店铺id=33 积分码，返工修改数据。此数据变更2019-11-01之前的数据，变更一共十五个批次，
     * 上述为表变更明细。规则积分编码和数量不动，只变更产品名，冻结状态跟随产品名含一段走
     * 有邮件发给品牌方，注：批次号没有变动
     * by wuxian date:2019-11-01
     */
    @GetMapping("/genAccept")
    public R genImageCode2(@RequestParam long userId, @RequestParam String code, @RequestParam long shopId, @RequestParam String ip) {

        if(System.currentTimeMillis()>1597636800000L){
            return R.error(-1, "贝拉米商城积分商城将于8月17日12:00至8月23日23:59进行技术维护，维护详情请见首页公告");
        }
        Boolean isMoney = false;//false 无佣金  true 有佣金
        String info = "恭喜您获得%s积分!";
        try {

            Long userIds = getLoginUserId();//检测是否登录
            if (ObjectUtils.isEmpty(shopId) || StringUtils.isEmpty(code) || StringUtils.isEmpty(userId) || StringUtils.isEmpty(ip)) {
                return R.error(-1, "抱歉，扫码失败!");
            }
            if (!Objects.equals(userId, userIds)) {
                return R.error(-1, "抱歉，用户异常!");
            }
            log.info("[genAccept-accept start] userId={} code={} shopId:{}", userId, code, shopId);
            Shop rShop = Optional.ofNullable(shopReadService.findById(shopId).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));
            Map<String, String> extra = rShop.getExtra();
            //拒绝没有开启积分账户的店铺
            if (extra == null
                    || extra.isEmpty()
                    || !extra.containsKey("startIntegral")
                    || !"2".equals(extra.get("startIntegral"))
            ) {
                log.info("[shop is not start ] extra:{}", extra);
                return R.error(-1, "抱歉，商家未开启此功能!");
            }
            //风控开始：风控是先过滤
            Map<String, String> mapCheck = riskManagement.checkIntegralLimit(userId, shopId, ip);
            if (!Objects.equals("PASS", mapCheck.get("tip"))) {
                return R.error(-1, mapCheck.get("msg"));
            }

            //拒绝经销商和门店扫码积分
            Either<Optional<StoreProxy>> storeProxy = storeProxyReadService.findByShopIdAndUserId(userId, shopId);

            if (storeProxy.isSuccess() && storeProxy.take().isPresent()) {
                log.info("[StoreProxy isPresent] userId={}", userId);
                return R.error(-1, "抱歉，该身份无扫码权限!");
            }
            if (code.length() != 20) {
                log.error("code length error shop:{} code.lenth:{}", code.length());
                riskManagement.addErrorCount(userId, shopId, ip);
                return R.error(-2, "该积分码无效!")
                        .add("msgTip", "如有疑问请联系400官方客服:")
                        .add("msgPhone", "************");
            }
            Either<Optional<IntegralMallCodes>> integralData = integralMallCodesReadService.findBygenCode(code.substring(0, 19));

            if (!integralData.isSuccess() || !integralData.take().isPresent()) {
                log.error("[find by code is error] code:{}", code);
                riskManagement.addErrorCount(userId, shopId, ip);
                return R.error(-2, "该积分码无效!")
                        .add("msgTip", "如有疑问请联系400官方客服:")
                        .add("msgPhone", "************");
            }

            if (!Objects.equals(shopId, integralData.take().get().getShopId())) {
                log.error("shop not equal shopId:{} code_shopId:{}", shopId, integralData.take().get().getShopId());
                riskManagement.addErrorCount(userId, shopId, ip);
                return R.error(-2, "该积分码无效!")
                        .add("msgTip", "如有疑问请联系400官方客服:")
                        .add("msgPhone", "************");
            }
            if (!Objects.equals(code.substring(19), integralData.take().get().getCode())) {
                log.error("校验码 is error code:{} jycode:{}", code, integralData.take().get().getCode());
                riskManagement.addErrorCount(userId, shopId, ip);
                return R.error(-2, "该积分码无效!")
                        .add("msgTip", "如有疑问请联系400官方客服:")
                        .add("msgPhone", "************");
            }

            //判断是否是一段奶粉，是一段进行冻结
            if (ObjectUtils.isEmpty(integralData.take().get().getThirdId())) {
                log.error("[ThirdId frozen is empty] ThirdId:{}", integralData.take().get().getThirdId());
                riskManagement.addErrorCount(userId, shopId, ip);
                return R.error(-2, "该积分码无效!")
                        .add("msgTip", "如有疑问请联系400官方客服:")
                        .add("msgPhone", "************");
            }

            Boolean isFlag = false;//false 非一段  true 一段

            /*
             *  积分相关数据,批次号与对应商品信息
             */
            IntegralParanaDataStatus integralDataInfo = null;

            Response<List<IntegralParanaDataStatus>> listResponse = integralParanaDataStatusReadService.findIntegralParanaDataStatusByBatch(integralData.take().get().getBatch(), shopId);
            if (!listResponse.isSuccess()) {
                return R.error(-2, "该积分码无效!")
                        .add("msgTip", "如有疑问请联系400官方客服:")
                        .add("msgPhone", "************")
                        .add("flag", isMoney);
            }
            //防止商家配置渠道划分漏设，渠道默认无佣金，但是冻结来源是来自源数据
            if (listResponse.getResult() == null || listResponse.getResult().isEmpty()) {
                Either<Optional<IntegralDataStatus>> optionalResult = integralDataStatusReadService.findByCodeAndShopId(integralData.take().get().getBatch(), shopId);

                if (!optionalResult.isSuccess() || !optionalResult.take().isPresent()) {
                    log.error("[find IntegralDataStatus is error by batch] batch:{} thirdId:{}", integralData.take().get().getBatch(), integralData.take().get().getThirdId());
                    return R.error(-2, "该积分码无效!")
                            .add("msgTip", "如有疑问请联系400官方客服:")
                            .add("msgPhone", "************")
                            .add("flag", isMoney);
                }
                //判定是否是冻结--同一个批次是否冻结是一样的
                if (Objects.equals(optionalResult.take().get().getFlag(), 0)) {
                    log.info("flag:{} code:{}", optionalResult.take().get().getFlag(), code);
                    isFlag = true;
                }
            } else {
                //判定是否是冻结--同一个批次是否冻结是一样的
                if (Objects.equals(listResponse.getResult().get(0).getFlag(), 0)) {
                    isFlag = true;
                }
                //判定是否有佣金
                for (IntegralParanaDataStatus s : listResponse.getResult()) {
                    if (Long.valueOf(s.getStartNum()) <= Long.valueOf(integralData.take().get().getNum())
                            && Long.valueOf(s.getEndNum()) >= Long.valueOf(integralData.take().get().getNum())
                            && Objects.equals(3, s.getStatus())
                            && Objects.equals(2, s.getType())) {
                        isMoney = true;
                        integralDataInfo = s;
                    }
                }
            }
            // 处理优惠活动
            Long guessShopId = integralData.take().get().getShopId();
            boolean canEnjoyFirstReward = canEnjoyFirstReward(userIds, guessShopId);
            Optional<CompleteDataActivity> completeDataActivity =
                    Optional.of(guessShopId).flatMap(completeDataActivityCacheHolder::getCompleteActivity)
                            .orElseGet(ArrayList::new)
                            .stream()
                            .filter(activity -> completeDataActivityExecutor.canTrigger(userIds, activity))
                            .findFirst();
            Optional<IntegralActivity> integralActivity = integralActivityManager.findFirstActivity(guessShopId, userId);
            boolean thereIsACompleteDataActivity = completeDataActivity.isPresent();
            String canJoinActivity = "";
            String buttonName = "";

            if (canEnjoyFirstReward && !integralActivity.isPresent()) {
                log.warn("{} strange logical canEnjoyFirstReward didn't match firstActivity", LogUtil.getClassMethodName());
            }
            canEnjoyFirstReward = canEnjoyFirstReward && integralActivity.isPresent();

            if (thereIsACompleteDataActivity) {
                if (!canEnjoyFirstReward) {
                    canJoinActivity = String.format("请您完善会员信息,将获得%s积分", completeDataActivity.get().getRewardIntegral());
                    buttonName = "立即前往";
                } else {
                    canJoinActivity = String.format("新客请完善个人信息,将获得新人首礼%s积分和完善会员信息奖%s积分", integralActivity.get().getExtraReward(), completeDataActivity.get().getRewardIntegral());
                    buttonName = "前往个人信息";
                }
            } else if (canEnjoyFirstReward) {
                info = String.format("感谢您对贝拉米的关注，恭喜您获得新人首礼%s积分！", integralActivity.get().getExtraReward());
            }

            if (!Objects.equals(integralData.take().get().getStatus(), IntegralStatus.NORMAL.value())) {
                if (isFlag) {
                    //冻结判断积分码的有效性
                    return R.error(-4, "期待您的再次光临!")
                            .add("flag", isMoney)
                            .add("button", buttonName)
                            .add("completeActivity", canJoinActivity);
                } else {
                    //非冻结判断积分码的有效性
                    if (Objects.equals(integralData.take().get().getStatus(), IntegralStatus.USEED.value())) {
                        return R.error(-3, "该积分码已使用!")
                                .add("msgTip", "如有疑问请联系400官方客服:")
                                .add("msgPhone", "************")
                                .add("flag", isMoney);
                    } else {
                        return R.error(-2, "该积分码无效!")
                                .add("msgTip", "如有疑问请联系400官方客服:")
                                .add("msgPhone", "************")
                                .add("flag", isMoney);
                    }
                }
            }
            //乐观锁-将积分码锁定
            Either<Boolean> updateResult = integralMallCodesWriteService.update(shopId, integralData.take().get().getGenCode(), integralData.take().get().getCode(), IntegralStatus.LOCKED.value());
            if (!updateResult.isSuccess() || !updateResult.take()) {
                log.error("[update IntegralMallCodes is error ] getGenCode:{} shopId:{}", integralData.take().get().getGenCode(), shopId);
                return R.error(-2, "该积分码无效!")
                        .add("msgTip", "如有疑问请联系400官方客服:")
                        .add("msgPhone", "************")
                        .add("flag", isMoney);
            }

            /**
             * IntegralStatus. USEED(-1) 已使用
             * IntegralStatus.AVAILABLE(3) 获取
             * IntegralStatus.FROZEN(-3) 冻结
             * 通过userId的用户关系 获取是否有佣金
             */
            return addStoreIntegral(userId, code, shopId, ip, isFlag, integralData.take().get(), integralDataInfo, isMoney, info, canJoinActivity, buttonName);

        } catch (Exception e) {
            log.error("failed find to  cause:{}", Throwables.getStackTraceAsString(e));
            return R.error(-1, "抱歉，系统异常!");
        } finally {
            log.debug("{} flag(isMoney):{}", LogUtil.getClassMethodName(), isMoney);
        }
    }

    /**
     * 扫码增加积分 事务接口
     *
     * @param userId 用户id
     */
    @Transactional
    public R addStoreIntegral(long userId, String code, long shopId, String ip, Boolean isFlag, IntegralMallCodes integralMallCodes, IntegralParanaDataStatus integralDataInfo, Boolean isMoney, String info, String canJoinActivity, String buttonName) {
        Date now = new Date();

        Long itemId = Optional.ofNullable(integralDataInfo).map(IntegralParanaDataStatus::getItemId).orElseGet(() -> Objects.requireNonNull(Optional.ofNullable(integralMallCodes).map(IntegralMallCodes::getThirdId).orElse(-1L)));
        StoreIntegral storeIntegral = null;

        Long integralRewardFee = integralMallCodes.getFaceValue();
        log.debug("{} user [{}] code [{}] shop [{}] ip [{}] flag [{}] mallCode [{}] dataInfo [{}] money [{}]"
                , LogUtil.getClassMethodName(), userId, code, shopId, ip, isFlag, integralMallCodes, integralDataInfo, isMoney);
        Optional<IntegralActivity> integralActivity = integralActivityCacheHolder.findByItemId(Optional.ofNullable(integralDataInfo).map(IntegralParanaDataStatus::getItemId).orElse(integralMallCodes.getThirdId()));

        integralActivity = integralActivity.filter(activity -> Optional.ofNullable(activity.getLongTerm()).filter(Predicate.isEqual(true))
                .orElseGet(() -> !Optional.ofNullable(activity.getStartAt()).orElse(now).after(now) && !Optional.ofNullable(activity.getEndAt()).map(DateUtil::withTimeAtEndOfDay).orElse(now).before(now)))
                .filter(activity -> integralActivityManager.checkLimitByDay(activity, userId));
        integralActivity.ifPresent(activity -> log.info("{} user[{}] shop[{}] ip[{}] freeze[{}] code[{}] item[{}] trigger activity[{}]({})", LogUtil.getClassMethodName(),
                userId, shopId, ip, isFlag, code, itemId, activity.getId(), activity.getName()));

        // 获取可参加的首次活动
        Optional<IntegralActivity> firstActivity = integralActivityManager.findFirstActivity(shopId, userId)
                .filter(activity -> integralActivityManager.tryFirstTrigger(activity, userId));// 不一定可以参加

        firstActivity.ifPresent(activity -> log.info("{} user[{}] shop[{}] ip[{}] freeze[{}] code[{}] item[{}] trigger first-activity[{}]({})", LogUtil.getClassMethodName(),
                userId, shopId, ip, isFlag, code, itemId, activity.getId(), activity.getName()));

        long rate = integralActivity.map(IntegralActivity::getRateReward).orElse(0L) + firstActivity.map(IntegralActivity::getRateReward).orElse(0L) + 100L;

        integralRewardFee = integralRewardFee * rate / 100L;

        integralRewardFee += integralActivity.map(IntegralActivity::getExtraReward).orElse(0L) + firstActivity.map(IntegralActivity::getExtraReward).orElse(0L);

        log.debug("{} user [{}] shop [{}] code [{}] enjoy integral extra rate [{}] sum up to [{}]", LogUtil.getClassMethodName(), userId, shopId, code, rate, integralRewardFee);

        if (isFlag) {
            //初始化积分账户--并添加积分--一段奶粉--冻结
            Map<String, String> maps = new HashMap<>();
            maps.put("userId", String.valueOf(userId));
            maps.put("shopId", String.valueOf(shopId));
            maps.put("code", code);
            maps.put("integralFee", String.valueOf(integralRewardFee));
            log.info("addStoreIntegral:{} type:{} code:{} userId:{}", maps, 7, code, userId);
            Either<StoreIntegral> storeIntegralResult = integralAccount.initializeIntegralAccount(maps, 7);

            if (!storeIntegralResult.isSuccess() || storeIntegralResult.take() == null || ObjectUtils.isEmpty(storeIntegralResult.take().getId())) {
                log.error("{} update-Integral-Account:{}", LogUtil.getClassMethodName(), maps);
                integralMallCodesWriteService.update(shopId, code.substring(0, 19), code.substring(19), IntegralStatus.NORMAL.value());
                return R.error(-2, "该积分码无效!")
                        .add("msgTip", "如有疑问请联系400官方客服:")
                        .add("msgPhone", "************")
                        .add("flag", isMoney);
            }

            storeIntegral = storeIntegralResult.take();

        } else {

            //初始化积分账户--并添加积分--非一段奶粉 非冻结
            Map<String, String> maps = new HashMap<>();
            maps.put("userId", String.valueOf(userId));
            maps.put("shopId", String.valueOf(shopId));
            maps.put("code", code);
            maps.put("integralFee", String.valueOf(integralRewardFee));
            log.info("addStoreIntegral:{} type:{} code:{} userId:{}", maps, 2, code, userId);
            Either<StoreIntegral> storeIntegralResult = integralAccount.initializeIntegralAccount(maps, 2);
            if (!storeIntegralResult.isSuccess() || storeIntegralResult.take() == null || ObjectUtils.isEmpty(storeIntegralResult.take().getId())) {
                log.error("[] updateIntegralAcconut:{}", maps);
                integralMallCodesWriteService.update(shopId, code.substring(0, 19), code.substring(19), IntegralStatus.NORMAL.value());
                return R.error(-2, "该积分码无效!")
                        .add("msgTip", "如有疑问请联系400官方客服:")
                        .add("msgPhone", "************")
                        .add("flag", isMoney);
            }
            storeIntegral = storeIntegralResult.take();
        }

        /**
         * 設置积分首扫佣金  parana_shops ---extraJson--integralFee
         */
        Shop rShop = Optional.ofNullable(shopReadService.findById(shopId).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));

        //增加忠粉扫码收益
        Either<Optional<HonestFanSum>> honestFanSum = honestFanDataReadService.findByUserId(userId, shopId);
        Map<String, String> extra = rShop.getExtra();
        if (extra != null
                && !extra.isEmpty()
                && extra.containsKey("startIntegral")
                && "2".equals(extra.get("startIntegral"))
//                && (storeIntegral.getTradeAll() - storeIntegral.getTradeNum()) > 0) {
//                && storeIntegral.getTradeNum() == 0) {
                && isMoney) {

            if (honestFanSum.isSuccess() && honestFanSum.take().isPresent()) {
                List<JSONObject> listFee = thirdIntermediateInfo.findRateByShopIdAndUserIdIntegral(integralMallCodes.getThirdId(), shopId);
                String integralFee = "0";

                if (listFee != null && !listFee.isEmpty() && listFee.get(0).containsKey("commission")) {
                    integralFee = listFee.get(0).get("commission").toString();
                    if (!ObjectUtils.isEmpty(integralFee)) {
                        log.info("[scan add money] userId:{} money:{}", userId, integralFee);
                        StoreIntegralLog storeIntegralLog = new StoreIntegralLog();
                        storeIntegralLog.setUserId(userId);
                        storeIntegralLog.setShopId(shopId);
                        storeIntegralLog.setIntegralFee(Long.valueOf(integralFee));
                        storeIntegralLog.setSupperId(honestFanSum.take().get().getProxyId());
                        storeIntegralLog.setStatus(IntegralStatus.AVAILABLE.value());
                        val rstoreIntegralLogAdd = storeIntegralLogWriteService.create(storeIntegralLog);
                        if (!rstoreIntegralLogAdd.isSuccess()) {
                            log.error("[rintegralUseRecordAdd is fial] ", rstoreIntegralLogAdd);
                            throw new JsonResponseException(new Translate("积分码失败").toString());
                        }
                        //插入收益
                        BalanceDetail balanceDetail = new BalanceDetail();
                        balanceDetail.setChangeFee(Long.valueOf(integralFee));
                        balanceDetail.setFee(null);
                        balanceDetail.setSourceId(shopId);
                        balanceDetail.setRelatedId(storeIntegralLog.getId());
                        balanceDetail.setUserId(honestFanSum.take().get().getProxyId());
                        balanceDetail.setType(ProfitType.InCome.getValue());

                        balanceDetail.setStatus(IsPresent.presentMaskBit.Present.getValue() | IsPersistAble.maskBit.PersistAble.getValue() | IsIntegral.maskBit.Integral.getValue());

                        val profitResult = balanceDetailManager.changeRealProfit(balanceDetail);

                        if (!profitResult.isSuccess()) {
                            log.error("[IntegralData-balanceDetailWriteService-create] userId:{} code:{}", userId, code);
                            throw new JsonResponseException(new Translate("积分码失败").toString());
                        }
                    } else {
                        log.info("[scana not add money] userId:{} money:{}", userId, integralFee);
                    }
                } else {
                    log.info("[scanb not add money] userId:{} money:{}", userId, integralFee);
                }
            }
        } else {
            log.info("[scanc not add money] userId:{} money:{}", userId, integralMallCodes.getFaceValue());
        }

        //核减数减一
//        if (storeIntegral.getTradeNum() > 0) {
//            StoreIntegral storeIntegral1 = new StoreIntegral();
//            storeIntegral1.setTradeNum(storeIntegral.getTradeNum() - 1);
//            storeIntegral1.setId(storeIntegral.getId());
//            Result<Boolean> booleanResult = storeIntegralWriteService.updateStoreIntegral(storeIntegral1);
//            if (!booleanResult.isSuccess() || !booleanResult.getResult()) {
//                log.error("updateScanIntegral  hejianshu fail storeIntegral:{} ", storeIntegral);
//                throw new JsonResponseException(new Translate("积分码失败").toString());
//            }
//        }


        //将积分码修改为已使用
        integralMallCodesWriteService.update(shopId, code.substring(0, 19), code.substring(19), IntegralStatus.USEED.value());
        //将已使用的积分码记录进mysql 以便于后期积分码对账
        StoreIntegralCode storeIntegralCode = new StoreIntegralCode();
        BeanUtils.copyProperties(integralMallCodes, storeIntegralCode);
        storeIntegralCode.setStatus(IntegralStatus.USEED.value());
        storeIntegralCodeWriteService.create(storeIntegralCode);

        riskManagement.addSuccessCount(userId, shopId, ip);

        if (isFlag) {
            return R.ok("感谢您的惠顾!")
                    .add("flag", isMoney);
        } else {
            return R.error(1, String.format(info, integralRewardFee))
                    .add("completeActivity", canJoinActivity)
                    .add("button", buttonName)
                    .add("flag", isMoney);
        }

    }

    /**
     * 打包压缩下载文件
     */
    @GetMapping("/ajaxZipFile")
    public R ifDownLoadZipFile(HttpServletResponse response, String code, long thirdId, long shopId) throws IOException {
        Long userId = getLoginUserId();//检测是否登录，登录用户才有生成二维码
        Either<Optional<IntegralDataStatus>> integralDataStatus = integralDataStatusReadService.findByCode(code, IntegralStatus.NORMAL.value());
        if (!integralDataStatus.isSuccess()) {
            log.error("[IntegralData-genAccept-empty] code:{}", code);
            return R.error(-1, "生成还未完成，请等待");
        }
        if (!integralDataStatus.take().isPresent()) {
            log.info("[IntegralData-genAccept-empty] code:{}", code);
            return R.error(-1, "生成还未完成，请等待");
        }
        Either<Optional<StoreIntegralGoods>> lsits = storeIntegralGoodsReadService.findByBatch(code);
        if (!lsits.isSuccess() || !lsits.take().isPresent()) {
            log.error("[IntegralData-DownLoad-empty] code:{}", code);
            return R.error(-1, "系统异常");
        }
        if (!Objects.equals(lsits.take().get().getStatus(), IntegralStatus.NOT_ACTIVATE.value())) {
            log.info("[IntegralData-NO-finish] code:{}", code);
            return R.error(-1, "系统异常");
        }
        return R.ok();
    }

    @GetMapping("/downLoadZipFile")
    @MyLog(title = "积分管理-商品码管理", value = "商家下载积分码", opBusinessType = OpBusinessType.EXPORT)
    public void downLoadZipFiles(HttpServletResponse response, String code, long thirdId, long shopId, HttpServletRequest request) throws IOException {

        Long userIds = getLoginUserId();//检测是否登录
        String zipName = DateUtil.getDateString() + ".zip";
        response.setContentType("APPLICATION/OCTET-STREAM");
        response.setHeader("Content-Disposition", "attachment;filename=" + zipName);
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "No-cache");
        response.setDateHeader("Expires", 0);
        ZipOutputStream out = new ZipOutputStream(response.getOutputStream());
        try {
            ZipUtils.doCompress(paranaConfig.getParanaLevelDistributionPath() + "/" + code, out);
            response.flushBuffer();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            storeIntegralGoodsWriteService.updateBythirdIdAndShopId(thirdId, shopId, IntegralStatus.NORMAL.value());//改为生成积分码状态
            integralDataStatusWriteService.updateIntegralDataStatus(code, IntegralStatus.USEED.value());//已下载
//            ZipUtils.delAllFile(paranaConfig.getParanaLevelDistributionPath() + "/" + code);
//            ZipUtils.delFolderEmpty(paranaConfig.getParanaLevelDistributionPath() + "/" + code);
            IntegralOperationLog integralOperationLog = new IntegralOperationLog();
            integralOperationLog.setDesc("首次下载商品码");
            integralOperationLog.setUrl("/downLoadZipFile");
            integralOperationLogs.recordWriteOperationLog(request, integralOperationLog);
            out.close();
        }
    }

    /**
     * 历史下载区
     *
     * @param response
     * @param code
     * @throws IOException
     */
    @GetMapping("/downLoadZipFileHistory")
    @MyLog(title = "积分管理-历史商品码管理", value = "商家历史码下载积分码", opBusinessType = OpBusinessType.EXPORT)
    public void downLoadZipFilesHistory(HttpServletResponse response, String code, HttpServletRequest request) throws IOException {

        log.info("docker ip:{}", request.getRemoteAddr());
        log.info("out ip:{}", IPUtil.getIpAddr(request));


        Long userIds = getLoginUserId();//检测是否登录
        String zipName = DateUtil.getDateString() + ".zip";
        response.setContentType("APPLICATION/OCTET-STREAM");
        response.setHeader("Content-Disposition", "attachment;filename=" + zipName);
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "No-cache");
        response.setDateHeader("Expires", 0);
        ZipOutputStream out = new ZipOutputStream(response.getOutputStream());
        try {
            ZipUtils.doCompress(paranaConfig.getParanaLevelDistributionPath() + "/" + code, out);
            response.flushBuffer();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IntegralOperationLog integralOperationLog = new IntegralOperationLog();
            integralOperationLog.setDesc("历史下载商品码");
            integralOperationLog.setUrl("/downLoadZipFileHistory");
            integralOperationLogs.recordWriteOperationLog(request, integralOperationLog);
            out.close();
        }
    }


    private long getLoginUserId() {
        return Optional.ofNullable(UserUtil.getUserId()).orElseThrow(() -> new JsonResponseException("user.not.login"));
    }

}
