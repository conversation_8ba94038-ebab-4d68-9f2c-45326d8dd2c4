## 买家查询退货款

* 请求url
```
GET buyer.refund.query
```

* 请求参数示例
```
id=&startAt=2016-10-20&endAt=2016-11-14&statusStr=&pageNo=1&pageSize=20
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| id | 整形 | 退款单id | 否 |  |
| startAt | 字符串 | 申请日期开始范围 | 否 |  |
| endAt | 字符串 | 申请日期截止范围 | 否 |  |
| statusStr | 字符串 | 订单状态 | 否 | 多个状态之间用逗号分隔 |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": {
        "total": 4,
        "data": [
          {
            "refund": {
              "id": 389,
              "refundType": 0,
              "fee": 5000,
              "shopId": 1,
              "shopName": "第一个店铺",
              "buyerId": 2,
              "buyerName": "jacksdfsd1",
              "outId": "20161114131020000000000000000389",
              "integral": null,
              "balance": null,
              "status": -10,
              "refundSerialNo": null,
              "paymentId": 712,
              "tradeNo": "20161114130915000000000000000712",
              "paySerialNo": "20161114130915000000000000000712",
              "refundAccountNo": "1",
              "channel": "mockpay",
              "promotionId": null,
              "buyerNote": "sxcx",
              "sellerNote": null,
              "extra": null,
              "tags": null,
              "refundAt": null,
              "createdAt": *************,
              "updatedAt": *************
            },
            "skuOrders": [
              {
                "id": 1649,
                "shopId": 1,
                "buyerId": 2,
                "fee": 5000,
                "status": -10,
                "promotionId": null,
                "extra": null,
                "tags": null,
                "createdAt": *************,
                "updatedAt": *************,
                "skuId": 5721,
                "quantity": 1,
                "orderId": 1263,
                "outId": null,
                "buyerName": "jacksdfsd1",
                "outBuyerId": null,
                "itemId": 4,
                "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
                "skuImage": "http://parana.oss-cn-hangzhou.aliyuncs.com/2016/07/10/TB1VF8TJpXXXXbZXFXXXXXXXXXX_!!2-item_pic.png",
                "shopName": "第一个店铺",
                "outShopId": null,
                "companyId": null,
                "outSkuId": null,
                "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
                "skuAttrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "白色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "channel": 1,
                "payType": 1,
                "shipmentType": 1,
                "originFee": 5000,
                "discount": null,
                "shipFee": null,
                "shipFeeDiscount": null,
                "integral": null,
                "balance": null,
                "itemSnapshotId": 138,
                "hasRefund": true,
                "invoiced": false,
                "commented": null,
                "hasApplyAfterSale": null,
                "commissionRate": null
              }
            ],
            "operations": [
              {
                "value": -14,
                "text": "returnConfirm",
                "operator": [
                  "seller"
                ]
              },
              {
                "value": -13,
                "text": "returnReject",
                "operator": [
                  "seller"
                ]
              }
            ]
          },
          {
            "refund": {
              "id": 388,
              "refundType": 0,
              "fee": 5000,
              "shopId": 1,
              "shopName": "第一个店铺",
              "buyerId": 2,
              "buyerName": "jacksdfsd1",
              "outId": "20161114115503000000000000000388",
              "integral": null,
              "balance": null,
              "status": 2,
              "refundSerialNo": null,
              "paymentId": 711,
              "tradeNo": "20161114115112000000000000000711",
              "paySerialNo": "20161114115112000000000000000711",
              "refundAccountNo": "1",
              "channel": "mockpay",
              "promotionId": null,
              "buyerNote": "与描述不符",
              "sellerNote": null,
              "extra": null,
              "tags": null,
              "refundAt": null,
              "createdAt": *************,
              "updatedAt": *************
            },
            "skuOrders": [
              {
                "id": 1648,
                "shopId": 1,
                "buyerId": 2,
                "fee": 5000,
                "status": 2,
                "promotionId": null,
                "extra": null,
                "tags": null,
                "createdAt": *************,
                "updatedAt": *************,
                "skuId": 5721,
                "quantity": 1,
                "orderId": 1262,
                "outId": null,
                "buyerName": "jacksdfsd1",
                "outBuyerId": null,
                "itemId": 4,
                "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
                "skuImage": "http://parana.oss-cn-hangzhou.aliyuncs.com/2016/07/10/TB1VF8TJpXXXXbZXFXXXXXXXXXX_!!2-item_pic.png",
                "shopName": "第一个店铺",
                "outShopId": null,
                "companyId": null,
                "outSkuId": null,
                "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
                "skuAttrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "白色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "channel": 1,
                "payType": 1,
                "shipmentType": 1,
                "originFee": 5000,
                "discount": null,
                "shipFee": null,
                "shipFeeDiscount": null,
                "integral": null,
                "balance": null,
                "itemSnapshotId": 138,
                "hasRefund": false,
                "invoiced": false,
                "commented": null,
                "hasApplyAfterSale": null,
                "commissionRate": null
              }
            ],
            "operations": [
              {
                "value": 3,
                "text": "confirm",
                "operator": [
                  "buyer"
                ]
              },
              {
                "value": -8,
                "text": "returnApply",
                "operator": [
                  "buyer"
                ]
              }
            ]
          },
          {
            "refund": {
              "id": 387,
              "refundType": 0,
              "fee": 6000,
              "shopId": 1,
              "shopName": "第一个店铺",
              "buyerId": 2,
              "buyerName": "jacksdfsd1",
              "outId": "20161114114806000000000000000387",
              "integral": null,
              "balance": null,
              "status": 1,
              "refundSerialNo": null,
              "paymentId": 710,
              "tradeNo": "20161114114115000000000000000710",
              "paySerialNo": "20161114114115000000000000000710",
              "refundAccountNo": "1",
              "channel": "mockpay",
              "promotionId": null,
              "buyerNote": "太贵了",
              "sellerNote": null,
              "extra": null,
              "tags": null,
              "refundAt": null,
              "createdAt": *************,
              "updatedAt": *************
            },
            "skuOrders": [
              {
                "id": 1647,
                "shopId": 1,
                "buyerId": 2,
                "fee": 5000,
                "status": 1,
                "promotionId": null,
                "extra": null,
                "tags": null,
                "createdAt": *************,
                "updatedAt": *************,
                "skuId": 5721,
                "quantity": 1,
                "orderId": 1261,
                "outId": null,
                "buyerName": "jacksdfsd1",
                "outBuyerId": null,
                "itemId": 4,
                "itemName": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
                "skuImage": "http://parana.oss-cn-hangzhou.aliyuncs.com/2016/07/10/TB1VF8TJpXXXXbZXFXXXXXXXXXX_!!2-item_pic.png",
                "shopName": "第一个店铺",
                "outShopId": null,
                "companyId": null,
                "outSkuId": null,
                "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"白色\"}]",
                "skuAttrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "白色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "channel": 1,
                "payType": 1,
                "shipmentType": 1,
                "originFee": 5000,
                "discount": null,
                "shipFee": null,
                "shipFeeDiscount": null,
                "integral": null,
                "balance": null,
                "itemSnapshotId": 138,
                "hasRefund": false,
                "invoiced": false,
                "commented": null,
                "hasApplyAfterSale": null,
                "commissionRate": null
              }
            ],
            "operations": [
              {
                "value": -3,
                "text": "refundApply",
                "operator": [
                  "buyer"
                ]
              },
              {
                "value": 2,
                "text": "ship",
                "operator": [
                  "seller"
                ]
              }
            ]
          },
          {
            "refund": {
              "id": 354,
              "refundType": 0,
              "fee": 63600,
              "shopId": 18,
              "shopName": "护肤美妆专营店",
              "buyerId": 2,
              "buyerName": "buyer",
              "outId": "20160918071536000000000000000354",
              "integral": null,
              "balance": null,
              "status": 1,
              "refundSerialNo": null,
              "paymentId": 582,
              "tradeNo": "582",
              "paySerialNo": "582",
              "refundAccountNo": "18",
              "channel": "mockpay",
              "promotionId": null,
              "buyerNote": "",
              "sellerNote": null,
              "extra": null,
              "tags": null,
              "refundAt": null,
              "createdAt": *************,
              "updatedAt": *************
            },
            "skuOrders": [
              {
                "id": 1031,
                "shopId": 18,
                "buyerId": 2,
                "fee": 63600,
                "status": 1,
                "promotionId": null,
                "extra": null,
                "tags": null,
                "createdAt": *************,
                "updatedAt": *************,
                "skuId": 5760,
                "quantity": 4,
                "orderId": 757,
                "outId": null,
                "buyerName": "buyer",
                "outBuyerId": null,
                "itemId": 5001,
                "itemName": "it's skin伊思美肌红参蜗牛BB霜50ML 保湿裸妆遮瑕强韩国正品",
                "skuImage": "http://terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/14/9fe5515d-eb9f-4306-a751-f0439971026b.jpg",
                "shopName": "护肤美妆专营店",
                "outShopId": null,
                "companyId": null,
                "outSkuId": null,
                "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"红色\"}]",
                "skuAttrs": [
                  {
                    "attrKey": "颜色分类",
                    "attrVal": "红色",
                    "unit": null,
                    "showImage": null,
                    "thumbnail": null,
                    "image": null
                  }
                ],
                "channel": 2,
                "payType": 1,
                "shipmentType": 1,
                "originFee": 63600,
                "discount": null,
                "shipFee": null,
                "shipFeeDiscount": null,
                "integral": null,
                "balance": null,
                "itemSnapshotId": 154,
                "hasRefund": false,
                "invoiced": false,
                "commented": null,
                "hasApplyAfterSale": null,
                "commissionRate": 0
              }
            ],
            "operations": [
              {
                "value": -3,
                "text": "refundApply",
                "operator": [
                  "buyer"
                ]
              },
              {
                "value": 2,
                "text": "ship",
                "operator": [
                  "seller"
                ]
              }
            ]
          }
        ],
        "empty": false
      }
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询退款单失败"
     }
    ```



## 买家申请退款(不能跨订单申请退款,但是支持同一订单的多个子订单申请退款)

* 请求url
```
POST buyer.refund.apply
```

* 请求参数示例
```
orderIds=412&orderType=1&note=太贵了
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| orderIds | 字符串 | 订单id列表 | 是 |  |
| orderType | 整形 | 订单类型 | 否 | 1表示店铺订单,2表示子订单,默认为2 |
| note | 字符串 | 买家备注 | 否 |  |


* 返回结果集

  - 正确结果示例，返回生成的退款单ID,HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": 122
    }
    ```

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "申请退款失败"
     }
    ```



## 买家取消退款

* 请求url
```
PUT buyer.refund.cancel
```

* 请求参数示例
```
refundId=122
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| refundId | 整形 | 退款单ID | 是 |  |


* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": true
    }
    ```

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "取消退款失败"
     }
    ```