/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.common.exception;

import com.google.common.base.MoreObjects;
import lombok.Getter;

/**
 * <AUTHOR>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-27
 */
public class InvalidException extends RuntimeException {
    private static final long serialVersionUID = -3978990660036533916L;

    @Getter
    private final int status;

    /**
     * 校验失败信息
     */
    @Getter
    private final String error;

    /**
     * 校验失败的一些参数说明信息
     */
    @Getter
    private final Object[] params;

    public InvalidException(int status, String error, Object... params){
        super(error);
        this.status = status;
        this.error = error;
        this.params = params;
    }

    public InvalidException(String error, Object... param) {
        this(400, error, param);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("status", status)
                .add("error", error)
                .add("params", params)
                .omitNullValues()
                .toString();
    }
}
