package moonstone.web.admin.jobs.settle.config;

import moonstone.web.admin.jobs.settle.component.SettlePaymentJob;
import moonstone.web.admin.jobs.settle.component.SettleRefundJob;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.Date;

/**
 * DATE: 16/11/13 下午3:21 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
public class TestSettleTradeJob {
    private final SettlePaymentJob settlePaymentJob;
    private final SettleRefundJob settleRefundJob;

    @Autowired
    public TestSettleTradeJob(SettlePaymentJob settlePaymentJob,
                                 SettleRefundJob settleRefundJob) {
        this.settlePaymentJob = settlePaymentJob;
        this.settleRefundJob = settleRefundJob;
    }

    @Scheduled(cron = "${settle.cron.test.payment: 1 */3 *  * * * }")
    public void settlePayments() {
        Date startAt = DateTime.now().withTimeAtStartOfDay().toDate();
        Date endAt = DateTime.now().toDate();
        settlePaymentJob.settlePayments(startAt, endAt);
    }

    @Scheduled(cron = "${settle.cron.test.refund: 3 */3 * * * * }")
    public void settleRefunds() {
        Date startAt = DateTime.now().withTimeAtStartOfDay().toDate();
        Date endAt = DateTime.now().toDate();
        settleRefundJob.settleRefunds(startAt, endAt);
    }
}
