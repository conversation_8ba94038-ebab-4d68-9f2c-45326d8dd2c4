package blue.sea.moonstone.scheme;

import java.util.Objects;

public class Eq implements SchemeFuc {
    @Override
    public AST execute(Executor executor, AST ast) {
        if (ast.size() > 3) {
            throw new IllegalArgumentException("wrong argument size of eq at -> " + ast);
        }
        AST a = executor.eval(ast.lisp.get(1));
        AST b = executor.eval(ast.lisp.get(2));
        if (a.type != b.type) {
            return AST.ofAtom("#F");
        }
        // brute equals compare
        if (!Objects.equals(a.toString(), b.toString())) {
            return AST.ofAtom("#F");
        }
        return AST.ofAtom("#T");
    }
}
