package moonstone.web.core.fileNew.dto;

import lombok.Getter;
import lombok.Setter;
import moonstone.common.model.dto.PageDto;

/**
 * author：书生
 */
@Getter
@Setter
public class ItemSearchPageDto extends PageDto {

    /**
     * 区域id
     */
    private String areaId;
    /**
     * 商品名称
     */
    private String name;
    /**
     * 商品id
     */
    private Long itemId;
    /**
     * 商品编码
     */
    private String code;
}
