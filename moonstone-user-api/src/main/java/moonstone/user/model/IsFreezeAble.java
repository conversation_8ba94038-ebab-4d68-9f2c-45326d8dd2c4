package moonstone.user.model;


import lombok.Getter;

/**
 * 用于冻结判断,但是会被余下接口重构
 *  第 20 级状态
 * @serial 20
 */
public interface IsFreezeAble {
    // 依赖函数
    Integer getStatus();

    void setStatus(Integer status);

    // 默认的冻结表
    enum defaultFreezeMask {
        Normal(1), Freeze(2 << 20);
        @Getter
        int bitMask;

        defaultFreezeMask(int bit) {
            bitMask = bit;
        }
    }
    default boolean notFreeze(){
        return !isFreeze();
    }
    default boolean isFreeze() {
        return (defaultFreezeMask.Freeze.getBitMask() & getStatus()) == defaultFreezeMask.Freeze.getBitMask();
    }

    default void unFreeze() {
        if (isFreeze()) {
            setStatus(getStatus() & ~defaultFreezeMask.Freeze.getBitMask());
        }
    }

    default void freeze() {
        if (!isFreeze()) {
            setStatus(getStatus() | defaultFreezeMask.Freeze.getBitMask());
        }
    }

}
