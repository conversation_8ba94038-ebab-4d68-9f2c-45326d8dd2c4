package moonstone.user.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import moonstone.user.model.StoreIntegralGoods;
import org.springframework.stereotype.Repository;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/19 15:11
 */
@Repository
@Slf4j
public class StoreIntegralGoodsDao extends MyBatisDao<StoreIntegralGoods> {

    public StoreIntegralGoods findStoreIntegralGoodsById(Long id) {
        return getSqlSession().selectOne(sqlId("findStoreIntegralGoodsById"), ImmutableMap.of("id", id));
    }

    public StoreIntegralGoods findByThirdId(Long thirdId) {
        return getSqlSession().selectOne(sqlId("findByThirdId"), ImmutableMap.of("thirdId", thirdId));
    }

    public Boolean updateBythirdIdAndShopId(long thirdId, long shopId, int status) {
        return  getSqlSession().update(sqlId("updateBythirdIdAndShopId"), ImmutableMap.of("shopId", shopId,
                "thirdId", thirdId, "status", status)) == 1;
    }

    public StoreIntegralGoods findByBatch(String code) {
        return getSqlSession().selectOne(sqlId("findByBatch"), ImmutableMap.of("batch", code));
    }
}
