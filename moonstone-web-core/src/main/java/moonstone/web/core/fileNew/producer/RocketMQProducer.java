package moonstone.web.core.fileNew.producer;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.TransactionSendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * author：书生
 */
@Component
@Slf4j
public class RocketMQProducer {


    @Autowired
    private RocketMQTemplate rocketMQTemplate;


    public SendResult sendMessage(String topic, String tag, String msg) {
        Message<String> message = MessageBuilder.withPayload(msg).build();
        SendResult sendResult = rocketMQTemplate.syncSend(topic + ":" + tag, message);
        log.info("sendMessage to topic {} tag {} message {} , msgId {}", topic, tag, JSONUtil.toJsonStr(message), sendResult.getMsgId());
        return sendResult;
    }

    /**
     * 发送延迟消息
     *
     * @param topic      主题
     * @param tag        标签
     * @param msg        消息体
     * @param delayLevel 延迟级别   1s, 5s, 10s, 30s, 1m, 2m, 3m, 4m, 5m, 6m, 7m, 8m, 9m, 10m, 20m, 30m, 1h, 2h。
     */
    public void sendDelayMessage(String topic, String tag, String msg, int delayLevel) {
        Message<String> message = MessageBuilder.withPayload(msg).build();
        SendResult sendResult = rocketMQTemplate.syncSend(topic + ":" +tag, message,3000,delayLevel);
        log.info("sendDelayMessage to topic {} tag {} message {} , msgId {}", topic, tag, JSONUtil.toJsonStr(message), sendResult.getMsgId());
    }


    /**
     * 发送事务消息
     *
     * @param topic   主题
     * @param tag     标签
     * @param msg 消息体
     */
    public void sendTransactionMessage(String topic, String tag, String msg) {
        Message<String> message = MessageBuilder.withPayload(msg).build();
        TransactionSendResult sendResult = rocketMQTemplate.sendMessageInTransaction(topic + ":" + tag, message, null);
        log.info("sendTransactionMessage to topic {} tag {} message {} msgId {} transactionId {}",
                topic, tag, JSONUtil.toJsonStr(message), sendResult.getMsgId(),sendResult.getTransactionId());

    }


}
