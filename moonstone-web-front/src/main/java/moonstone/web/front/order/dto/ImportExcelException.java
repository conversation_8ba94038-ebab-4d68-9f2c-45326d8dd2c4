package moonstone.web.front.order.dto;

public class ImportExcelException extends RuntimeException{
    static final long serialVersionUID = -129380123810238L;
    Integer line;
    String msg;
    public ImportExcelException()
    {}

    public ImportExcelException(Integer line, String msg) {
        this.line = line;
        this.msg = msg;
    }

    public ImportExcelException(String s, Integer line, String msg) {
        super(s);
        this.line = line;
        this.msg = msg;
    }

    public ImportExcelException(String s, Throwable throwable, Integer line, String msg) {
        super(s, throwable);
        this.line = line;
        this.msg = msg;
    }

    public ImportExcelException(Throwable throwable, Integer line, String msg) {
        super(throwable);
        this.line = line;
        this.msg = msg;
    }

    public ImportExcelException(String s, Throwable throwable, boolean b, boolean b1, Integer line, String msg) {
        super(s, throwable, b, b1);
        this.line = line;
        this.msg = msg;
    }

    public Integer getLine() {
        return line;
    }

    public String getMsg() {
        return msg;
    }
}
