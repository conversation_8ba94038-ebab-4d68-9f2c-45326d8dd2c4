package moonstone.order.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.order.model.HonestFanSum;
import moonstone.order.service.HonestFanDataWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Date;

@Slf4j
@Service
@RpcProvider
public class HonestFanDataWriteServiceImpl implements HonestFanDataWriteService {
    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public Either<Boolean> create(HonestFanSum entity) {
        try {

            entity.setLastBoughtTime(Date.from(Instant.now()));
            mongoTemplate.insert(entity);
            return Either.ok(true);

        } catch (Exception ex) {
            log.error("{} entity:{}", LogUtil.getClassMethodName(), entity);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> increaseData(long userId, long shopId, long tradeFee, long income, long honestIncome) {
        try {
            Query query = Query.query(Criteria.where("userId").is(userId)
                    .andOperator(Criteria.where("shopId").is(shopId))
            );
            Update update = new Update();
            if (tradeFee > 0) {
                update.currentDate("lastBoughtTime");
            }
            update.inc("tradeTimes", Long.compare(tradeFee, 0L));
            update.inc("tradeFeeSum", tradeFee)
                    .inc("incomeSum", income)
                    .inc("honestIncomeSum", honestIncome);
            mongoTemplate.updateFirst(query, update, HonestFanSum.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{} userId:{} shopId:{} tradeFee:{} income:{}", LogUtil.getClassMethodName(), userId, shopId, tradeFee, income);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> increaseDataByIntegral(Long proxyId, long shopId, Long integralFee) {
        try {
            Query query = Query.query(Criteria.where("userId").is(proxyId)
                    .andOperator(Criteria.where("shopId").is(shopId))
            );
            Update update = new Update();
            update.inc("incomeSum", integralFee);
            update.currentDate("lastBoughtTime");
            mongoTemplate.updateFirst(query, update, HonestFanSum.class);
            return Either.ok(true);
        } catch (Exception ex) {
            log.error("{} userId:{} shopId:{}  income:{}", LogUtil.getClassMethodName(), proxyId, shopId, integralFee);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> updateByShopIdAndProxyId(Long shopId, Long userId, HonestFanSum fanSum) {
        try {
            Query query = Query.query(Criteria.where("shopId").is(shopId)).addCriteria(Criteria.where("userId").is(userId));
            Update update = new Update().set("proxyId", fanSum.getProxyId())
                    .set("tradeTimes", fanSum.getTradeTimes())
                    .set("tradeFeeSum", fanSum.getTradeFeeSum())
                    .set("incomeSum", fanSum.getIncomeSum())
                    .set("honestIncomeSum", fanSum.getHonestIncomeSum())
                    .set("lastBoughtTime", fanSum.getLastBoughtTime());
            return Either.ok(mongoTemplate.updateMulti(query, update, HonestFanSum.class).getModifiedCount() > 0);
        } catch (Exception ex) {
            ex.printStackTrace();
            return Either.error(ex);
        }
    }
}
