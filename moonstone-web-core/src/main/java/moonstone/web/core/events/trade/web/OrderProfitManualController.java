package moonstone.web.core.events.trade.web;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.web.core.events.trade.app.OrderProfitActionRecordApp;
import moonstone.web.core.events.trade.model.OrderProfitActionRecord;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@AllArgsConstructor
@RestController
@Slf4j
public class OrderProfitManualController {
    OrderProfitActionRecordApp orderProfitActionRecordApp;
    MongoTemplate mongoTemplate;

    @GetMapping("/api/profit/event/action")
    public APIResp<List<OrderProfitActionRecord>> listEvent(){
        return APIResp.ok(mongoTemplate.findAll(OrderProfitActionRecord.class));
    }

    @PostMapping("/api/profit/event/action")
    public APIResp<Boolean> addEvent(Long orderId, Integer status){
        switch (OrderProfitActionRecord.Status.from(status)){
            case PRESENT:
                orderProfitActionRecordApp.recordConfirmProfitOrder(orderId);
                break;
            case FORESEE:
                orderProfitActionRecordApp.recordForeseeProfitOrder(orderId);
                break;
            case REFUND:
                orderProfitActionRecordApp.recordRefundProfitOrder(orderId);
                break;
            default:
                return APIResp.error("NOT SUPPORT");
        }
        return APIResp.ok(true);
    }
}
