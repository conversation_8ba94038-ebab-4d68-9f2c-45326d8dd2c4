CREATE TABLE parana.parana_intermediate_info (
	id BIGINT NULL AUTO_INCREMENT,
	third_id BIGINT not NULL COMMENT '第三方id',
	first_rate  BIGINT  NULL DEFAULT 0 COMMENT '一级佣金率',
	first_fee  BIGINT  NULL DEFAULT 0 COMMENT '一级佣金',
	second_rate BIGINT  NULL DEFAULT 0 COMMENT '二级佣金率',
	second_fee BIGINT  NULL DEFAULT 0 COMMENT '二级佣金',
	commission BIGINT  NULL DEFAULT 0 COMMENT '拉新佣金',
	is_commission INT NULL COMMENT '是否开启单品佣金0-不开启 1-开启',
	type INT NULL COMMENT '类型',
	status INT NULL COMMENT '状态',
	extra_json varchar(255) NULL COMMENT '拓展信息',
	created_at DATETIME NULL COMMENT '创建时间',
	updated_at DATETIME NULL COMMENT '修改时间',
	CONSTRAINT parana_intermediate_info_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4 COMMENT='拓传参数中间表'
COLLATE=utf8mb4_general_ci;
CREATE INDEX parana_intermediate_info_third_id_IDX USING BTREE ON parana.parana_intermediate_info (third_id);
