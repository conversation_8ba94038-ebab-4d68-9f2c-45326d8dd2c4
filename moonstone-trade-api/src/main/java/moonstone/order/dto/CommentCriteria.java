package moonstone.order.dto;

import com.google.common.base.MoreObjects;
import lombok.Getter;
import lombok.Setter;
import moonstone.common.model.BaseCriteria;

import java.io.Serializable;
import java.util.Date;

/**
 * Desc:
 * Mail: <EMAIL>
 * Data: 16/7/7
 * Author: yangzefeng
 */
public class CommentCriteria extends BaseCriteria implements Serializable {
    private static final long serialVersionUID = -7120773978433668319L;

    @Getter
    @Setter
    private Long userId;

    @Getter
    @Setter
    private Long skuOrderId;

    @Getter
    @Setter
    private Long itemId;

    @Getter
    @Setter
    private Long shopId;

    @Getter
    @Setter
    private Integer status;

    @Getter
    @Setter
    private Boolean hasDisplay;

    @Getter @Setter
    private Date startAt;

    @Getter @Setter
    private Date endAt;

    @Getter @Setter
    private Integer pageNo;

    @Getter @Setter
    private Integer size;

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("userId", userId)
                .add("skuOrderId", skuOrderId)
                .add("itemId", itemId)
                .add("shopId", shopId)
                .toString();
    }
}
