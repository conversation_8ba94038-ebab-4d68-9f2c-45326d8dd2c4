package moonstone.web.front.distribution.app;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.ImmutableMap;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.user.dto.EncryptImage;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.component.wx.WxAccessTokenCacheHolder;
import moonstone.web.core.files.service.OSSClientService;
import moonstone.wxOpen.service.WxOpenParanaComponentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.Ellipse2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class WechatAppImageRender {
    @Autowired
    WxAccessTokenCacheHolder wxAccessTokenCacheHolder;
    @Autowired
    ShopWxaCacheHolder shopWxaCacheHolder;
    @Autowired
    WxOpenParanaComponentService wxOpenParanaComponentService;
    @Autowired
    OSSClientService ossClientService;
    @Autowired
    MongoTemplate mongoTemplate;


    LoadingCache<String, ByteArrayOutputStream> imageCache = Caffeine
            .newBuilder().expireAfterWrite(3, TimeUnit.MINUTES)
            .maximumSize(10).build(this::findProjectImageBuff);

    private BufferedImage cutImageFromShareImage(Long projectId, int size) {
        ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
        String appId = shopWxa.getAppId();
        // read the WeChat App head Image
        String accessToken = Optional.of(shopWxa).map(ShopWxa::getAppSecret).map(appSecret ->
                wxAccessTokenCacheHolder.getAccessToken(appId, appSecret).take())
                .orElseGet(() -> {
                    try {
                        return wxOpenParanaComponentService.getAuthorizerAccessToken(appId, false);
                    } catch (Exception e) {
                        return Either.<String>error(e).take();
                    }
                });
        int bigSize = 560 * size / 240;
        int shift = 160 * size / 240;
        int realSize = bigSize - 2 * shift;
        String accessTokenGainUrlApi = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken;
        Map<String, String> args = ImmutableMap.of("scene", "itemId=", "width", bigSize + "");
        HttpRequest request = HttpRequest.post(accessTokenGainUrlApi).contentType("application/json", "utf-8").send(JSON.toJSONBytes(args));
        if (!request.ok()) {
            log.error("{} fail to render the WeChat App head Image ProjectId => {}", LogUtil.getClassMethodName(), projectId);
            throw Translate.exceptionOf("获取微信分享图片失败");
        }
        BufferedImage shareImage = null;
        ByteArrayOutputStream buff = new ByteArrayOutputStream();
        try {
            request.receive(buff);
            shareImage = ImageIO.read(new ByteArrayInputStream(buff.toByteArray()));
        } catch (Exception ex) {
            log.error("{} fail to create shopShare image", LogUtil.getClassMethodName(), ex);
        }
        if (shareImage == null) {
            throw Translate.exceptionOf(buff.toString());
        }
        BufferedImage circleDrawGround = new BufferedImage(bigSize, bigSize, BufferedImage.TYPE_4BYTE_ABGR);
        Graphics draw = circleDrawGround.createGraphics();
        Ellipse2D circle = new Ellipse2D.Float(shift, shift, realSize, realSize);
        draw.setClip(circle);
        draw.setColor(new Color(0, 0, 0, 0));
        draw.drawImage(shareImage, 0, 0, null);
        draw.dispose();
        BufferedImage cut = new BufferedImage(realSize, realSize, BufferedImage.TYPE_4BYTE_ABGR);
        Graphics graphics = cut.createGraphics();
        graphics.setColor(new Color(0, 0, 0, 0));
        graphics.drawImage(circleDrawGround, -shift, -shift, null);
        graphics.dispose();
        return cut;
    }

    public void clearImageForProject(Long projectId) {
        imageCache.invalidate(projectId);
        mongoTemplate.findAllAndRemove(Query.query(Criteria.where("projectId").is(projectId)),
                WechatAppCircleHeadImage.class);
    }

    public void renderImage(Long projectId, int size, OutputStream outputStream) throws IOException {
        outputStream.write(Objects.requireNonNull(imageCache.get(projectId + "," + size)).toByteArray());
    }

    private ByteArrayOutputStream findProjectImageBuff(String arg) throws IOException {
        Long projectId = Long.parseLong(arg.split(",")[0]);
        int size = Integer.parseInt(arg.split(",")[1]);
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        WechatAppCircleHeadImage wechatAppCircleHeadImage = mongoTemplate.findOne(Query.query(Criteria.where("size").is(size))
                .addCriteria(Criteria.where("projectId").is(projectId)), WechatAppCircleHeadImage.class);
        if (wechatAppCircleHeadImage == null) {
            BufferedImage image = cutImageFromShareImage(projectId, size);
            ossClientService.uploadNoEncryptImage(image, projectId.toString()).map(EncryptImage::getDataUrl)
                    .ifSuccess(url ->
                            mongoTemplate.upsert(Query.query(Criteria.where("size").is(size))
                                            .addCriteria(Criteria.where("projectId").is(projectId)),
                                    Update.update("url", url),
                                    WechatAppCircleHeadImage.class
                            ));

            ImageIO.write(image, "png", outputStream);
        } else {
            HttpRequest request = HttpRequest.get(wechatAppCircleHeadImage.getUrl());
            if (request.ok()) {
                request.receive(outputStream);
            } else {
                throw Translate.exceptionOf("网络连接异常[%s]", request.code());
            }
        }
        return outputStream;
    }

    @Data
    static class WechatAppCircleHeadImage {
        String id;
        Long projectId;
        Long size;
        String url;
        Date at;
    }
}
