package moonstone.web.admin.jobs;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.api.OrderReadLogic;
import moonstone.order.dto.OrderDetail;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.Payment;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.SkuOrderReadService;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.model.User;
import moonstone.web.core.order.OrderWriteLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class OrderSeal {
    @Autowired
    OrderWriteLogic orderWriteLogic;
    @Autowired
    OrderReadLogic orderReadLogic;
    @Autowired
    SkuOrderReadService skuOrderReadService;
    @Autowired
    UserCacheHolder userCacheHolder;
    @Autowired
    MongoTemplate mongoTemplate;

    @Data
    static class StrangeOrderInfo {
        Long orderId;
        String info;
        String date;
        Long instant;
    }

//    @Scheduled(cron = "0 0 2 * * ?")
    public void sealOrder(){
        // 封存异常单, 所有超过半年以上未发货也未退款的都是异常单
        List<Long> strangeOrderList = orderReadLogic.findStrangeOrderBefore(LocalDateTime.now().minusMonths(6).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        for (Long orderId : strangeOrderList) {
            try {
                OrderDetail orderDetail  = orderReadLogic.orderDetail(orderId).getResult();
                ShopOrder shopOrder = orderDetail.getShopOrder();
                String paySerialNo = Optional.ofNullable(orderDetail.getPayment()).map(Payment::getPaySerialNo).orElse("未支付?");
                String mobile = userCacheHolder.findByUserId(shopOrder.getBuyerId()).map(User::getMobile).orElse("手机号丢失");
                log.info("订单 [{} : {}] 总价 {}, 来源[{} : {}] 支付渠道 {}, 支付信息 {}, 原状态 {}, 买家[{} : {}] 进入异常订单状态", shopOrder.getDeclaredId(), shopOrder.getId(), shopOrder.getFee(), shopOrder.getShopName(), shopOrder.getShopId(),
                        shopOrder.getChannel(), paySerialNo, OrderStatus.fromInt(shopOrder.getStatus()), shopOrder.getBuyerId(), mobile);
                orderWriteLogic.turnItAsStrangeOrder(orderId);
                StrangeOrderInfo strangeOrderInfo = new StrangeOrderInfo();
                strangeOrderInfo.setOrderId(orderId);
                LocalDateTime n = LocalDateTime.now();
                strangeOrderInfo.setDate(n.toString());
                strangeOrderInfo.setInstant(n.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
                strangeOrderInfo.setInfo(String.format("订单 [%s : %s] 总价 %s, 来源[%s : %s] 支付渠道 %s, 支付信息 %s, 原状态 %s, 买家[%s : %s] 进入异常订单状态", shopOrder.getDeclaredId(), shopOrder.getId(), shopOrder.getFee(), shopOrder.getShopName(), shopOrder.getShopId(),
                        shopOrder.getChannel(), paySerialNo, OrderStatus.fromInt(shopOrder.getStatus()), shopOrder.getBuyerId(), mobile));
                mongoTemplate.insert(strangeOrderInfo);
            }catch (Exception e){
                log.error("FAIL TO TURN THE ORDER {} INTO STRANGE", orderId, e);
            }
        }
    }
}
