package moonstone.order.service;

import com.danding.common.search.PageParam;
import com.danding.common.search.SearchResult;
import moonstone.common.model.SysOperLog;
import moonstone.user.criteria.SysOperCriteria;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/10/21 19:24
 */
public interface SysOperLogReadService {

    SearchResult<SysOperLog> findSysOperLogAll(PageParam pageParam, SysOperCriteria sysOperCriteria);

}
