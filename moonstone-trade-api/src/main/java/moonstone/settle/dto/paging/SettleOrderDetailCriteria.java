package moonstone.settle.dto.paging;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;
import org.joda.time.DateTime;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单明细 查询条件
 * Created with IntelliJ IDEA
 * Author: songrenfei
 * Date: 7/21/16
 * Time: 3:14 PM
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SettleOrderDetailCriteria extends PagingCriteria implements Serializable {

    private static final long serialVersionUID = -5457316572691284120L;

    /**
     * 支付起始时间
     */
    private Date paidAtStart;

    /**
     * 支付截止时间
     */
    private Date paidAtEnd;

    /**
     * 支付起始时间戳
     */
    private Long paidAtStartTimestamp;

    /**
     * 支付截止时间戳
     */
    private Long paidAtEndTimestamp;

    /**
     * 对账状态 {@link moonstone.settle.enums.CheckStatus}
     */
    private Integer checkStatus;

    /**
     * 对账起始时间
     */
    private Date checkAtStart;

    /**
     * 对账截止时间
     */
    private Date checkAtEnd;

    /**
     * 对账起始时间戳
     */
    private Long checkAtStartTimestamp;

    /**
     * 对账截止时间戳
     */
    private Long checkAtEndTimestamp;

    /**
     * 汇总开始时间
     */
    private Date sumAtStart;

    /**
     * 汇总截止时间
     */
    private Date sumAtEnd;

    /**
     * 汇总起始时间戳
     */
    private Long sumAtStartTimestamp;

    /**
     * 汇总截止时间戳
     */
    private Long sumAtEndTimestamp;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 商家ID
     */
    private Long sellerId;

    /**
     * 订单明细类型
     */
    private Integer orderType;

    /**
     * 如果Start的时间和End的时间一致, 则End+1day
     */
    @Override
    public void formatDate(){
        if(paidAtStart != null && paidAtEnd != null){
            if(paidAtStart.equals(paidAtEnd)){
                paidAtEnd=new DateTime(paidAtEnd.getTime()).plusDays(1).minusSeconds(1).toDate();
            }
        }

        if(checkAtStart != null && checkAtEnd != null){
            if(checkAtStart.equals(checkAtEnd)){
                checkAtEnd=new DateTime(checkAtEnd.getTime()).plusDays(1).minusSeconds(1).toDate();
            }
        }

        if(sumAtStart != null && sumAtEnd != null){
            if(sumAtStart.equals(sumAtEnd)){
                sumAtEnd=new DateTime(sumAtEnd.getTime()).plusDays(1).minusSeconds(1).toDate();
            }
        }
    }
}
