package moonstone.search.weShopItem;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Created by CaiZhy on 2018/12/24.
 */
@ConfigurationProperties(prefix = "we-shop-item.search")
@Getter
@Setter
public class SearchWeShopItemProperties {

    /**
     * 商品的索引名称, 可能是alias
     */
    private String indexName = "we_shop_items";

    /**
     * 商品的索引类型
     */
    private String indexType = "we_shop_item";

    /**
     * 对应商品类型的索引文件路径, 用以初始化索引的mapping, 默认为 ${indexType}_mapping.json
     */
    private String mappingPath;

    /**
     * 全量dump索引时, 最多涉及多少天之前有更新的商品
     */
    private Integer fullDumpRange = 3;

    /**
     * 每次批量处理多少个商品
     */
    private Integer batchSize = 100;
}
