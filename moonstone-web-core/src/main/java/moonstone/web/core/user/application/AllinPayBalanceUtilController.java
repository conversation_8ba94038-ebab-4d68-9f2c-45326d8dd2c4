package moonstone.web.core.user.application;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.web.core.component.pay.allinpay.user.AllinPayAccountBindService;
import moonstone.web.core.component.pay.allinpay.user.dto.AllinPayLoginUserRegisterAccountDTO;
import moonstone.web.core.component.pay.allinpay.user.req.*;
import moonstone.web.core.component.pay.allinpay.user.res.AccountAllinPayQueryVO;
import moonstone.web.core.component.pay.allinpay.user.res.CardBinVo;
import moonstone.web.op.allinpay.dto.*;
import moonstone.web.op.allinpay.service.IAllinpayService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("allinPay/utils")
@ApiModel(value = "通联支付工具接口")
public class AllinPayBalanceUtilController {

	@Resource
	private IAllinpayService allinpayService;

	/**
	 * 查用户余额
	 * @param shopId
	 * @param bizUserId
	 * @param accountSetNo
	 * @return
	 */
	@GetMapping("queryBalance")
	public Result<String> queryBalance(@RequestParam Long shopId, @RequestParam String bizUserId, @RequestParam String accountSetNo){
		return allinpayService.queryBalance(shopId, bizUserId, accountSetNo);
	}
//	@GetMapping("queryMerchantBalance")
//	public Result<String> queryMerchantBalance(@RequestParam Long shopId, @RequestParam String accountSetNo){
//		return allinpayService.queryMerchantBalance(shopId, accountSetNo);
//	}

	/**
	 * 查平台余额
	 * @param shopId
	 * @param sysId
	 * @return
	 */
	@GetMapping("queryReserveFundBalance")
	public Result<String> queryReserveFundBalance(@RequestParam Long shopId, @RequestParam String sysId){
		return allinpayService.queryReserveFundBalance(shopId, sysId);
	}

	/**
	 * 查询账户收支明细
	 * @param shopId
	 * @param dto
	 * @return
	 */
	@GetMapping("queryInExpDetail")
	public Result queryInExpDetail(@RequestParam Long shopId, @RequestBody QueryInExpDetailDTO dto){
		Result<String> result = allinpayService.queryInExpDetail(shopId, dto);
		if(result.isSuccess()){
			return Result.data(JSONObject.parseObject(result.getData()));
		}
		return result;
	}

	/**
	 * 订单分账明细查询
	 * @param shopId
	 * @param dto
	 * @return
	 */
	@GetMapping("getOrderSplitRuleListDetail")
	public Result getOrderSplitRuleListDetail(@RequestParam Long shopId, @RequestBody GetOrderSplitRuleListDetailDTO dto){
		Result<String> result =  allinpayService.getOrderSplitRuleListDetail(shopId, dto);
		if(result.isSuccess()){
			return Result.data(JSONObject.parseObject(result.getData()));
		}
		return result;
	}

	/**
	 * 付款方资金代付明细查询
	 * @param shopId
	 * @param dto
	 * @return
	 */
	@GetMapping("getPaymentInformationDetail")
	public Result getPaymentInformationDetail(@RequestParam Long shopId, @RequestBody GetPaymentInformationDetailDTO dto){
		Result<String> result =  allinpayService.getPaymentInformationDetail(shopId, dto);
		if(result.isSuccess()){
			return Result.data(JSONObject.parseObject(result.getData()));
		}
		return result;
	}

	/**
	 * 收款方在途资金明细查询
	 * @param shopId
	 * @param dto
	 * @return
	 */
	@GetMapping("getPayeeFundsInTransitDetail")
	public Result getPayeeFundsInTransitDetail(@RequestParam Long shopId, @RequestBody GetPayeeFundsInTransitDetailDTO dto){
		Result<String> result =  allinpayService.getPayeeFundsInTransitDetail(shopId, dto);
		if(result.isSuccess()){
			return Result.data(JSONObject.parseObject(result.getData()));
		}
		return result;
	}

	/**
	 * 收银宝商户资金查询
	 * @param shopId
	 * @param dto
	 * @return
	 */
	@GetMapping("queryVSPFund")
	public Result queryVSPFund(@RequestParam Long shopId, @RequestBody QueryVSPFundDTO dto){
		Result<String> result =  allinpayService.queryVSPFund(shopId, dto);
		if(result.isSuccess()){
			return Result.data(JSONObject.parseObject(result.getData()));
		}
		return result;
	}

	/**
	 * 下载商户对账文件
	 * @param shopId
	 * @param date
	 * @param fileType
	 * @return
	 */
	@GetMapping("getCheckAccountFile")
	public Result getCheckAccountFile(@RequestParam Long shopId, @RequestParam String date, @RequestParam Long fileType){
		Result<String> result =  allinpayService.getCheckAccountFile(shopId, date, fileType);
		if(result.isSuccess()){
			return Result.data(JSONObject.parseObject(result.getData()));
		}
		return result;
	}

}
