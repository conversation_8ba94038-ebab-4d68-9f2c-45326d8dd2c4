/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.item.dto;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.common.enums.ImageUrlTransformType;
import moonstone.common.utils.ImageUrlHandler;

import java.io.Serializable;
import java.util.List;

/**
 * 用户上传图片信息, 主要包括图片名称, url, 其中url是必选的, 其他可选
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImageInfo implements Serializable {
    private static final long serialVersionUID = -1023779755022441354L;

    /**
     * 图片名称, 用户自定义, 如侧面图, 正面图等, 非必选
     */
    private String name;

    /**
     * 图片对应的url, 必选
     */
    private String url;

    /**
     * 简化url
     *
     * @param imageInfos 图片信息
     * @return url简化后的图片信息
     * @see ImageUrlHandler#simplify
     */
    public static List<ImageInfo> simplifyImageUrl(List<ImageInfo> imageInfos) {
        return transform(imageInfos, ImageUrlTransformType.SIMPLIFY);
    }

    public static List<ImageInfo> completeImageUrl(List<ImageInfo> imageInfos) {
        return transform(imageInfos, ImageUrlTransformType.COMPLETE);
    }

    private static List<ImageInfo> transform(List<ImageInfo> imageInfos, ImageUrlTransformType transformType) {
        if (imageInfos == null) {
            return null;
        }
        List<ImageInfo> tansformedImageInfos = Lists.newArrayListWithCapacity(imageInfos.size());
        for (ImageInfo imageInfo : imageInfos) {
            ImageInfo transformedImageInfo = new ImageInfo();
            transformedImageInfo.setName(imageInfo.getName());
            transformedImageInfo.setUrl(ImageUrlHandler.handle(imageInfo.getUrl(), transformType));
            tansformedImageInfos.add(transformedImageInfo);
        }
        return tansformedImageInfos;
    }
}
