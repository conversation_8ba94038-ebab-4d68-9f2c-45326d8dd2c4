package moonstone.web.front.shop.substore.component;

import io.vertx.core.Vertx;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.OrderBase;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.related.OrderRelated;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.front.profit.view.ProfitViewForSubStoreModel;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class ServiceProviderProfitExporter {
    Vertx vertx;
    BalanceDetailReadService balanceDetailReadService;
    SubStoreCache subStoreCache;
    ServiceProviderCache serviceProviderCache;
    GuiderCache guiderCache;

    /**
     * query the order profit
     *
     * @param orderList order
     * @return profit view
     */
    public List<ProfitViewForSubStoreModel> queryProfitForOrder(List<ShopOrder> orderList) {
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setRelatedIds(orderList.stream().map(OrderBase::getId).collect(Collectors.toList()));
        criteria.setStatusBitMarks(Collections.singletonList(OrderRelated.orderRelatedMask.ShopOrder.getValue()));
        criteria.setType(1);
        List<BalanceDetail> balanceDetailList = balanceDetailReadService.paging(criteria).getResult().getData();
        Map<Long, ShopOrder> orderMap = orderList.stream().collect(Collectors.toMap(OrderBase::getId, it -> it));
        Map<Long, ProfitViewForSubStoreModel> viewMap = new HashMap<>();
        for (BalanceDetail balanceDetail : balanceDetailList) {
            ProfitViewForSubStoreModel view = viewMap.computeIfAbsent(balanceDetail.getRelatedId(),
                    id -> new ProfitViewForSubStoreModel());
            ShopOrder order = orderMap.get(balanceDetail.getRelatedId());
            view.setOrderId(balanceDetail.getId());
            view.setOrderStatus(OrderStatus.fromInt(order.getStatus()).intoString());
            view.setPrice(new BigDecimal(order.getFee())
                    .divide(new BigDecimal("100"),2,RoundingMode.UP));
            if (balanceDetail.isPresent()) {
                view.setConfirmedAt(balanceDetail.getCreatedAt());
            }
            if (!balanceDetail.isPresent()) {
                view.setPaidAt(balanceDetail.getCreatedAt());
            }
            view.setOrderAt(order.getCreatedAt());
            // set the profit
            subStoreCache.findByShopIdAndUserId(order.getShopId(), balanceDetail.getUserId())
                    .ifPresent(subStore -> {
                        view.setSubStoreName(subStore.getName());
                        view.setSubStoreProfit(new BigDecimal(balanceDetail.getChangeFee())
                                .divide(new BigDecimal("100"), 2, RoundingMode.UP));
                    });
            serviceProviderCache.findServiceProviderByShopIdAndUserId(order.getShopId(), balanceDetail.getUserId())
                    .ifPresent(provider -> {
                        view.setServiceProviderProfit(new BigDecimal(balanceDetail.getChangeFee())
                                .divide(new BigDecimal("100"), 2, RoundingMode.UP));
                        view.setServiceProviderName(provider.getRelation().getExtra().get("name"));
                    });
            guiderCache.findByShopIdAndUserId(order.getShopId(), balanceDetail.getUserId())
                    .ifPresent(guider -> {
                        view.setGuiderProfit(new BigDecimal(balanceDetail.getChangeFee())
                                .divide(new BigDecimal("100"), 2, RoundingMode.UP));
                        view.setGuiderName(guider.getStoreGuiderNickname());
                    });
            viewMap.putIfAbsent(order.getId(), view);
        }
        return new ArrayList<>(viewMap.values());
    }
}
