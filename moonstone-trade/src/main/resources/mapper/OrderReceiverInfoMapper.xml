<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="OrderReceiverInfo">

    <resultMap id="OrderReceiverInfoMap" type="OrderReceiverInfo">
        <id column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="order_type" property="orderType"/>
        <result column="hash" property="hash"/>
        <result column="referer_id" property="refererId"/>
        <result column="receiver_info_json" property="receiverInfoJson"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_order_receiver_infos
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `order_id`,`order_type`,`receiver_info_json`, `referer_id`, `hash`,`created_at`,`updated_at`
    </sql>

    <sql id="vals">
        #{orderId},#{orderType},#{receiverInfoJson},#{refererId},#{hash},now(),now()
    </sql>

    <select id="findRefererId" parameterType="map" resultType="long">
        SELECT
        referer_id
        FROM
        <include refid="tb"/>
        <where>
            order_id = #{orderId} AND order_type = #{orderType}
        </where>
    </select>

    <insert id="create" parameterType="OrderReceiverInfo" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findById" parameterType="long" resultMap="OrderReceiverInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByOrderIdAndOrderType" parameterType="map" resultMap="OrderReceiverInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `order_id` = #{orderId} AND `order_type` = #{orderType}
        ORDER BY id  DESC
    </select>

    <select id="findByOrderIdsAndOrderType" parameterType="map" resultMap="OrderReceiverInfoMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `order_id` in
         <foreach collection="orderIds" open="(" close=")" separator="," item="item">
             #{item}
         </foreach>
        AND `order_type` = #{orderType}

    </select>


    <update id="update" parameterType="OrderReceiverInfo">
        UPDATE
        <include refid="tb"/>
        SET
        updated_at = now(),
        `receiver_info_json` = #{receiverInfoJson}
        <if test="hash != null">
            ,`hash` = #{hash}
        </if>
        <if test="refererId != null">
            ,`referer_id` = #{refererId}
        </if>
        WHERE id=#{id}
    </update>


    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>


</mapper>