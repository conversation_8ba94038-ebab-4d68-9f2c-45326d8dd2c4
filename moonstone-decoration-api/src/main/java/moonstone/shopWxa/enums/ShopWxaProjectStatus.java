package moonstone.shopWxa.enums;

import com.google.common.base.Objects;

/**
 * Created by CaiZhy on 2018/11/12.
 */
public enum ShopWxaProjectStatus {

    DELETED(-1),            //删除
    WAITING_DECORATE(1),    //待装修
    WAITING_UPLOAD(2),      //待上传
    UPLOADED(3);            //已上传

    private final int value;

    ShopWxaProjectStatus(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static ShopWxaProjectStatus fromInt(int value){
        for (ShopWxaProjectStatus shopWxaProjectStatus : ShopWxaProjectStatus.values()) {
            if(Objects.equal(shopWxaProjectStatus.value, value)){
                return shopWxaProjectStatus;
            }
        }
        throw new IllegalArgumentException("unknown status: "+value);
    }
}
