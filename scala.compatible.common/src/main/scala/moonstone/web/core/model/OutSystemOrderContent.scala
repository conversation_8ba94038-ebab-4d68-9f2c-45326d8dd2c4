package moonstone.web.core.model

import java.math.BigDecimal
import java.util
import java.util.Map.Entry

/**
 * 外部订单内容
 *
 * @param orderId     主订单单号 标志这个是哪个主订单的内容
 * @param skuId       系统内部skuId 用于系统内部查询
 * @param skuOrderId  系统子订单单号(不向外提供)
 * @param skuCode     sku编码,用于与外部系统通讯
 * @param quantity    数量 该订单商品的数量
 * @param originPrice 原价 该单品订单的原件
 * @param price       现价格 该单品支付该使用的金额(可能会和订单总价不同)
 * @param shipFee     邮费
 * @param tax         税 需要缴纳多少税费
 * @param discount    优惠,原价 - 现价格
 * @param custom      关区
 * @param remark      备注 比如不发货什么的
 * @param info        额外信息,用于标记其他信息
 */
case class OutSystemOrderContent(_id: String
                                 , orderId: String
                                 , skuOrderId: Long
                                 , skuId: Long
                                 , skuCode: String
                                 , quantity: Int
                                 , originPrice: BigDecimal
                                 , price: BigDecimal
                                 , shipFee: BigDecimal
                                 , tax: BigDecimal
                                 , custom: String
                                 , discount: BigDecimal
                                 , remark: String
                                 , var info: Map[String, String]) {

  def setInfo(javaMap: util.HashMap[String, String]): Unit = {
    info = javaMap.entrySet().toArray(Array.empty[Entry[String, String]])
      .map(entry => (entry.getKey, entry.getValue)).toMap
  }

  def getInfo: util.HashMap[String, String] = {
    val javaMap = new util.HashMap[String, String]()
    info.foreach(pair => javaMap.put(pair._1, pair._2))
    javaMap
  }
}
