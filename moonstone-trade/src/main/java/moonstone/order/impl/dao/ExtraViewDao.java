package moonstone.order.impl.dao;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.common.model.ExtraView;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public class ExtraViewDao extends MyBatisDao<ExtraView> {
    LoadingCache<Long, ExtraView> cache = Caffeine.newBuilder()
            .build(this::findById);

    public ExtraView cacheId(Long extraId) {
        if (extraId == null) return null;
        return cache.get(extraId);
    }

    public ExtraView createOrFind(ExtraView extraInfo) {
        var hash = extraInfo.generateHash();
        ExtraView exists = getSqlSession().selectOne(sqlId("findByHash"), Map.of("hash", hash));
        if (exists != null) {
            return exists;
        }
        if (extraInfo.hash() == null) {
            extraInfo = new ExtraView(null, extraInfo.extraJson(), extraInfo.tagsJson(), hash);
        }
        getSqlSession().insert(sqlId("create"), extraInfo);
        return getSqlSession().selectOne(sqlId("findByHash"), Map.of("hash", hash));
    }
}
