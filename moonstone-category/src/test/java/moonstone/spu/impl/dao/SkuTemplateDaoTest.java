/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.spu.impl.dao;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import moonstone.BaseDaoTest;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.spu.model.SkuTemplate;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Random;

import static org.hamcrest.Matchers.contains;
import static org.hamcrest.core.Is.is;
import static org.hamcrest.core.IsNot.not;
import static org.hamcrest.core.IsNull.notNullValue;
import static org.junit.Assert.assertThat;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-21
 */
public class SkuTemplateDaoTest extends BaseDaoTest{

    @Autowired
    private SkuTemplateDao skuTemplateDao;

    private SkuTemplate skuTemplate;

    @Before
    public void setUp() throws Exception {
        skuTemplate = make(1);
        skuTemplateDao.create(skuTemplate);
        assertThat(skuTemplate.getId(), notNullValue());
    }

    private SkuTemplate make(Integer i) {
        SkuTemplate skuTemplate = new SkuTemplate();
        skuTemplate.setName("sku-template"+new Random().nextInt());
        skuTemplate.setSkuCode("skuCode"+i);
        skuTemplate.setSpuId(1L);
        skuTemplate.setStatus(1);
        skuTemplate.setStockType(1);
        skuTemplate.setPrice(100);
        skuTemplate.setExtraPrice(ImmutableMap.of("originPrice", 300));
        skuTemplate.setImage("image"+i);
        SkuAttribute skuAttribute = new SkuAttribute();
        skuAttribute.setAttrKey("颜色");
        skuAttribute.setAttrVal("红色");
        skuTemplate.setAttrs(ImmutableList.of(skuAttribute));
        skuTemplate.setStockQuantity(20);
        skuTemplate.setSpecification("sku-spec"+i);
        skuTemplate.setExtraJson("{\"key\":\"value\"}");
        return skuTemplate;
    }

    @Test
    public void testFindBySpuId() throws Exception {
        SkuTemplate shouldIn = make(2);
        skuTemplateDao.create(shouldIn);

        SkuTemplate shouldOut = make(3);
        shouldOut.setSpuId(2L);
        skuTemplateDao.create(shouldOut);

        List<SkuTemplate> actual = skuTemplateDao.findBySpuId(skuTemplate.getSpuId());
        assertThat(actual, contains(skuTemplate, shouldIn));
        assertThat(actual, not(contains(shouldOut)));
    }

    @Test
    public void testFindBySkuCode() throws Exception {
        SkuTemplate shouldOut = make(2);
        skuTemplateDao.create(shouldOut);

        SkuTemplate shouldIn = make(1);
        shouldOut.setSpuId(2L);
        skuTemplateDao.create(shouldIn);

        List<SkuTemplate> actual = skuTemplateDao.findBySkuCode(skuTemplate.getSkuCode());
        assertThat(actual, contains(skuTemplate, shouldIn));
        assertThat(actual, not(contains(shouldOut)));
    }

    @Test
    public void testFindBySpuIdAndSkuCode() throws Exception {
        SkuTemplate actual = skuTemplateDao.findBySpuIdAndSkuCode(skuTemplate.getSpuId(), skuTemplate.getSkuCode());
        assertThat(actual, is(skuTemplate));
    }

    @Test
    public void testUpdateBySpuIdAndSkuCode() throws Exception {
        SkuTemplate u = new SkuTemplate();

        u.setSkuCode(skuTemplate.getSkuCode());
        u.setSpuId(skuTemplate.getSpuId());

        u.setName("foobar");
        u.setImage("barfoo");
        u.setStockQuantity(-1);
        u.setPrice(-1);
        SkuAttribute skuAttribute = new SkuAttribute();
        skuAttribute.setAttrKey("颜色");
        skuAttribute.setAttrVal("绿色");
        u.setAttrs(ImmutableList.of(skuAttribute));

        skuTemplateDao.updateBySpuIdAndSkuCode(u);

        SkuTemplate actual = skuTemplateDao.findById(skuTemplate.getId());

        assertThat(actual.getName(),is(u.getName()));
        assertThat(actual.getImage_(), is(u.getImage_()));
        assertThat(actual.getStockQuantity(),is(u.getStockQuantity()));
        assertThat(actual.getPrice(), is(u.getPrice()));
        assertThat(actual.getAttrs(), is(u.getAttrs()));
    }
}