/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.rule;

import moonstone.cache.BackCategoryCacher;
import moonstone.cache.CategoryAttributeCacher;
import moonstone.cache.SpuCacher;
import moonstone.component.attribute.CategoryAttributeNoCacher;
import moonstone.rule.attribute.*;
import moonstone.rule.sku.SkuRuleByCategoryExecutor;
import moonstone.rule.sku.SkuRuleByGroupedSkuAttributeExecutor;
import moonstone.rule.sku.SkuRuleBySpuExecutor;
import moonstone.rule.sku.SkuTemplateRuleByCategoryExecutor;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-29
 */
public record DefaultPipelineConfigurer(BackCategoryCacher backCategoryCacher,
                                        SpuCacher spuCacher,
                                        CategoryAttributeCacher categoryAttributeCacher,
                                        CategoryAttributeNoCacher categoryAttributeNoCacher) implements PipelineConfigurer {

    /**
     * 配置规则
     *
     * @param ruleExecutorRegistry 规则注册中心
     */
    @Override
    public void configureRuleExecutors(RuleExecutorRegistry ruleExecutorRegistry) {
        ruleExecutorRegistry.register(new AttributeLiteralRule());
        ruleExecutorRegistry.register(new OtherAttributeRuleBySpuExecutor(spuCacher, categoryAttributeCacher));
        ruleExecutorRegistry.register(new ItemOtherAttributeRuleByCategoryExecutor(backCategoryCacher, spuCacher, categoryAttributeCacher));
        ruleExecutorRegistry.register(new SpuOtherAttributeRuleByCategoryExecutor(backCategoryCacher, categoryAttributeNoCacher));

        ruleExecutorRegistry.register(new SkuAttributeRuleBySpuExecutor(spuCacher));
        ruleExecutorRegistry.register(new ItemSkuAttributeRuleByCategoryExecutor(backCategoryCacher, categoryAttributeCacher));
        ruleExecutorRegistry.register(new SpuSkuAttributeRuleByCategoryExecutor(backCategoryCacher, categoryAttributeNoCacher));

        ruleExecutorRegistry.register(new SkuRuleByGroupedSkuAttributeExecutor());

        ruleExecutorRegistry.register(new SkuRuleBySpuExecutor(spuCacher));
        ruleExecutorRegistry.register(new SkuRuleByCategoryExecutor(backCategoryCacher, categoryAttributeCacher));
        ruleExecutorRegistry.register(new SkuTemplateRuleByCategoryExecutor(backCategoryCacher, categoryAttributeNoCacher));

    }
}
