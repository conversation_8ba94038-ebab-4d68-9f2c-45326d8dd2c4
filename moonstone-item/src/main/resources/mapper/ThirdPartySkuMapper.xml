<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ThirdPartySku">

    <resultMap id="ThirdPartySkuMap" type="ThirdPartySku">
        <id property="id" column="id"/>
        <result column="third_party_id" property="thirdPartyId"/>
        <result column="outer_sku_id" property="outerSkuId"/>
        <result column="outer_sku_name" property="outerSkuName"/>
        <result column="status" property="status"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="extra_json" property="extraJson"/>
        <result column="unit" property="unit" />
        <result column="tax" property="tax" />
        <result column="type" property="type"/>
    </resultMap>

    <sql id="tb">
        parana_third_party_skus
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        third_party_id, outer_sku_id, outer_sku_name, status, created_at, updated_at ,extra_json
        ,`type`,unit,tax,source_type
    </sql>

    <sql id="vals">
        #{thirdPartyId}, #{outerSkuId}, #{outerSkuName}, #{status}, now(), now() ,#{extraJson}
        ,#{type},#{unit},#{tax},#{sourceType}
    </sql>

    <sql id="criteria">
        <if test="ids != null">AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="thirdPartyId!=null">AND third_party_id = #{thirdPartyId}</if>
        <if test="outerSkuId != null">AND outer_sku_id = #{outerSkuId}</if>
        <if test="outerSkuName != null">AND outer_sku_name LIKE CONCAT(#{outerSkuName} ,'%')</if>
        <if test="updatedFrom != null">AND <![CDATA[updated_at >= #{updatedFrom}]]> </if>
        <if test="updatedTo != null">AND <![CDATA[updated_at < #{updatedTo}]]> </if>
        <if test="statuses == null and status == null">
            AND `status` != -1
        </if>
        <if test="status != null">AND status = #{status}</if>
        <if test="statuses != null">
            and status in
            <foreach collection="statuses" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'thirdPartyId'">ORDER BY third_party_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'outerSkuId'">ORDER BY outer_sku_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'updatedAt'">ORDER BY updated_at
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <insert id="create" parameterType="ThirdPartySku" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.thirdPartyId}, #{i.outerSkuId}, #{i.outerSkuName}, #{i.status}, now(), now(),#{i.extraJson}
            ,#{i.type}
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="ThirdPartySkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="ThirdPartySkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByThirdPartyId" parameterType="long" resultMap="ThirdPartySkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE third_party_id=#{thirdPartyId} AND status != -1
    </select>

    <select id="findByThirdPartyAndSkuId" parameterType="map" resultMap="ThirdPartySkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE third_party_id=#{ThirdPartyId} AND outer_sku_id=#{outerSkuId}
        LIMIT 1
    </select>
    <select id="findByThirdParty" parameterType="map" resultMap="ThirdPartySkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE third_party_id=#{ThirdPartyId}
        LIMIT 1
    </select>

    <select id="findByThirdPartyIdAndOuterSkuId" parameterType="map" resultMap="ThirdPartySkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE third_party_id = #{thirdPartyId} AND outer_sku_id = #{outerSkuId} AND status != -1 LIMIT 1
    </select>

    <update id="update" parameterType="ThirdPartySku">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="thirdPartyId != null">third_party_id = #{thirdPartyId},</if>
            <if test="outerSkuId != null">outer_sku_id = #{outerSkuId},</if>
            <if test="outerSkuName != null">outer_sku_name = #{outerSkuName},</if>
            <if test="status!=null">status=#{status},</if>
            <if test="extraJson!=null">extra_json = #{extraJson},</if>
            <if test="type!=null">`type` = #{type},</if>
            <if test="unit!=null">`unit` = #{unit},</if>
            <if test="tax!=null">`tax` = #{tax},</if>
            <if test="sourceType!=null">`source_type` = #{sourceType},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id = #{id} and status != #{status}
    </update>

    <update id="updateStatusByThirdPartyAndSkuId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE third_party_id = #{thirdPartyId} AND outer_sku_id = #{outerSkuId} and status != #{status}
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <update id="deletes" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET status=-1
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="ThirdPartySkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countInOuterSkuIds" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            outer_sku_id IN
            <foreach item="skuId" collection="skuIds" open="(" separator="," close=")">
                #{skuId}
            </foreach>
            <if test="outerSkuId != null">AND outer_sku_id like CONCAT(#{outerSkuId} ,'%')</if>
            <if test="outerSkuName != null">AND outer_sku_name like CONCAT(#{outerSkuName} ,'%')</if>
            AND status != -1
        </where>
    </select>

    <select id="pagingInOuterSkuIds" parameterType="map" resultMap="ThirdPartySkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            outer_sku_id IN
            <foreach item="skuId" collection="skuIds" open="(" separator="," close=")">
                #{skuId}
            </foreach>
            <if test="outerSkuId != null">AND outer_sku_id like CONCAT(#{outerSkuId} ,'%')</if>
            <if test="outerSkuName != null">AND outer_sku_name like CONCAT(#{outerSkuName} ,'%')</if>
            AND status != -1
        </where>
        ORDER BY third_party_id
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countInOuterSkuIdsWithText" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        WHERE outer_sku_id IN
        <foreach item="outerSkuId" collection="outerSkuIds" open="(" separator="," close=")">
            #{outerSkuId}
        </foreach>
        <if test="thirdPartyId != null">
            and third_party_id = #{thirdPartyId}
        </if>
        AND
        (outer_sku_name like CONCAT(#{text} ,'%') OR outer_sku_id like CONCAT(#{text} ,'%'))
        AND
        status != -1
    </select>

    <select id="pagingInOuterSkuIdsWithText" parameterType="map" resultMap="ThirdPartySkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE outer_sku_id IN
        <foreach item="outerSkuId" collection="outerSkuIds" open="(" separator="," close=")">
            #{outerSkuId}
        </foreach>
        <if test="thirdPartyId != null">
            and third_party_id = #{thirdPartyId}
        </if>
        AND
        (outer_sku_name like CONCAT(#{text} ,'%') OR outer_sku_id like CONCAT(#{text} ,'%'))
        AND
        status != -1
        ORDER BY third_party_id
        LIMIT #{offset}, #{limit}
    </select>

    <select id="countByText" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        WHERE
        (outer_sku_name like CONCAT(#{text} ,'%') OR outer_sku_id like CONCAT(#{text} ,'%'))
        AND
        status != -1
    </select>

    <select id="pagingByText" parameterType="map" resultMap="ThirdPartySkuMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
        (outer_sku_name like CONCAT(#{text} ,'%') OR outer_sku_id like CONCAT(#{text} ,'%'))
        AND
        status != -1
        ORDER BY third_party_id
        LIMIT #{offset}, #{limit}
    </select>

</mapper>
