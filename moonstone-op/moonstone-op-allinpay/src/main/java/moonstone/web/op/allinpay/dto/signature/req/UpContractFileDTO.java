//package moonstone.web.op.allinpay.dto.signature.req;
//
//import lombok.Data;
//import moonstone.web.op.allinpay.dto.signature.BaseRequest;
//
//import java.io.Serializable;
//
//@Data
//public class UpContractFileDTO extends BaseRequest implements Serializable {
//    /**
//     * 统一客户号
//     */
//    private String merId;
//
//    /**
//     * 商户订单号（由商户生成），不重复值
//     */
//    private String custOrderId;
//
//    /**
//     * 操作者用户帐号，必须要指定一个用户帐号作为操作者
//     */
//    private String account;
//
//    /**
//     * 文件类型，当前仅支持PDF
//     */
//    private String ftype;
//
//    /**
//     * 文件页数
//     */
//    private Long fpages;
//
//    /**
//     * 调用文件系统上传返回的token值
//     */
//    private String token;
//
//    @Override
//    public Class responseClass() {
//        return UpContractFileDTO.class;
//    }
//
//    @Override
//    public String methodName() {
//        return null;
//    }
//}
