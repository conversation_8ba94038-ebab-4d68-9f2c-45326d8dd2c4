package moonstone.web.core.fileNew.dto;

import lombok.Data;

/**
 * author：书生
 */
@Data
public class RefundReqDto {

    /**
     * 退款单id
     */
    private Long refundId;

    /**
     * 退款类型（1：仅退款，3：退货退款）
     */
    private Integer refundType;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单类型（1：店铺订单）
     */
    private Integer orderType;

    /**
     * 买家备注
     */
    private String buyerRemark;

    /**
     * 卖家备注
     */
    private String sellerNote;

    /**
     * 图片json
     */
    private String imagesJson;

    /**
     * 退款原因
     */
    private Integer reasonType;

    /**
     * 卖家寄回地址
     */
    private String sellerAddress;

    /**
     * 是否收到货
     */
    private Boolean buyerReceivedStatus;

    /**
     * 快递单号
     */
    private String expressNo;

    /**
     * 快递公司编码
     */
    private String expressCode;

    /**
     * 快递公司
     */
    private String expressCompany;

}
