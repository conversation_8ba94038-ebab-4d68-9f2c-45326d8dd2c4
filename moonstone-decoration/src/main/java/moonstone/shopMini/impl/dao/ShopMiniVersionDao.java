package moonstone.shopMini.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.shopMini.model.ShopMiniVersion;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * author：书生
 */
@Repository
public class ShopMiniVersionDao extends MyBatisDao<ShopMiniVersion> {
    public ShopMiniVersion selectOne(Map<String, Object> query) {
        return getSqlSession().selectOne(sqlId("selectOne"), query);
    }

    public List<ShopMiniVersion> selectList(Map<String, Object> query) {
        return getSqlSession().selectList(sqlId("selectList"), query);
    }
}
