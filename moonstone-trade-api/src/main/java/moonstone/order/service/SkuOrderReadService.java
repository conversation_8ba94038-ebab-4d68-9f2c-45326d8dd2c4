/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.common.model.Either;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.model.SkuOrder;

import java.util.List;
import java.util.Map;

/**
 * Desc: sku订单读接口
 * Mail: <EMAIL>
 * Data: 16/3/1
 * Author: yangzefeng
 */
public interface SkuOrderReadService {

    Paging<SkuOrder> pagingSkuOrder(OrderCriteria orderCriteria);

    Response<List<SkuOrder>> findByOrderIdWithStatus(Long orderId, Integer status);

    /**
     * 根据订单id查询sku订单
     *
     * @param id 订单id
     * @return sku订单
     */
    Response<SkuOrder> findById(Long id);


    /**
     * 根据id列表查询sku订单
     *
     * @param ids 订单id列表
     * @return 合并支付订单列表
     */
    Response<List<SkuOrder>> findByIds(List<Long> ids);


    /**
     * sku订单分页列表, 供开放平台使用
     *
     * @param pageNo        页码
     * @param size          每页大小
     * @param orderCriteria 查询条件
     * @return 分页订单
     */
    Response<Paging<SkuOrder>> findBy(Integer pageNo, Integer size, OrderCriteria orderCriteria);


    /**
     * 根据店铺订单id查找对应的sku子订单
     *
     * @param shopOrderId 店铺订单id
     * @return 对应的sku子订单
     */
    Response<List<SkuOrder>> findByShopOrderId(Long shopOrderId);

    /**
     * only the itemId and shopId
     *
     * @param shopOrderId orderId
     * @return a json-object actually
     */
    Either<List<SkuOrder>> findItemIdAndShopByShopOrderId(Long shopOrderId);

    /**
     * 根据店铺订单id和skuId查找唯一的sku子订单
     *
     * @param orderId 店铺订单id
     * @return 对应的sku子订单
     */
    Response<SkuOrder> findByOrderIdAndSkuId(Long orderId, Long skuId);

    /**
     * 根据店铺订单id查找对应的sku子订单
     *
     * @param shopOrderIds 店铺订单id列表
     * @return 对应的sku子订单
     */
    Response<List<SkuOrder>> findByShopOrderIds(List<Long> shopOrderIds);

    /**
     * 店铺订单计数
     *
     * @param orderCriteria 条件
     * @return 订单数量
     */
    Response<Long> countShopOrder(OrderCriteria orderCriteria);

    /**
     * 查询子订单列表
     *
     * @param orderCriteria
     * @return
     */
    Response<List<SkuOrder>> listSkuOrdersBy(OrderCriteria orderCriteria);

    Response<SkuOrder> findByOrderIdAndOuterSkuId(Long orderId, String outCode);

    /**
     * 由采购单查询订单
     *
     * @param gatherOrderId 采购单号
     * @return 子订单列表
     */
    Either<List<SkuOrder>> findByGatherOrderId(Long gatherOrderId);

    Either<List<SkuOrder>> findNoItemSnapShotId(int count);

    Long findOrderIdById(Long orderId);

    Long findUserIdByOrderId(Long orderId, Integer orderType);

    /**
     * 查询子订单
     *
     * @param shopOrderIds
     * @return key = shopOrderId , value = List<SkuOrder>
     */
    Map<Long, List<SkuOrder>> getSkuOrderMap(List<Long> shopOrderIds);

    /**
     * @param skuOrderIds
     * @return key = 子订单id
     */
    Map<Long, SkuOrder> findMapByIds(List<Long> skuOrderIds);

    /**
     * 根据查询条件获取sku订单信息
     * @param query 查询条件
     * @return sku订单信息
     */
    SkuOrder getOne(Map<String, Object> query);

    /**
     * 根据查询条件获取sku订单信息列表
     * @param query 查询条件
     * @return sku订单信息列表
     */
    List<SkuOrder> list(Map<String, Object> query);

}
