package moonstone.web.core.events.item.listener;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.NumberUtil;
import moonstone.common.utils.Translate;
import moonstone.event.OrderRefundEvent;
import moonstone.event.PaymentPaidEvent;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.model.*;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.weShop.model.WeShopSku;
import moonstone.weShop.service.WeShopItemReadService;
import moonstone.weShop.service.WeShopItemWriteService;
import moonstone.weShop.service.WeShopSkuReadService;
import moonstone.web.core.events.weShopItem.WeShopItemUpdatedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * 微店铺的商品的销量更新器
 * 注意以下事件均没有线程同步 也没有进行多次判断修正
 */
@Component
@AllArgsConstructor
@Slf4j
public class WeShopItemSellQuantityModifyListener {
    PaymentReadService paymentReadService;
    RefundReadService refundReadService;
    SkuOrderReadService skuOrderReadService;
    WeShopSkuReadService weShopSkuReadService;
    WeShopItemWriteService weShopItemWriteService;
    WeShopItemReadService weShopItemReadService;
    SkuCacheHolder skuCacheHolder;

    /**
     * 增加订单对应微店铺的商品销量
     *
     * @param paymentPaidEvent 支付成功事件
     */
    @EventListener(PaymentPaidEvent.class)
    public void orderPaidByPayment(PaymentPaidEvent paymentPaidEvent) {
        if (paymentPaidEvent.getPaymentId() == null) {
            throw new IllegalStateException(Translate.of("支付单信息丢失"));
        }
        List<SkuOrder> skuOrderList = new ArrayList<>(8);
        for (OrderBase orderBase : paymentReadService.findOrdersByPaymentId(paymentPaidEvent.getPaymentId()).getResult()) {
            if (orderBase instanceof ShopOrder) {
                skuOrderList.addAll(skuOrderReadService.findByShopOrderId(orderBase.getId()).getResult());
            } else if (orderBase instanceof SkuOrder) {
                skuOrderList.add((SkuOrder) orderBase);
            }
        }
        Set<Long> modified = new HashSet<>();
        for (SkuOrder skuOrder : skuOrderList) {
            if (modified.contains(skuOrder.getId())) {
                continue;
            }
            modified.add(skuOrder.getId());
            // extract the sku information
            if (OrderOutFrom.fromCode(skuOrder.getOutFrom()) != OrderOutFrom.WE_SHOP) {
                continue;
            }
            NumberUtil.parseNumber(skuOrder.getOutShopId(), Long.TYPE)
                    .flatMap(weShopId ->
                            (weShopSkuReadService.findByWeShopIdAndSkuId(weShopId, skuOrder.getSkuId(), -99))
                                    .map(WeShopSku::getWeShopItemId)
                                    .ifFail(() -> weShopItemReadService.findByWeShopIdAndItemId(weShopId, skuCacheHolder.findSkuById(skuOrder.getSkuId()).getItemId()).getResult().getId()))
                    .ifSuccess(weShopItemId -> weShopItemWriteService.modifySellQuantityById(weShopItemId, (long) skuOrder.getQuantity()))
                    .ifSuccess(weShopItemId -> EventSender.publish(new WeShopItemUpdatedEvent(weShopItemId)));
        }
    }

    /**
     * 回退订单对应的微店铺的销售额
     *
     * @param orderRefundEvent 退款事件, 只处理退款成功事件
     */
    @EventListener(OrderRefundEvent.class)
    public void orderRefund(OrderRefundEvent orderRefundEvent) {
        if (!Objects.equals(orderRefundEvent.getOrderOperation(), OrderEvent.REFUND_SUCCESS.toOrderOperation())) {
            return;
        }
        List<SkuOrder> skuOrderListForDecreaseSellQuantity = new ArrayList<>(8);
        for (OrderRefund orderRefund : refundReadService.findOrderIdsByRefundId(orderRefundEvent.getRefundId()).getResult()) {
            if (orderRefund.getOrderLevel() == OrderLevel.SHOP) {
                skuOrderListForDecreaseSellQuantity.addAll(skuOrderReadService.findByShopOrderId(orderRefund.getOrderId()).getResult());
            } else if (orderRefund.getOrderLevel() == OrderLevel.SKU) {
                skuOrderListForDecreaseSellQuantity.add(skuOrderReadService.findById(orderRefund.getOrderId()).getResult());
            }
        }
        Set<Long> decreasedSet = new HashSet<>();
        for (SkuOrder skuOrder : skuOrderListForDecreaseSellQuantity) {
            if (decreasedSet.contains(skuOrder.getId())) {
                continue;
            }
            decreasedSet.add(skuOrder.getId());
            // extract the sku information
            if (OrderOutFrom.fromCode(skuOrder.getOutFrom()) != OrderOutFrom.WE_SHOP) {
                continue;
            }
            NumberUtil.parseNumber(skuOrder.getOutShopId(), Long.TYPE)
                    .flatMap(weShopId ->
                            (weShopSkuReadService.findByWeShopIdAndSkuId(weShopId, skuOrder.getSkuId(), -99))
                                    .map(WeShopSku::getWeShopItemId)
                                    .ifFail(() -> weShopItemReadService.findByWeShopIdAndItemId(weShopId, skuCacheHolder.findSkuById(skuOrder.getSkuId()).getItemId()).getResult().getId()))
                    .ifSuccess(weShopItemId -> weShopItemWriteService.modifySellQuantityById(weShopItemId, (long) -skuOrder.getQuantity()))
                    .ifSuccess(weShopItemId -> EventSender.publish(new WeShopItemUpdatedEvent(weShopItemId)));
        }
    }
}
