package moonstone.web.front.order.component;

import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.WeShopSkuCacheHolder;
import moonstone.common.enums.BondedType;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.component.item.component.TaxChecker;
import moonstone.order.dto.RichSku;
import moonstone.weShop.model.WeShopSku;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Slf4j
@Component
public class WeShopOrderPriceAndTaxPreviewProcessor {
    @Autowired
    private WeShopSkuCacheHolder weShopSkuCacheHolder;
    @Autowired
    private TaxChecker taxChecker;

    /**
     * 计算微店订单中的价格
     *
     * @param richSku 单品数据
     */
    public void proccess(RichSku richSku) {
        // no impl for non weShopItem Order
        if (richSku.getWeShopItem() == null) return;
        Optional<WeShopSku> weShopSkuOpt = weShopSkuCacheHolder.findByWeShopIdAndSkuId(richSku.getWeShopItem().getWeShopId(), richSku.getSku().getId());
        if (!weShopSkuOpt.isPresent()) {
            log.error("{} sku[name => {}, id => {}] is already invalidated", LogUtil.getClassMethodName(), richSku.getSku().getName(), richSku.getSku().getId());
            throw new JsonResponseException(Translate.of("商品[%s]已经失效", richSku.getSku().getName()));
        }
        WeShopSku weShopSku = weShopSkuOpt.get();
        Long finalPrice = Optional.ofNullable(weShopSku.getPrice()).orElseGet(() -> richSku.getSku().getPrice().longValue() + weShopSku.getDiffPrice());
        if (BondedType.fromInt(richSku.getItem().getIsBonded()).isBonded()) {
            richSku.getSku().setPrice(finalPrice.intValue());
            if (!weShopSku.getTaxSellerBear()) {
                richSku.setTax(taxChecker.getTax(richSku.getSku(), richSku.getQuantity()));
            } else richSku.setTax(0L);
        }

        richSku.setOriginFee(finalPrice);
    }
}
