
package moonstone.web.op.allinpay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 确认绑卡
 *
 * @since 2021-02-19
 */
@Data
@ApiModel(value = "BindBankCardDTO对象", description = "确认绑卡")
public class BindBankCardDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "商户系统用户标识，商户系统中唯一编号", required = true)
	@NotEmpty(message = "商户系统用户标识不能为空")
	private String bizUserId;

	@ApiModelProperty(value = "银行预留手机号码", required = true)
	@NotNull(message = "银行预留手机号码不能为空")
	private String phone;

	@ApiModelProperty(value = "流水号")
	@NotNull(message = "流水号不能为空")
	private String tranceNum;

	@ApiModelProperty(value = "短信验证码")
	@NotNull(message = "短信验证码不能为空")
	private String verificationCode;

}
