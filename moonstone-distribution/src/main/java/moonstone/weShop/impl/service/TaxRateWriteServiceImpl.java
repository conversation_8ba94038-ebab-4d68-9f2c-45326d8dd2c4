package moonstone.weShop.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.weShop.impl.dao.TaxRateDao;
import moonstone.weShop.model.TaxRate;
import moonstone.weShop.service.TaxRateWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@RpcProvider
@Component
@Slf4j
public class TaxRateWriteServiceImpl implements TaxRateWriteService {
    @Autowired
    TaxRateDao taxRateDao;

    @Override
    public Response<Long> save(TaxRate taxRate) {
        try {
            if (taxRate.getId() != null && taxRate.getId() >= 0) {
                taxRateDao.update(taxRate);
            } else {
                taxRateDao.create(taxRate);
            }
            return Response.ok(taxRate.getId());
        }
        catch (Exception ex)
        {
            log.error("failed to save TaxRate:{}", JSON.toJSONString(taxRate));
            return Response.fail("fail.save.taxRate");
        }
    }

    @Override
    public Response<List<Long>> save(List<TaxRate> taxRate) {
        try {
            return Response.ok(taxRate.stream()
                    .map(this::save)
                    .map(Response::getResult)
                    .collect(Collectors.toList()));
        }
        catch (Exception ex)
        {
            log.error("failed to save TaxRates:{}", JSON.toJSONString(taxRate));
            return Response.fail("fail.save.taxRate");
        }
    }
}
