
package moonstone.web.op.allinpay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 查询余额
 *

 * @since 2021-02-19
 */
@Data
@ApiModel(value = "QueryBalanceVO对象", description = "查询余额")
public class QueryBalanceVO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "总额")
	private Long allAmount;

	@ApiModelProperty(value = "冻结额")
	private Long freezenAmount;


}
