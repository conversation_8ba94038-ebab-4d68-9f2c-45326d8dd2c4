/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.order.api;

import moonstone.order.dto.fsm.Flow;
import moonstone.order.model.OrderBase;
import moonstone.order.model.OrderLevel;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-11
 */
public interface FlowPicker {

    /**
     * 根据(子)订单id和级别来决定选择哪个订单流程
     *
     * @param orderBase  (子)订单, 可能是shopOrder, 也可能是skuOrder, 根据OrderLevel决定
     * @param orderLevel 订单级别
     * @return 对应的流程
     */
    Flow pick(OrderBase orderBase, OrderLevel orderLevel);
}
