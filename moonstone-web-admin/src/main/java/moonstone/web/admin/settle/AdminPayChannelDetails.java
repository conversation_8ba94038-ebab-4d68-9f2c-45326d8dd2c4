package moonstone.web.admin.settle;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.RespUtil;
import moonstone.settle.dto.paging.PayChannelDetailCriteria;
import moonstone.settle.model.PayChannelDetail;
import moonstone.settle.service.PayChannelDetailReadService;
import moonstone.web.core.exports.common.Exporter;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * DATE: 16/11/13 下午5:53 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
@RestController
@ConditionalOnProperty(name = "settle.controller.enable",  havingValue = "true",  matchIfMissing = true)
@RequestMapping("/api/settle")
public class AdminPayChannelDetails {

    @Autowired
    protected Exporter exporter;

    @RpcConsumer
    protected PayChannelDetailReadService payChannelDetailReadService;


    @RequestMapping(value = "/pay-channel-detail-paging", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<Paging<PayChannelDetail>> pagingPayChannelDetails(PayChannelDetailCriteria criteria){
        formatDate(criteria);
        return RespUtil.log(
                payChannelDetailReadService.pagingPayChannelDetails(criteria),
                "pagingPayChannelDetails", criteria
        );
    }

    @RequestMapping(value = "/pay-channel-detail/{payChannelDetailId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public PayChannelDetail findPayChannelDetailById(@PathVariable("payChannelDetailId") Long payChannelDetailId){
        return RespUtil.orJsonEx(
                payChannelDetailReadService.findPayChannelDetailById(payChannelDetailId),
                "findPayChannelDetailById", payChannelDetailId
        );
    }

    @RequestMapping(value = "/pay-channel-detail-export", method = RequestMethod.GET)
    public void exportPayChannelDetails(PayChannelDetailCriteria criteria, HttpServletResponse response, HttpServletRequest request) {
        formatDate(criteria);
        exporter.export(PayChannelDetail.class, criteria,
                payChannelDetailReadService::pagingPayChannelDetails,
                request, response);
    }

    public void formatDate(PayChannelDetailCriteria criteria){
        if(criteria.getCheckFinishedAtEnd()!=null){
            criteria.setCheckFinishedAtEnd(new DateTime(criteria.getCheckFinishedAtEnd()).plusDays(1).toDate());
        }
        if(criteria.getTradeFinishedAtEnd()!=null){
            criteria.setTradeFinishedAtEnd(new DateTime(criteria.getTradeFinishedAtEnd()).plusDays(1).toDate());
        }
    }

}
