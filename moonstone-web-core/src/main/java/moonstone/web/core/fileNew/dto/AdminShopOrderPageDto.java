package moonstone.web.core.fileNew.dto;

import lombok.Getter;
import lombok.Setter;
import moonstone.common.model.dto.PageDto;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class AdminShopOrderPageDto extends PageDto {


	/**
	 * 订单号列表
	 */
	private List<Long> ids;

	/**
	 * 店铺id列表
	 */
	private List<String> shopIds;


	/**
	 * 申报单号列表
	 */
	private List<String> declareIds;


	/**
	 * 支付单号列表
	 */
	private List<String> paymentOutIds;

	/**
	 * 门店名称
	 */
	private String subStoreName;

	/**
	 * 服务商名称
	 */
	private String serviceProviderName;

	/**
	 * 导购员
	 */
	private String guiderName;

	/**
	 * 订单状态列表
	 */
	private List<String> statusList;

	/**
	 * table名称
	 */
	private String tabName;

	/**
	 * 下单开始时间
	 */
	private Date orderTimeStart;
	/**
	 * 下单结束时间
	 */
	private Date orderTimeEnd;

	/**
	 * 发货开始时间
	 */
	private Date shipTimeStart;

	/**
	 * 发货结束时间
	 */
	private Date shipTimeEnd;

	/**
	 * 确认收货开始时间
	 */
	private Date confirmTimeStart;

	/**
	 * 确认收货结束时间
	 */
	private Date confirmTimeEnd;

	/**
	 * 异常类型
	 * @see moonstone.order.dto.fsm.OrderExceptionEnum
	 */
	private Integer exceptionType;

	/**
	 * 异常类型列表
	 */
	private List<Integer> exceptionTypes;

	/**
	 * 第三方交易单号来源（1：京东云交易）
	 */
	private Integer thirdPartyTransactionNoSource;

	/**
	 * 第三方交易单号
	 */
	private List<String> thirdPartyTransactionNoList;
	private String thirdPartyTransactionNo;

	/**
	 * 发货仓类型(1:代塔仓自有,2：京东云交易)
	 */
	private Integer skuOrderShippingWarehouseType;

	/**
	 * 支付单推送状态
	 */
	private Integer paymentPushStatus;

	/**
	 * 订单推送状态
	 */
	private Integer skuOrderPushStatus;

	/**
	 * 支付流水号
	 */
	private String paySerialNo;

}
