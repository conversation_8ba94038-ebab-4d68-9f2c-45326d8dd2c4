CREATE TABLE parana.p_area_info_of_entity (
	id BIGINT auto_increment NULL,
	main_id BIGINT NOT NULL,
	`type` BIGINT NOT NULL,
	status INT DEFAULT 1 NULL,
	province_id BIGINT NOT NULL,
	city_id BIGINT NOT NULL,
	county_id BIGINT NULL,
	address varchar(512) NULL,
	created_at TIMESTAMP NULL,
	updated_at TIMESTAMP NULL,
	CONSTRAINT p_area_info_of_entity_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8
COLLATE=utf8_general_ci;
CREATE INDEX p_area_info_of_entity_type_IDX USING BTREE ON parana.p_area_info_of_entity (`type`,main_id);
CREATE INDEX p_area_info_of_entity_province_id_IDX USING BTREE ON parana.p_area_info_of_entity (province_id,city_id,county_id);
