package moonstone.order.impl.service;

import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.dao.OrderRefundDao;
import moonstone.order.model.OrderRefund;
import moonstone.order.service.OrderRefundService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderRefundServiceImpl implements OrderRefundService {

    @Resource
    private OrderRefundDao orderRefundDao;

    @Override
    public void save(OrderRefund orderRefund) {
        orderRefundDao.create(orderRefund);
    }

    @Override
    public OrderRefund getOne(Map<String, Object> query) {
        return orderRefundDao.selectOne(query);
    }

    @Override
    public List<OrderRefund> list(Map<String, Object> query) {
        return orderRefundDao.selectList(query);
    }
}
