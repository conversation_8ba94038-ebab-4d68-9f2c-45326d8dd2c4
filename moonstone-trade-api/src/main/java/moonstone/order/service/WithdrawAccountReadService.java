package moonstone.order.service;

import io.terminus.common.model.Response;
import moonstone.common.model.Either;
import moonstone.order.model.WithdrawAccount;

import java.util.List;
import java.util.Optional;

public interface WithdrawAccountReadService {
    Either<List<WithdrawAccount>> findByShopIdAndUserId(Long shopId, Long userId);

    Either<Optional<WithdrawAccount>> findById(Long id);

    Response<List<WithdrawAccount>> findByIds(List<Long> idList);

    Response<List<WithdrawAccount>> findAccount(Long shopId, Long userId, String name, String account);
}
