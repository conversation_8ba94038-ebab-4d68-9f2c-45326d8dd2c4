package moonstone.shop.impl.service;

import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.item.dto.ShopExtra;
import moonstone.shop.model.Shop;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class ShopExtraManager {
    @Autowired
    private MongoTemplate mongoTemplate;

    public Shop packShopExtra(Shop shop) {
        findByShopId(shop.getId()).map(ShopExtra::getExtra).ifSuccess(shop::setExtra);
        return shop;
    }

    public Either<ShopExtra> findByShopId(Long shopId) {
        try {
            if (shopId == null) {
                log.debug("{} shopId:{}", LogUtil.getClassMethodName("not-found"), shopId);
                return null;
            }
            return Either.ok(mongoTemplate.findOne(Query.query(Criteria.where("shopId").is(shopId)), ShopExtra.class));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} shopId:{}", LogUtil.getClassMethodName(), shopId, ex);
            return Either.error(ex);
        }
    }

    public Either<Boolean> updateShopExtra(Shop shop) {
        return updateShopExtra(new ShopExtra(shop));
    }

    public Either<ShopExtra> createShopExtra(ShopExtra shopExtra) {
        if (shopExtra == null) {
            return Either.error("require.not.null");
        }
        try {
            if (mongoTemplate.count(Query.query(Criteria.where("shopId").is(shopExtra.getShopId())), ShopExtra.class) > 0) {
                return Either.error("shopExtra.duplicate.shopId");
            }
            mongoTemplate.save(shopExtra);
            return Either.ok(shopExtra);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} shopExtra:{}", LogUtil.getClassMethodName(), shopExtra);
            return Either.error(ex);
        }
    }

    public Either<Boolean> updateShopExtra(ShopExtra shopExtra) {
        try {
            if (shopExtra.getExtra() == null) {
                return Either.ok(true);
            }

            if (mongoTemplate.count(Query.query(Criteria.where("shopId").is(shopExtra.getShopId())), ShopExtra.class) == 0) {
                return createShopExtra(shopExtra).map(Objects::nonNull);
            }
            UpdateResult result = mongoTemplate.updateFirst(Query.query(Criteria.where("shopId").is(shopExtra.getShopId())), Update.update("extra", shopExtra.getExtra()), ShopExtra.class);
            if (result.getModifiedCount() > 0) {
                return Either.ok(true);
            }
            return Either.ok(false);
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} shopExtra:{}", LogUtil.getClassMethodName(), shopExtra);
            return Either.error(ex);
        }
    }
}
