<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="ItemSnapshot">
    <resultMap id="ItemSnapshotMap" type="ItemSnapshot">
        <id column="id" property="id"/>
        <result column="item_id" property="itemId"/>
        <result column="item_code" property="itemCode"/>
        <result column="item_info_md5" property="itemInfoMd5"/>
        <result column="shop_id" property="shopId"/>
        <result column="shop_name" property="shopName"/>
        <result column="name" property="name"/>
        <result column="main_image" property="mainImage"/>
        <result column="images_json" property="imagesJson"/>
        <result column="advertise" property="advertise"/>
        <result column="specification" property="specification"/>
        <result column="extra_json" property="extraJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="sku_attributes" property="skuAttrsJson"/>
        <result column="other_attributes" property="otherAttrsJson"/>
        <result column="detail" property="detail"/>
        <result column="bar_code" property="barCode"/>
        <result column="created_at" property="createdAt"/>
    </resultMap>

    <sql id="tb">
        parana_item_snapshots
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        item_id, item_code, item_info_md5, shop_id, shop_name, name,
        main_image, images_json, advertise, specification, extra_json,tags_json,
        sku_attributes, other_attributes, detail, bar_code, created_at
    </sql>

    <sql id="vals">
        #{itemId},#{itemCode}, #{itemInfoMd5}, #{shopId}, #{shopName}, #{name},
        #{mainImage}, #{imagesJson}, #{advertise}, #{specification}, #{extraJson},
        #{tagsJson},#{skuAttrsJson}, #{otherAttrsJson},#{detail}, #{barCode}, now()
    </sql>


    <insert id="create" parameterType="ItemSnapshot" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findByItemIdAndItemInfoMd5" parameterType="map" resultMap="ItemSnapshotMap">
        SELECT * FROM
        <include refid="tb"/>
        WHERE item_id=#{itemId} and item_info_md5 = #{itemInfoMd5} limit 1
    </select>

    <select id="findById" parameterType="long" resultMap="ItemSnapshotMap">
        SELECT *
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>


    <select id="findExportViewById" parameterType="long" resultMap="ItemSnapshotMap">
        SELECT
        name, bar_code, item_code
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

</mapper>