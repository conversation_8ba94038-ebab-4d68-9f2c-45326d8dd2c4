package moonstone.web.front.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.core.files.service.OSSClientService;
import moonstone.web.front.sales.vo.*;
import moonstone.web.front.util.excelData.ProvinceRankData;
import moonstone.web.front.util.excelData.ServiceProviderRankData;
import moonstone.web.front.util.excelData.SkuRankData;
import moonstone.web.front.util.excelData.SubStoreRankData;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 使用easyExcel来进行Excel的相关操作
 * Date: 2022/11/22
 * Author: yilang
 */
@Component
@Slf4j
public class ExcelUtils {


    @Resource
    private OSSClientService ossClientService;

    @Resource
    private EnvironmentConfig environmentConfig;

    public List<String> createAndUploadSalesExcel(String shopName, DataBoardShowVO dataBoardShowVO) throws IOException {

        List<String> urls = new ArrayList<>();
        File gmvFile = File.createTempFile("平台:" + shopName, "-" + getNowTime() + "GMV排行榜");
        File orderFile = File.createTempFile("平台:" + shopName, "-" + getNowTime() + "单量排行榜");
        fillGmvExcel(gmvFile, dataBoardShowVO);
        fillOrderExcel(orderFile, dataBoardShowVO);

        try {
            String urlOfGmv = ossClientService.upload(environmentConfig.getEnv(), shopName + getNowTime() + "GMV排行榜.xlsx", gmvFile);
            String urlOfOrderQuantity = ossClientService.upload(environmentConfig.getEnv(), shopName + getNowTime() + "单量排行榜.xlsx", orderFile);

            urls.add(urlOfGmv);
            urls.add(urlOfOrderQuantity);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return urls;
    }

    public String getNowTime() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy年MM月dd日");
        String string = simpleDateFormat.format(new Date());
        return string;
    }

    public File fillGmvExcel(File gmvFile, DataBoardShowVO dataBoardShowVO) {

        try (ExcelWriter excelWriter = EasyExcel.write(gmvFile).build()) {
            // 依次把各个sheet写入
            WriteSheet writeSheetForServiceProvider = EasyExcel.writerSheet(0, "服务商GMV排名").head(ServiceProviderRankData.class).build();
            excelWriter.write(repackServiceProviderData(dataBoardShowVO.getServiceProviderGmvTop()), writeSheetForServiceProvider);

            WriteSheet writeSheetForSubStore = EasyExcel.writerSheet(1, "门店GMV排名").head(SubStoreRankData.class).build();
            excelWriter.write(repackSubStoreRankData(dataBoardShowVO.getSubStoreGmvTop()), writeSheetForSubStore);

            WriteSheet writeSheetForItems = EasyExcel.writerSheet(2, "商品GMV排名").head(SkuRankData.class).build();
            excelWriter.write(repackSkuRankData(dataBoardShowVO.getSkuGmvTop()), writeSheetForItems);

            WriteSheet writeSheetForProvince = EasyExcel.writerSheet(3, "省份GMV排名").head(ProvinceRankData.class).build();
            excelWriter.write(repackProvinceData(dataBoardShowVO.getProvinceGmvTop()), writeSheetForProvince);
        }

        return gmvFile;
    }

    public File fillOrderExcel(File orderFile, DataBoardShowVO dataBoardShowVO) {

        try (ExcelWriter excelWriter = EasyExcel.write(orderFile).build()) {
            // 依次把各个sheet写入
            WriteSheet writeSheetForServiceProvider = EasyExcel.writerSheet(0, "服务商单量排名").head(ServiceProviderRankData.class).build();
            excelWriter.write(repackServiceProviderData(dataBoardShowVO.getServiceProviderOrderTop()), writeSheetForServiceProvider);

            WriteSheet writeSheetForSubStore = EasyExcel.writerSheet(1, "门店单量排名").head(SubStoreRankData.class).build();
            excelWriter.write(repackSubStoreRankData(dataBoardShowVO.getSubStoreOrderTop()), writeSheetForSubStore);

            WriteSheet writeSheetForItems = EasyExcel.writerSheet(2, "商品单量排名").head(SkuRankData.class).build();
            excelWriter.write(repackSkuRankData(dataBoardShowVO.getSkuOrderTop()), writeSheetForItems);

            WriteSheet writeSheetForProvince = EasyExcel.writerSheet(3, "省份单量排名").head(ProvinceRankData.class).build();
            excelWriter.write(repackProvinceData(dataBoardShowVO.getProvinceOrderTop()), writeSheetForProvince);
        }

        return orderFile;
    }

    /**
     * 由于VO中的GMV是Long型，且后两位代表小数， 所以导出的数据需要进行Long -> Double 的转化
     *
     * @param VOList
     * @return
     */
    private List<ServiceProviderRankData> repackServiceProviderData(List<ServiceProviderRankVO> VOList) {
        List<ServiceProviderRankData> dataList = new ArrayList<>();
        for (ServiceProviderRankVO vo : VOList) {
            ServiceProviderRankData data = new ServiceProviderRankData();
            data.setRank(vo.getRank());
            data.setServiceProviderName(vo.getServiceProviderName());
            data.setProvince(vo.getProvince());
            data.setGmv(vo.getGmv() * 1.0 / 100);
            data.setOrderQuantity(vo.getOrderQuantity());

            dataList.add(data);
        }
        return dataList;
    }

    private List<SubStoreRankData> repackSubStoreRankData(List<SubStoreRankVO> VOList) {
        List<SubStoreRankData> dataList = new ArrayList<>();
        for (SubStoreRankVO vo : VOList) {
            SubStoreRankData data = new SubStoreRankData();
            data.setRank(vo.getRank());
            data.setSubStoreName(vo.getSubStoreName());
            data.setServiceProviderName(vo.getServiceProviderName());
            data.setProvince(vo.getProvince());
            data.setGmv(vo.getGmv() * 1.0 / 100);
            data.setOrderQuantity(vo.getOrderQuantity());

            dataList.add(data);
        }
        return dataList;
    }

    private List<SkuRankData> repackSkuRankData(List<SkuRankVO> VOList) {
        List<SkuRankData> dataList = new ArrayList<>();
        for (SkuRankVO vo : VOList) {
            SkuRankData data = new SkuRankData();
            data.setRank(vo.getRank());
            data.setSkuName(vo.getSkuName());
            data.setSkuId(vo.getSkuId());
            data.setGmv(vo.getGmv() * 1.0 / 100);
            data.setOrderQuantity(vo.getOrderQuantity());

            dataList.add(data);
        }
        return dataList;
    }

    private List<ProvinceRankData> repackProvinceData(List<ProvinceRankVO> VOList) {
        List<ProvinceRankData> dataList = new ArrayList<>();
        for (ProvinceRankVO vo : VOList) {
            ProvinceRankData data = new ProvinceRankData();
            data.setRank(vo.getRank());
            data.setProvince(vo.getProvince());
            data.setGmv(vo.getGmv() * 1.0 / 100);
            data.setOrderQuantity(vo.getOrderQuantity());

            dataList.add(data);
        }
        return dataList;
    }

}
