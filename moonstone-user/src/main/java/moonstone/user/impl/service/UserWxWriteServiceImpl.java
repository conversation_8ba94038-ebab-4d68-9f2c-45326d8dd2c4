package moonstone.user.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.utils.LogUtil;
import moonstone.user.impl.dao.UserWxDao;
import moonstone.user.model.UserWx;
import moonstone.user.service.UserWxWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by CaiZhy on 2018/11/16.
 */
@Slf4j
@Service
@RpcProvider
public class UserWxWriteServiceImpl implements UserWxWriteService {
    @Autowired
    private UserWxDao userWxDao;

    @Override
    public Response<Boolean> update(UserWx userWx) {
        try {
            return Response.ok(userWxDao.update(userWx));
        } catch (Exception e) {
            log.error("fail to update userWx({}), cause: {}", userWx, Throwables.getStackTraceAsString(e));
            return Response.fail("user.wx.update.fail");
        }
    }

    @Override
    public Response<Boolean> removeByUserIdAndAppId(Long userId, String appId) {
        try {
            return Response.ok(userWxDao.removeByUserIdAndAppId(userId, appId));
        } catch (Exception ex) {
            ex.printStackTrace();
            log.error("{} userId:{} appId:{}", LogUtil.getClassMethodName(), userId, appId);
            return Response.fail("user.wx.delete.fail");
        }
    }

    @Override
    public Response<Boolean> removeByUserIdAndAppIdAndAppType(Long userId, String appId, AppTypeEnum appType) {
        try {
            return Response.ok(userWxDao.removeByUserIdAndAppIdAndAppType(userId, appId, appType.getCode()));
        } catch (Exception ex) {
            log.error("UserWxWriteServiceImpl.removeByUserIdAndAppIdAndAppType error, userId={}, appId={}, appType={}",
                    userId, appId, appType, ex);
            return Response.fail("用户小程序信息删除失败");
        }
    }
}
