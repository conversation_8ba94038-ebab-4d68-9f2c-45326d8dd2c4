package moonstone.thirdParty.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.thirdParty.model.ThirdPartySkuDetailOss;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class ThirdPartySkuDetailOssDao extends MyBatisDao<ThirdPartySkuDetailOss> {

    public ThirdPartySkuDetailOss findByPath(String outerSkuId, String path) {
        return getSqlSession().selectOne(sqlId("findByPath"), ImmutableMap.of(
                "path", path,
                "outerSkuId", outerSkuId));
    }

    public List<ThirdPartySkuDetailOss> findByPaths(String outerSkuId, List<String> paths) {
        return getSqlSession().selectList(sqlId("findByPaths"), ImmutableMap.of(
                "outerSkuId", outerSkuId,
                "paths", paths));
    }
}
