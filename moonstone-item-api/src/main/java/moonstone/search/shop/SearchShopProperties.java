package moonstone.search.shop;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Author:cp
 * Created on 7/25/16.
 */
@ConfigurationProperties(prefix = "shop.search")
@Getter
@Setter
public class SearchShopProperties {

    /**
     * 店铺的索引名称, 可能是alias
     */
    private String indexName = "shops";

    /**
     * 店铺的索引类型
     */
    private String indexType = "shop";

    /**
     * 对应店铺类型的索引文件路径, 用以初始化索引的mapping, 默认为 ${indexType}_mapping.json
     */
    private String mappingPath;

    /**
     * 全量dump索引时, 最多涉及多少天之前有更新的店铺
     */
    private Integer fullDumpRange = 3;

    /**
     * 每次批量处理多少个店铺
     */
    private Integer batchSize = 100;

}
