package moonstone.promotion.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.cache.PromotionCacher;
import moonstone.common.exception.InvalidException;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.RichSku;
import moonstone.order.dto.RichSkusByShop;
import moonstone.promotion.api.CouponCheck;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.Set;

/**
 * 优惠券使用规则校验
 * <p>
 * 检查同一张商品券只能在一个店铺里同时使用一张
 * </p>
 * Author:cp
 * Created on 7/20/16.
 */
@Component
@Slf4j
public class CouponUsageChecker implements CouponCheck {

    private final PromotionCacher promotionCacher;

    @Autowired
    public CouponUsageChecker(PromotionCacher promotionCacher) {
        this.promotionCacher = promotionCacher;
    }

    public void check(RichOrder richOrder) {
        for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
            Set<Long> usedItemCouponIds = new HashSet<>();
            for (RichSku richSku : richSkusByShop.getRichSkus()) {
                final Long promotionId = richSku.getPromotionId();
                if (promotionId == null) {
                    continue;
                }

                Promotion promotion = promotionCacher.findByPromotionId(promotionId);
                if (PromotionType.from(promotion.getType()) != PromotionType.ITEM_COUPON) {
                    continue;
                }

                if (usedItemCouponIds.contains(promotionId)) {
                    log.error("the item coupon(promotionId={}) has used too many in a shop",
                            promotionId);
                    throw new InvalidException("one.item.coupon.should.use.only.once.in.shop");
                } else {
                    usedItemCouponIds.add(promotionId);
                }
            }
        }
    }

}
