package moonstone.search.weShopItem.impl;

import io.terminus.search.api.IndexTaskBuilder;
import io.terminus.search.api.model.IndexAction;
import io.terminus.search.api.model.IndexTask;
import lombok.extern.slf4j.Slf4j;
import moonstone.search.dto.IndexedWeShopItem;
import moonstone.search.weShopItem.SearchWeShopItemProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by CaiZhy on 2018/12/24.
 */
@Slf4j
@Component
public class IndexedWeShopItemIndexAction {

    private final IndexTaskBuilder indexTaskBuilder;

    private final SearchWeShopItemProperties searchWeShopItemProperties;

    @Autowired
    public IndexedWeShopItemIndexAction(
            IndexTaskBuilder indexTaskBuilder,
            SearchWeShopItemProperties searchWeShopItemProperties) {
        this.indexTaskBuilder = indexTaskBuilder;
        this.searchWeShopItemProperties = searchWeShopItemProperties;
    }

    public IndexTask indexTask(IndexedWeShopItem indexedWeShopItem){
        return indexTaskBuilder.indexName(searchWeShopItemProperties.getIndexName())
                .indexType(searchWeShopItemProperties.getIndexType())
                .indexAction(IndexAction.INDEX).build(indexedWeShopItem.getId(), indexedWeShopItem);
    }

    public IndexTask deleteTask(Long weShopItemId){
        return indexTaskBuilder.indexName(searchWeShopItemProperties.getIndexName())
                .indexType(searchWeShopItemProperties.getIndexType())
                .indexAction(IndexAction.DELETE).build(weShopItemId, null);
    }
}
