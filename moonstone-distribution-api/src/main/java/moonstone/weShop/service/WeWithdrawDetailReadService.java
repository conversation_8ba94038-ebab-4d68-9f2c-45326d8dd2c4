package moonstone.weShop.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.weShop.model.WeWithdrawDetail;

import java.util.List;

/**
 * Created by CaiZhy on 2018/12/11.
 */
public interface WeWithdrawDetailReadService {
    Response<WeWithdrawDetail> findById(Long id);

    Response<List<WeWithdrawDetail>> findByIds(List<Long> ids);

    Response<List<WeWithdrawDetail>> findByWeShopId(Long weShopId);

    Response<List<WeWithdrawDetail>> findByUserId(Long userId);

    Response<Paging<WeWithdrawDetail>> paging(List<Long> ids,
                                              List<Long> userIds,
                                              List<Long> weShopIds,
                                              Long shopId,
                                              Integer type,
                                              String applicantName,
                                              Integer status,
                                              String statuses,
                                              String tradeNo,
                                              String openId,
                                              String clientIp,
                                              String paymentNo,
                                              Integer pageNo,
                                              Integer pageSize);
}
