package moonstone.web.front.user;

import cn.hutool.core.util.StrUtil;
import com.danding.common.search.PageParam;
import com.danding.common.search.SearchResult;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.common.enums.OpBusinessType;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.model.SysOperLog;
import moonstone.common.utils.R;
import moonstone.common.utils.UserUtil;
import moonstone.order.service.SysOperLogReadService;
import moonstone.user.criteria.SysOperCriteria;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.UserProfileReadService;
import moonstone.user.service.UserReadService;
import org.springframework.beans.BeanUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 用户日志相关接口
 */
@Slf4j
@RestController
@RequestMapping("/api/user/log")
public class UserOperationLog {


    @RpcConsumer
    SysOperLogReadService sysOperLogReadService;

    @RpcConsumer
    private UserProfileReadService userProfileReadService;

    @RpcConsumer
    private StoreProxyReadService storeProxyReadService;

    @RpcConsumer
    private UserReadService<User> userReadService;

    /**
     * 获取店铺用户日志信息
     *
     * @param pageNo 页码，默认为1
     * @param pageSize 每页记录数，默认为10
     * @param shopId 店铺信息id
     * @param preCriteria 预设条件，以Map形式传递
     * @return 包含用户信息的响应结果
     */
    @GetMapping("/get-shop-user-info")
    public R getVipInfoGet(@RequestParam(defaultValue = "1") int pageNo,
                           @RequestParam(defaultValue = "10") int pageSize,
                           @RequestParam long shopId,
                           @RequestParam Map<String, String> preCriteria) {
        try {
            CommonUser user = UserUtil.getCurrentUser();
            if (user == null) {
                return R.error(-1, "请登录");
            }
            if (ObjectUtils.isEmpty(shopId)) {
                return R.error(-1, "店铺信息id缺失");
            }
            PageParam pageParam = new PageParam();
            pageParam.setPage(pageNo);
            pageParam.setSize(pageSize);
            SysOperCriteria criteria = new SysOperCriteria();
            criteria.setShopId(shopId);

            if (preCriteria != null && preCriteria.containsKey("startTime")) {
                if (preCriteria.get("startTime").length() > 0) {
                    preCriteria.put("startTime", preCriteria.get("startTime") + " 00:00:00");
                    criteria.setStartTime(dateToStamp(preCriteria.get("startTime")));
                }
            }
            if (preCriteria != null && preCriteria.containsKey("endTime")) {
                if (preCriteria.get("endTime").length() > 0) {
                    preCriteria.put("endTime", preCriteria.get("endTime") + " 23:59:59");
                    criteria.setEndTime(dateToStamp(preCriteria.get("endTime")));
                }
            }

            if (preCriteria != null && preCriteria.containsKey("name")) {
                Response<List<Long>> userResponse = userReadService.findIdsByName(preCriteria.get("name"));
                //userProfileReadService.findProfileByNickName(preCriteria.get("name"));
                // userReadService.findIdsByName(preCriteria.get("name"));
                List<Long> list = new ArrayList<>();
                if (userResponse.isSuccess() && userResponse.getResult() != null && !userResponse.getResult().isEmpty()) {
                    for (Long s : userResponse.getResult()) {
                        list.add(s);
                    }
                }

                Either<List<StoreProxy>> listResult = storeProxyReadService.findByProxyShopName(preCriteria.get("name"));
                if (listResult.isSuccess() && listResult.take() != null && !listResult.take().isEmpty()) {
                    for (StoreProxy s : listResult.take()) {
                        list.add(s.getUserId());
                    }
                }
                Response<User> mobileUser = userReadService.findByMobile(preCriteria.get("name"));
                if (mobileUser.isSuccess() && mobileUser.getResult() != null) {
                    list.add(mobileUser.getResult().getId());
                }
                criteria.setUserIds(list);
            }

            if (preCriteria != null && preCriteria.containsKey("status") && preCriteria.get("status") != null) {
                criteria.setStatus(Long.valueOf(preCriteria.get("status")));
            }

            if (preCriteria != null && preCriteria.containsKey("businessType") && preCriteria.get("businessType") != null) {
                criteria.setBusinessType(preCriteria.get("businessType"));
            }

            if (preCriteria != null && preCriteria.containsKey("flag") && preCriteria.get("flag") != null) {
                criteria.setFlag(preCriteria.get("flag"));
            }
            if (preCriteria != null && preCriteria.containsKey("name") && criteria.getUserIds().isEmpty()) {
                criteria.setUserIds(Arrays.asList(-1L));
            }

            SearchResult<SysOperLog> listResult = sysOperLogReadService.findSysOperLogAll(pageParam, criteria);
            if (listResult.getResult().isEmpty()) {
                return R.ok().add("data", null);
            }
            SystemLogBody systemLogBody = new SystemLogBody();
            systemLogBody.setPage(listResult.getPagination().getPage());
            systemLogBody.setSize(listResult.getPagination().getSize());
            systemLogBody.setTotal(listResult.getPagination().getTotal());
            systemLogBody.setTotalPage(listResult.getPagination().getTotalPage());

            List<SystemLogs> logs = new ArrayList<>();
            for (SysOperLog sysOperLog : listResult.getResult()) {
                SystemLogs systemLogs = new SystemLogs();
                BeanUtils.copyProperties(sysOperLog, systemLogs);
                Response<User> rProfile = userReadService.findById(sysOperLog.getUserId());//findProfileByUserId(sysOperLog.getUserId());
                if (rProfile.isSuccess() && rProfile.getResult() != null) {
                    if (rProfile.getResult().getName() != null) {
                        systemLogs.setUserName(rProfile.getResult().getName());
                    } else {
                        systemLogs.setUserName(rProfile.getResult().getMobile());
                    }
                }

                if (!ObjectUtils.isEmpty(systemLogs.getShopId())) {
                    Either<Optional<StoreProxy>> optionalResult = storeProxyReadService.findByShopIdAndUserId(sysOperLog.getShopId(), sysOperLog.getUserId());
                    if (optionalResult.isSuccess() && optionalResult.take().isPresent()) {
                        systemLogs.setUserName(optionalResult.take().get().getProxyShopName());
                    }
                }

                if (ObjectUtils.isEmpty(systemLogs.getUserName())) {
                    Response<UserProfile> rProfiles = userProfileReadService.findProfileByUserId(sysOperLog.getUserId());
                    if (rProfiles.isSuccess() && rProfiles.getResult() != null && rProfiles.getResult().getRealName() != null) {
                        systemLogs.setUserName(rProfiles.getResult().getRealName());
                    }
                }

                systemLogs.setOperator(OpBusinessType.getEnumByCode(sysOperLog.getBusinessType()).getDesc());
                logs.add(systemLogs);
            }
            systemLogBody.setSystemLogs(logs);

            return R.ok().add("data", systemLogBody);
        } catch (
                Exception e) {
            log.error("failed find to  cause:{}", Throwables.getStackTraceAsString(e));
            return R.error(-1, "系统异常");
        }
    }

    /**
     * 获取店铺用户日志信息
     *
     * @return 包含用户信息的响应结果
     */
    @PostMapping("/get-shop-user-info")
    public Result<SystemLogBody> getVipInfo(@RequestBody @Valid SysOperCriteria criteria) {
        try {
            CommonUser user = UserUtil.getCurrentUser();
            if (user == null) {
                throw new ApiException("未登录");
            }
            if (ObjectUtils.isEmpty(criteria.getShopId())) {
                Long currentShopId = UserUtil.getCurrentShopId();
                criteria.setShopId(currentShopId);
            }
            PageParam pageParam = new PageParam();
            pageParam.setPage((int) criteria.getCurrent());
            pageParam.setSize((int) criteria.getSize());
            if (StrUtil.isNotBlank(criteria.getName())) {
                Response<List<Long>> userResponse = userReadService.findIdsByName(criteria.getName());
                //userProfileReadService.findProfileByNickName(preCriteria.get("name"));
                // userReadService.findIdsByName(preCriteria.get("name"));
                List<Long> list = new ArrayList<>();
                if (userResponse.isSuccess() && userResponse.getResult() != null && !userResponse.getResult().isEmpty()) {
                    for (Long s : userResponse.getResult()) {
                        list.add(s);
                    }
                }
                Either<List<StoreProxy>> listResult = storeProxyReadService.findByProxyShopName(criteria.getName());
                if (listResult.isSuccess() && listResult.take() != null && !listResult.take().isEmpty()) {
                    for (StoreProxy s : listResult.take()) {
                        list.add(s.getUserId());
                    }
                }
                Response<User> mobileUser = userReadService.findByMobile(criteria.getName());
                if (mobileUser.isSuccess() && mobileUser.getResult() != null) {
                    list.add(mobileUser.getResult().getId());
                }
                criteria.setUserIds(list);
            }

            if (StrUtil.isNotBlank(criteria.getName())&& criteria.getUserIds().isEmpty()) {
                criteria.setUserIds(List.of(-1L));
            }
            SearchResult<SysOperLog> listResult = sysOperLogReadService.findSysOperLogAll(pageParam, criteria);
            SystemLogBody systemLogBody = new SystemLogBody();
            systemLogBody.setPage(listResult.getPagination().getPage());
            systemLogBody.setSize(listResult.getPagination().getSize());
            systemLogBody.setTotal(listResult.getPagination().getTotal());
            systemLogBody.setTotalPage(listResult.getPagination().getTotalPage());
            List<SystemLogs> logs = new ArrayList<>();
            for (SysOperLog sysOperLog : listResult.getResult()) {
                SystemLogs systemLogs = new SystemLogs();
                BeanUtils.copyProperties(sysOperLog, systemLogs);
                systemLogs.setStatusDesc(systemLogs.getStatus() == 1 ? "成功" : "失败");
                Response<User> rProfile = userReadService.findById(sysOperLog.getUserId());//findProfileByUserId(sysOperLog.getUserId());
                if (rProfile.isSuccess() && rProfile.getResult() != null) {
                    if (rProfile.getResult().getName() != null) {
                        systemLogs.setUserName(rProfile.getResult().getName());
                    } else {
                        systemLogs.setUserName(rProfile.getResult().getMobile());
                    }
                }

                if (!ObjectUtils.isEmpty(systemLogs.getShopId())) {
                    Either<Optional<StoreProxy>> optionalResult = storeProxyReadService.findByShopIdAndUserId(sysOperLog.getShopId(), sysOperLog.getUserId());
                    if (optionalResult.isSuccess() && optionalResult.take().isPresent()) {
                        systemLogs.setUserName(optionalResult.take().get().getProxyShopName());
                    }
                }

                if (ObjectUtils.isEmpty(systemLogs.getUserName())) {
                    Response<UserProfile> rProfiles = userProfileReadService.findProfileByUserId(sysOperLog.getUserId());
                    if (rProfiles.isSuccess() && rProfiles.getResult() != null && rProfiles.getResult().getRealName() != null) {
                        systemLogs.setUserName(rProfiles.getResult().getRealName());
                    }
                }

                systemLogs.setOperator(OpBusinessType.getEnumByCode(sysOperLog.getBusinessType()).getDesc());
                logs.add(systemLogs);
            }
            systemLogBody.setSystemLogs(logs);

            return Result.data(systemLogBody);
        } catch (
                Exception e) {
            log.error("failed find to  cause:{}", Throwables.getStackTraceAsString(e));
            return Result.fail("查询失败");
        }
    }

    /**
     * llyj
     *
     * @param pageNo
     * @param pageSize
     * @param shopId
     * @param preCriteria
     * @return
     */
    @GetMapping("/get-shop-user-info/new")
    public Result getVipInfoNew(@RequestParam(defaultValue = "1") int pageNo,
                                @RequestParam(defaultValue = "10") int pageSize,
                                @RequestParam long shopId,
                                @RequestParam Map<String, String> preCriteria) {
        try {
            CommonUser user = UserUtil.getCurrentUser();
            if (user == null) {
                return Result.fail(-1, "请登录");
            }
            if (ObjectUtils.isEmpty(shopId)) {
                return Result.fail(-1, "店铺信息id缺失");
            }
            PageParam pageParam = new PageParam();
            pageParam.setPage(pageNo);
            pageParam.setSize(pageSize);
            SysOperCriteria criteria = new SysOperCriteria();
            criteria.setShopId(shopId);

            if (preCriteria != null && preCriteria.containsKey("startTime")) {
                if (preCriteria.get("startTime").length() > 0) {
                    preCriteria.put("startTime", preCriteria.get("startTime") + " 00:00:00");
                    criteria.setStartTime(dateToStamp(preCriteria.get("startTime")));
                }
            }
            if (preCriteria != null && preCriteria.containsKey("endTime")) {
                if (preCriteria.get("endTime").length() > 0) {
                    preCriteria.put("endTime", preCriteria.get("endTime") + " 23:59:59");
                    criteria.setEndTime(dateToStamp(preCriteria.get("endTime")));
                }
            }

            if (preCriteria != null && preCriteria.containsKey("name")) {
                Response<List<Long>> userResponse = userReadService.findIdsByName(preCriteria.get("name"));
                //userProfileReadService.findProfileByNickName(preCriteria.get("name"));
                // userReadService.findIdsByName(preCriteria.get("name"));
                List<Long> list = new ArrayList<>();
                if (userResponse.isSuccess() && userResponse.getResult() != null && !userResponse.getResult().isEmpty()) {
                    for (Long s : userResponse.getResult()) {
                        list.add(s);
                    }
                }

                Either<List<StoreProxy>> listResult = storeProxyReadService.findByProxyShopName(preCriteria.get("name"));
                if (listResult.isSuccess() && listResult.take() != null && !listResult.take().isEmpty()) {
                    for (StoreProxy s : listResult.take()) {
                        list.add(s.getUserId());
                    }
                }
                Response<User> mobileUser = userReadService.findByMobile(preCriteria.get("name"));
                if (mobileUser.isSuccess() && mobileUser.getResult() != null) {
                    list.add(mobileUser.getResult().getId());
                }
                criteria.setUserIds(list);
            }

            if (preCriteria != null && preCriteria.containsKey("status") && preCriteria.get("status") != null) {
                criteria.setStatus(Long.valueOf(preCriteria.get("status")));
            }

            if (preCriteria != null && preCriteria.containsKey("businessType") && preCriteria.get("businessType") != null) {
                criteria.setBusinessType(preCriteria.get("businessType"));
            }

            if (preCriteria != null && preCriteria.containsKey("flag") && preCriteria.get("flag") != null) {
                criteria.setFlag(preCriteria.get("flag"));
            }
            if (preCriteria != null && preCriteria.containsKey("name") && criteria.getUserIds().isEmpty()) {
                criteria.setUserIds(Arrays.asList(-1L));
            }

            SearchResult<SysOperLog> listResult = sysOperLogReadService.findSysOperLogAll(pageParam, criteria);
            if (listResult.getResult().isEmpty()) {
                return Result.data(null);
            }
            SystemLogBody systemLogBody = new SystemLogBody();
            systemLogBody.setPage(listResult.getPagination().getPage());
            systemLogBody.setSize(listResult.getPagination().getSize());
            systemLogBody.setTotal(listResult.getPagination().getTotal());
            systemLogBody.setTotalPage(listResult.getPagination().getTotalPage());

            List<SystemLogs> logs = new ArrayList<>();
            for (SysOperLog sysOperLog : listResult.getResult()) {
                SystemLogs systemLogs = new SystemLogs();
                BeanUtils.copyProperties(sysOperLog, systemLogs);
                Response<User> rProfile = userReadService.findById(sysOperLog.getUserId());//findProfileByUserId(sysOperLog.getUserId());
                if (rProfile.isSuccess() && rProfile.getResult() != null) {
                    if (rProfile.getResult().getName() != null) {
                        systemLogs.setUserName(rProfile.getResult().getName());
                    } else {
                        systemLogs.setUserName(rProfile.getResult().getMobile());
                    }
                }

                if (!ObjectUtils.isEmpty(systemLogs.getShopId())) {
                    Either<Optional<StoreProxy>> optionalResult = storeProxyReadService.findByShopIdAndUserId(sysOperLog.getShopId(), sysOperLog.getUserId());
                    if (optionalResult.isSuccess() && optionalResult.take().isPresent()) {
                        systemLogs.setUserName(optionalResult.take().get().getProxyShopName());
                    }
                }

                if (ObjectUtils.isEmpty(systemLogs.getUserName())) {
                    Response<UserProfile> rProfiles = userProfileReadService.findProfileByUserId(sysOperLog.getUserId());
                    if (rProfiles.isSuccess() && rProfiles.getResult() != null && rProfiles.getResult().getRealName() != null) {
                        systemLogs.setUserName(rProfiles.getResult().getRealName());
                    }
                }

                systemLogs.setOperator(OpBusinessType.getEnumByCode(sysOperLog.getBusinessType()).getDesc());
                logs.add(systemLogs);
            }
            systemLogBody.setSystemLogs(logs);
            return Result.data(systemLogBody);
        } catch (
                Exception e) {
            log.error("failed find to  cause:{}", Throwables.getStackTraceAsString(e));
            return Result.fail(-1, "系统异常");
        }
    }

    @Data
    public class SystemLogBody {
        List<SystemLogs> systemLogs;
        Integer page;
        Integer size;
        Integer totalPage;
        Integer total;
    }


    @Data
    class SystemLogs {
        Long userId;
        Long shopId;
        String userName;
        //        String shopName;
        String title;
        String name;
        Integer status;
        String statusDesc = "";
        String operator;
        Long createAt;

    }

    public static Long dateToStamp(String s) throws Exception {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = simpleDateFormat.parse(s);
        long ts = date.getTime();
        return ts;
    }
}
