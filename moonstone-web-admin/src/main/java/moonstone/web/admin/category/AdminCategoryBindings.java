/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.admin.category;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.BackCategoryCacher;
import moonstone.category.model.BackCategory;
import moonstone.category.service.CategoryBindingReadService;
import moonstone.category.service.CategoryBindingWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.List;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-25
 */
@RestController
@Slf4j
@RequestMapping("/api/categoryBindings")
public class AdminCategoryBindings {

    @RpcConsumer
    private CategoryBindingReadService categoryBindingReadService;

    @RpcConsumer
    private CategoryBindingWriteService categoryBindingWriteService;

    @Autowired
    private BackCategoryCacher backCategoryCacher;

    @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<BindingShow> findByFrontCategoryId(@RequestParam("fid")Long frontCategoryId){
        Response<List<BackCategory>> r = categoryBindingReadService.findByFrontCategoryId(frontCategoryId);
        if(!r.isSuccess()){
            log.error("failed to find category bindings for front category(id={}),error code:{}",
                    frontCategoryId, r.getError());
            throw new JsonResponseException(r.getError());
        }


        final List<BackCategory> backCategories = r.getResult();
        List<BindingShow> result = Lists.newArrayListWithCapacity(backCategories.size());
        for (BackCategory backCategory : backCategories) {
            List<BackCategory> ancestors = backCategoryCacher.findAncestorsOf(backCategory.getId());
            List<String> categoryNames = Lists.newArrayListWithCapacity(ancestors.size());
            for (BackCategory ancestor : ancestors) {
                categoryNames.add(ancestor.getName());
            }
            result.add(new BindingShow(backCategory.getId(), Joiner.on('>').skipNulls().join(categoryNames)));
        }
        return result;
    }

    @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long bind(@RequestParam("fid")Long frontCategoryId,
                               @RequestParam("bid")Long backCategoryId){
        Response<Long> r = categoryBindingWriteService.bind(frontCategoryId, backCategoryId);
        if(!r.isSuccess()){
            log.error("failed to bind front category(id={}) with back category(id={}),error code:{}",
                    frontCategoryId, backCategoryId, r.getError());
            throw new JsonResponseException(r.getError());
        }
        return r.getResult();
    }

    @RequestMapping(value = "/fid/{fid}/bid/{bid}",method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean unbind(@PathVariable("fid")Long frontCategoryId,
                          @PathVariable("bid")Long backCategoryId){
        Response<Boolean> r = categoryBindingWriteService.unBind(frontCategoryId, backCategoryId);
        if(!r.isSuccess()){
            log.error("failed to unbind front category(id={}) with back category(id={}),error code:{}",
                    frontCategoryId, backCategoryId, r.getError());
            throw new JsonResponseException(r.getError());
        }
        return r.getResult();
    }

    public static class BindingShow implements Serializable{

        private static final long serialVersionUID = -2901349425148529298L;

        @Getter
        private final Long backCategoryId;

        @Getter
        private final String path;

        public BindingShow(Long backCategoryId, String path) {
            this.backCategoryId = backCategoryId;
            this.path = path;
        }
    }
}
