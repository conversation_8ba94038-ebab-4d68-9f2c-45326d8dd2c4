package moonstone.wxa.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.wxa.model.WxaTemplate;

import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/11/12.
 */
public interface WxaTemplateReadService {
    Response<WxaTemplate> findById(Long id);

    /**
     * 根据查询条件分页查询模板列表
     *
     * @param query 查询条件
     * @return 模板列表
     */
    Paging<WxaTemplate> pages(Map<String,Object> query);

    /**
     * 根据查询条件查询小程序模板信息列表
     *
     * @param query 条件查询
     * @return 小程序模板信息列表
     */
    List<WxaTemplate> list(Map<String, Object> query);

    /**
     * 根据查询条件查询对应的数量
     * @param query 查询条件
     * @return 数量
     */
    long count(Map<String, Object> query);

    /**
     * 根据查询条件查询小程序模板信息
     * @param query 查询条件
     * @return 小程序模板信息
     */
    WxaTemplate getOne(Map<String, Object> query);
}
