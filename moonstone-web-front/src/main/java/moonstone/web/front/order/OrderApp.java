package moonstone.web.front.order;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.AppResponse;
import moonstone.common.utils.UserUtil;
import moonstone.item.service.SkuReadService;
import moonstone.order.dto.SubmittedOrder;
import moonstone.order.dto.SubmittedSku;
import moonstone.order.dto.SubmittedSkusByShop;
import moonstone.user.model.UserCertification;
import moonstone.user.service.UserCertificationReadService;
import moonstone.web.front.order.dto.AppSubmitOrderVO;
import moonstone.web.front.order.web.Orders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> shark
 * @date 2020/5/25 16:19
 */
@Slf4j
@RestController
@RequestMapping("/api/app/v1")
public class OrderApp {
    @Autowired
    private Orders orders;
    @RpcConsumer
    private SkuReadService skuReadService;
    @RpcConsumer
    private UserCertificationReadService userCertificationReadService;

    /**
     * 提交订单
     *
     * @param orderBody
     * @return
     */
    @RequestMapping(value = "/order", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public AppResponse create(@RequestBody AppSubmitOrderVO orderBody) {
        log.info("orderBody:{}", orderBody);
        try {
            //组装下单参数
            SubmittedOrder submittedOrder = info(orderBody);
            //调用订单创建
            List<Long> orderIds = orders.create(submittedOrder, UserUtil.getCurrentUser());
            if (orderIds == null || orderIds.isEmpty()) {
                return AppResponse.error("下单失败");
            }
            return AppResponse.ok(orderIds);
        } catch (Exception e) {
            log.error("failed to create {}, cause:{}", orderBody, Throwables.getStackTraceAsString(e));
            throw e;
        }
    }

    /**
     * 关闭订单
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/order/cancel", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public AppResponse buyerCancelOrder(Long id) {
        Integer orderType = 1; // 订单类型默认为1 店铺级别
        if (!orders.buyerCancelOrder(id, orderType)) {
            return AppResponse.error();
        }
        return AppResponse.ok();
    }

    /**
     * 删除订单
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "/order/del", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public AppResponse deleteOrder(Long id) {
        if (!orders.deleteShopOrder(id)) {
            return AppResponse.error();
        }
        return AppResponse.ok();
    }

    /**
     * 组装下单参数
     *
     * @param submitOrderBody
     * @return
     */
    private SubmittedOrder info(AppSubmitOrderVO submitOrderBody) {
        SubmittedOrder submittedOrder = new SubmittedOrder();
        submittedOrder.setReceiverInfoId(submitOrderBody.getAddressId());//设置收货地址
        submittedOrder.setChannel(3);//设置来源APP
        submittedOrder.setPayType(1);//支付方式在线支付
        List<SubmittedSkusByShop> submittedSkusByShops = new ArrayList<>();
        SubmittedSkusByShop submittedSkusByShop = new SubmittedSkusByShop();
        SubmittedSkusByShop submittedSkusByShopParam = submitOrderBody.getSubmittedSkusByShops().get(0); // 默认只有一个
        submittedSkusByShop.setBuyerNote(submittedSkusByShopParam.getBuyerNote());//买家备注
        submittedSkusByShop.setReceiverInfoId(submitOrderBody.getAddressId());//设置收货地址
        submittedSkusByShop.setShopId(submittedSkusByShopParam.getShopId());//设置店铺id

        List<SubmittedSku> submittedSkus = new ArrayList<>();
        for (SubmittedSku submitOrderSkuBody : submittedSkusByShopParam.getSubmittedSkus()) {
            SubmittedSku submittedSku = new SubmittedSku();
            submittedSku.setSkuId(submitOrderSkuBody.getSkuId());
            submittedSku.setQuantity(submitOrderSkuBody.getQuantity());
            submittedSkus.add(submittedSku);
        }
        submittedSkusByShop.setSubmittedSkus(submittedSkus);
        submittedSkusByShops.add(submittedSkusByShop);  // sku信息
        submittedOrder.setSubmittedSkusByShops(submittedSkusByShops);
        //实名信息
        if (submitOrderBody.getRealNameId() != null) {
            Response<UserCertification> ucResponse = userCertificationReadService.findById(submitOrderBody.getRealNameId());
            if (!ucResponse.isSuccess()) {
                log.error("fail to find certification by id={}", submitOrderBody.getRealNameId());
                throw new JsonResponseException("user.certification.find.fail");
            }
            submittedOrder.setExtra(new HashMap<>() {{
                put("payerNo", ucResponse.getResult().getPaperNo());
                put("payerName", ucResponse.getResult().getPaperName());
            }});
        }
        return submittedOrder;
    }

}
