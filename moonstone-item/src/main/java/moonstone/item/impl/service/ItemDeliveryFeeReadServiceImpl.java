package moonstone.item.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.delivery.impl.dao.ItemDeliveryFeeDao;
import moonstone.delivery.model.ItemDeliveryFee;
import moonstone.item.service.ItemDeliveryFeeReadService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class ItemDeliveryFeeReadServiceImpl implements ItemDeliveryFeeReadService {

    @Resource
    private ItemDeliveryFeeDao itemDeliveryFeeDao;

    @Override
    public Response<ItemDeliveryFee> findByItemId(Long itemId) {
        try {
            return Response.ok(itemDeliveryFeeDao.findByItemId(itemId));
        } catch (Exception ex) {
            log.error("ItemDeliveryFeeReadServiceImpl.findByItemId error, itemId={}", itemId, ex);
            return Response.fail(ex.getMessage());
        }
    }
}
