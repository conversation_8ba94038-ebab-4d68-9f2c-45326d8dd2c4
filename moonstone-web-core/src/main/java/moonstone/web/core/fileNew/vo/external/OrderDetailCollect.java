package moonstone.web.core.fileNew.vo.external;

import lombok.Data;

import java.util.List;

/**
 * author：书生
 */
@Data
public class OrderDetailCollect {

    /**
     * 商品优惠金额
     */
    private Long itemDiscountAmount;

    /**
     * 商品总金额（总货款）
     */
    private Long itemTotalAmount;

    /**
     * 商品数量
     */
    private Integer itemQuantity;

    /**
     * 订单明细
     */
    private List<ExternalOrderDetail> orderDetailList;

}
