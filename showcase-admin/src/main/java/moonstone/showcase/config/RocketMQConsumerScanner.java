package moonstone.showcase.config;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;

@Component
public class RocketMQConsumerScanner {

    @Autowired
    private ApplicationContext applicationContext;

    @PostConstruct
    public void printConsumersInSingleLine() {
        Map<String, RocketMQListener> listeners = applicationContext.getBeansOfType(RocketMQListener.class);

        listeners.forEach((beanName, listener) -> {
            Class<?> targetClass = AopUtils.getTargetClass(listener);
            RocketMQMessageListener annotation = targetClass.getAnnotation(RocketMQMessageListener.class);

            if (annotation != null) {
                System.out.printf("RocketMQ消费者  bean=%s, class=%s, topic=%s, group=%s, selector=%s:%s, mode=%s/%s%n",
                        beanName,
                        targetClass.getSimpleName(),
                        annotation.topic(),
                        annotation.consumerGroup(),
                        annotation.selectorType(),
                        annotation.selectorExpression(),
                        annotation.consumeMode(),
                        annotation.messageModel());
            }
        });
    }
}