alter table `parana_payments` add `pay_request` varchar(4000) default '' COMMENT '支付请求数据' after `channel`;
alter table `parana_payments` add `pay_response` varchar(1000) default '' COMMENT '支付响应数据' after `pay_request`;
alter table `parana_payments` add `push_status` tinyint(1) DEFAULT '0' COMMENT '推送状态' after `pay_response`;

alter table `parana_payments` modify column `pay_request` varchar(8000);
alter table `parana_payments` modify column `pay_response` varchar(8000);
