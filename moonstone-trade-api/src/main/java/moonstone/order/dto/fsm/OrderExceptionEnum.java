package moonstone.order.dto.fsm;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * author：书生
 */
@Getter
@AllArgsConstructor
public enum OrderExceptionEnum {

    /**
     * 恢复正常
     */
    CLEAR_ERROR(10000, ""),

    /**
     * 支付人信息错误
     */
    PAYEE_INFO_ERROR(101, "支付人信息错误"),

    /**
     * 快递不可达
     */
    EXPRESS_UNREACHABLE(102, "快递不可达"),

    /**
     * 清关失败
     */
    CLEARANCE_FAILED(103, "清关失败"),

    /**
     * 风控异常
     */
    FEN_YIN_EXCEPTION(104, "风控异常"),

    /**
     * 超年限
     */
    OVER_YEAR(105, "超年限"),

    /**
     * 系统错误
     */
    SYSTEM_ERROR(106, "系统错误"),


    /**
     * 上传支付原始信息失败
     */
    UPLOAD_PAYMENT_ORIGINAL_INFO_FAILED(107, "上传支付原始信息失败"),

    ;

    private final int code;
    private final String message;


    public static OrderExceptionEnum getEnum(Integer code) {
        if (code == null) {
            return CLEAR_ERROR;
        }
        for (OrderExceptionEnum orderExceptionEnum : OrderExceptionEnum.values()) {
            if (orderExceptionEnum.getCode() == code) {
                return orderExceptionEnum;
            }
        }
        return CLEAR_ERROR;
    }
}
