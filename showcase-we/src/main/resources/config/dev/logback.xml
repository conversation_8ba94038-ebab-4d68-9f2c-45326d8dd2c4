<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false" scanPeriod="60 seconds">
	<!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
	<springProperty scope="context" name="app.name" source="application.name"/>

	<property name="log.level" value="info" />
	<property name="CHARSET" value="utf-8"/>

	<!-- 控制台彩色日志格式 -->
	<property name="CONSOLE_LOG" value="${CONSOLE_LOG:-%clr(%X{trace.id}){blue} %clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}" />

	<!-- 简化输出过程：不使用颜色解析器 -->
	<property name="FILE_LOG" value="${FILE_LOG:-%X{trace.id} %d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} [%15.15t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}" />

	<!-- 导入spring的默认配置：含颜色解析器，异常解析器等-->
	<include resource="org/springframework/boot/logging/logback/defaults.xml"/>

	<!-- 控制台输出 -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<!--测试开启debug及以上的日志-->
		<encoder>
			<pattern>${CONSOLE_LOG}</pattern>
			<charset>${CHARSET}</charset>
		</encoder>
	</appender>


	<!--	mysql日志 预发先开启mysql日志-->
	<!--	<logger name="p6spy" level="error"/>-->
	<logger name="com.alibaba.nacos" level="warn"/>
	<logger name="com.alibaba.nacos.client.identify" level="error"/>
	<logger name="com.xxl.job" level="warn"/>
	<logger name="org.redisson" level="error"/>
	<logger name="ch.qos.logback.core" level="error"/>
	<logger name="ch.qos.logback.classic" level="warn"/>
	<logger name="ReconfigureOnChangeTask" level="warn"/>

	<root level="INFO">
		<appender-ref ref="console"/>
	</root>
</configuration>