package moonstone.order.service;

import io.terminus.common.model.Response;
import moonstone.order.enu.FundsTransferPaymentRelatedTypeEnum;
import moonstone.order.model.FundsTransferPayment;

import java.util.List;

public interface FundsTransferPaymentReadService {

    Response<List<FundsTransferPayment>> findBy(Long relatedOrderId, FundsTransferPaymentRelatedTypeEnum type);

    Response<FundsTransferPayment> findById(Long fundsTransferPaymentId);
}
