package moonstone.web.admin.shop.migrate;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.criteria.UserRelationEntityCriteria;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.web.admin.shop.migrate.component.SubStoreEntityMigrateComponent;
import moonstone.web.admin.shop.migrate.request.MigrateRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * subStore模式下，服务商、门店、导购等身份信息迁移
 */
@Slf4j
@RestController
public class SubStoreEntityMigration {

    @Resource
    private SubStoreEntityMigrateComponent subStoreEntityMigrateComponent;

    @Resource
    private SubStoreReadService subStoreReadService;

    @Resource
    private SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;

    @Resource
    private UserRelationEntityReadService userRelationEntityReadService;

    /**
     * 迁移内容:
     * ServiceProvider (mongoDB)
     * parana_sub_store
     * parana_sub_store_t_store_guider
     * <p>
     * parana_user_relation_entity (type = 4)
     *
     * @param request
     * @return
     */
    @PostMapping("/api/shop/SubStoreEntity/migrate")
    public APIResp<Boolean> migrate(@RequestBody MigrateRequest request) {
        if (request == null || request.getSourceShopId() == null || request.getTargetShopId() == null) {
            return APIResp.error("入参缺失");
        }

        // ServiceProvider
        subStoreEntityMigrateComponent.migrateServiceProvider(request.getSourceShopId(), request.getTargetShopId());

        // parana_sub_store、 parana_sub_store_t_store_guider
        var stores = subStoreReadService.findByShopId(request.getSourceShopId()).getResult();
        if (CollectionUtils.isEmpty(stores)) {
            return APIResp.ok(true, "源商家的门店数据集为空");
        }
        // key = subStoreId
        var guiderMap = findGuiderMap(request.getSourceShopId());

        for (var store : stores) {
            subStoreEntityMigrateComponent.migrateSubStoreAndGuider(request.getTargetShopId(), store, guiderMap.get(store.getId()));
        }

        // parana_user_relation_entity (type = 4)
        subStoreEntityMigrateComponent.migrateSuperRelation(request.getSourceShopId(), request.getTargetShopId());

        return APIResp.ok(true);
    }

    /**
     * 迁移内容:
     * parana_user_relation_entity ( RelationId = shopId, Type= UserRelationEntity.UserRelationType.Member )
     * <p>
     * UserWithTags (显辉没有这些数据，不管了)
     *
     * @param request
     * @return
     */
    @PostMapping("/api/shop/SubStoreEntity/migrateUserRelationEntity")
    public APIResp<Boolean> migrateUserRelationEntity(@RequestBody MigrateRequest request) {
        if (request == null || request.getSourceShopId() == null || request.getTargetShopId() == null) {
            return APIResp.error("入参缺失");
        }

        var criteria = new UserRelationEntityCriteria();
        criteria.setRelationId(request.getSourceShopId());
        criteria.setType(UserRelationEntity.UserRelationType.Member.getType());
        criteria.setSortBy("id");

        for (int pageNo = 1, pageSize = 500; ; pageNo++) {
            criteria.setPageNo(pageNo);
            criteria.setPageSize(pageSize);

            var list = userRelationEntityReadService.paging(criteria).getResult().getData();
            if (CollectionUtils.isEmpty(list)) {
                log.info("SubStoreEntityMigration.migrateUserRelationEntity, pageNo={}, 数据据集为空", pageNo);
                break;
            }

            subStoreEntityMigrateComponent.migrateMemberRelation(list, request.getTargetShopId());
        }

        return APIResp.ok(true);
    }

    /**
     * @param sourceShopId
     * @return key = subStoreId
     */
    private Map<Long, List<SubStoreTStoreGuider>> findGuiderMap(Long sourceShopId) {
        var guiders = subStoreTStoreGuiderReadService.findAllByShopId(sourceShopId).getResult();
        if (CollectionUtils.isEmpty(guiders)) {
            log.info("源商家的导购数据集为空");
            return Collections.emptyMap();
        }

        return guiders.stream().collect(Collectors.groupingBy(SubStoreTStoreGuider::getSubStoreId));
    }
}
