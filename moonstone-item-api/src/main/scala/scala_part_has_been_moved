# scala part has been moved

for the compile speed and coder, scala part code has been move to a new dependency `trade.herald:scala.compatible.common`
pom.xml is like
``
    <dependency>
            <groupId>trade.herald</groupId>
            <artifactId>scala.compatible.common</artifactId>
            <version>1.4.2-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>io.parana</groupId>
                    <artifactId>parana-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
``

the scala.compatible.common is also on gitlab, it depend the io.parana:parana-trade-api at a special version.
if you need change the scala part, you need check out the the dependency, and remember it should only depend the api module.