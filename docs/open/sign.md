## 调用开放api

开放平台用户可以通过发送http请求来调用服务, 例如

```
http://parana.dithub.com/api/gateway?appKey=foobar&pampasCall=say.hi&name=dadu&sign=b5728f7e2a7c2cca1eb8a07d70ac8a1d
```

* ```/api/gateway``` 是开放平台统一网关地址
* ```appKey```是分配给调用者的唯一标识
* ```pampasCall=say.hi```表示要调用key为say.hi的开放api
* ```name```为该开放api的调用参数
* ```sign```是表示签名参数签名, 防止被篡改


## 签名生成步骤

所有调用参数(包括pampasCall, 但不包括sign本身), 按照字母序升序排列, 然后调用者再附加上给分配给自己的appSecret, 再做md5签名

比如调用参数为

```
/api/gateway?appKey=foobar&pampasCall=say.hi&name=dadu
```

则首先参数名按照字母序升序排列, 得到

```
appKey=foobar&name=dadu&pampasCall=say.hi
```

再假设分配给客户的appSecret为my.secret, 则将其附加到参数末尾, 得到

```
appKey=foobar&name=dadu&pampasCall=say.himy.secret
```

再计算这段字符串的md5,得到校验码为```b5728f7e2a7c2cca1eb8a07d70ac8a1d```, 所以最后的请求为:

```
/api/gateway?appKey=foobar&pampasCall=say.hi&name=dadu&sign=b5728f7e2a7c2cca1eb8a07d70ac8a1d
```

## 签名生成示例代码

```java
import com.google.common.base.Charsets;
import com.google.common.base.Joiner;
import com.google.common.hash.HashFunction;
import com.google.common.hash.Hashing;

import java.util.Map;
import java.util.TreeMap;

public final class SignGenerator {

    private static final HashFunction md5 = Hashing.md5();

    /**
     * 分配给客户的appSecret
     */
    private static final String SECRET = "my.secret";

    public static String generateSign(Map<String, Object> params) {
        //对参数名按照字母序升序排序
        Map<String, Object> sortedParams = new TreeMap<>(params);

        //排列参数对
        String toVerify = Joiner.on('&').withKeyValueSeparator("=").join(sortedParams);

        //把appSecret附加到末尾,然后md5一把
        String sign = md5.newHasher()
                .putString(toVerify, Charsets.UTF_8)
                .putString(SECRET, Charsets.UTF_8)
                .hash()
                .toString();
        return sign;
    }
}
```


## app端调用

* 对于所有需要登录的接口,需要传递sid参数,sid参数参与签名计算。