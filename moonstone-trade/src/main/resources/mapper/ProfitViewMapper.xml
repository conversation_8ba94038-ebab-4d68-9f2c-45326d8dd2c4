<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="ProfitView">
    <resultMap id="ProfitViewMapper" type="moonstone.order.model.ProfitView">
        <id column="id" property="id"/>
        <result column="profit_a" property="profitA"/>
        <result column="profit_b" property="profitB"/>
        <result column="profit_c" property="profitC"/>
        <result column="profit_d" property="profitD"/>
        <result column="profit_e" property="profitE"/>
        <result column="hash" property="hash"/>
    </resultMap>
    <sql id="tb">
        profit_view
    </sql>

    <select id="findById" parameterType="long" resultMap="ProfitViewMapper">
        SELECT
        `id`, `profit_a`, `profit_b`, `profit_c`, `profit_d`, `profit_e`, `hash`
        FROM
        <include refid="tb"/>
        <where>
            `id` = #{id}
        </where>
    </select>

    <insert id="create" parameterType="moonstone.order.model.ProfitView" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO <include refid="tb"/>
        (`hash`, `profit_a`, `profit_b`, `profit_c`, `profit_d`, `profit_e`)
        VALUES (#{hash}, #{profitA}, #{profitB}, #{profitC}, #{profitD}, #{profitE}) on duplicate key  update `hash` = #{hash}
    </insert>

    <select id="findByHash" parameterType="map" resultMap="ProfitViewMapper">
        SELECT
        `id`, `profit_a`, `profit_b`, `profit_c`, `profit_d`, `profit_e`, `hash`
        FROM
        <include refid="tb"/>
        <where>
            `hash` = #{hash}
        </where>
    </select>

</mapper>