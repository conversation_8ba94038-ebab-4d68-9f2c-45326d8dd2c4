package moonstone.web.front.open.thirdParty;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.EmailReceiverGroup;
import moonstone.common.model.ErrorWarnMsg;
import moonstone.common.model.Y800OpenRequestV3;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.web.core.component.pay.app.Json;
import moonstone.web.front.open.dto.Y800CallBack;
import moonstone.web.front.open.thirdParty.callback.Y800CallBackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/callback/Y800Storage")
public class CallBackForY800Storage {

    @Autowired(required = false)
    private List<Y800CallBackService<?>> y800CallBackServiceList;

    Map<String, Y800CallBackService<?>> callBackServiceMap = new HashMap<>();

    Map<String, Object> success = ImmutableMap.of("success", true);

    Y800CallBackService<?> notImplService = new Y800CallBackService<Y800CallBack>() {
        @Override
        public String getServiceName() {
            return null;
        }

        @Override
        public Object handle(Y800CallBack y800CallBack) {
            throw new RuntimeException(new Translate("尚未接入服务[%s]", getRequest().getServiceName()).toString());
        }
    };

    private Map<String, Object> wrong(String message) {
        return ImmutableMap.of("success", false, "errorMessage", message);
    }

    @PostConstruct
    public void regCallBack() {
        if (y800CallBackServiceList == null) {
            log.warn("{} not Y800 callBack service", LogUtil.getClassMethodName());
            return;
        }
        for (Y800CallBackService<?> y800CallBackService : y800CallBackServiceList) {
            callBackServiceMap.put(y800CallBackService.getServiceName(), y800CallBackService);
        }
    }

    @RequestMapping
    //public Map<String, Object> callBack(Y800OpenRequestV3 urlRequest, @RequestBody(required = false) Y800OpenRequestV3 jsonRequest) {
    public Map<String, Object> callBack(@RequestParam Map<String, Object> params) {
        log.info("CallBackForY800Storage.callBack, params={}", JSON.toJSONString(params));
        Y800OpenRequestV3 y800OpenRequestV3 = Json.OBJECT_MAPPER.convertValue(params, Y800OpenRequestV3.class);
        log.debug("CallBackForY800Storage.callBack, converted y800OpenRequestV3={}", JSON.toJSONString(y800OpenRequestV3));

        if (y800OpenRequestV3 == null) {
            return wrong(Translate.of("不支持空数据"));
        }
        try (Y800CallBackService<?> callBackService = callBackServiceMap.getOrDefault(y800OpenRequestV3.getServiceName(), notImplService)) {
            Object result = callBackService.handle(y800OpenRequestV3);
            log.debug("{} request(sign:{}) handle request[{}]", LogUtil.getClassMethodName(), y800OpenRequestV3.getSign(), result);
            if (Objects.equals(true, result)) {
                return success;
            }
            return wrong(result.toString());
        } catch (Exception ex) {
            log.error("{} fail to handle request [{}]", LogUtil.getClassMethodName(), y800OpenRequestV3.toMap(), ex);
            EventSender.sendApplicationEvent(ErrorWarnMsg.groupMsg("ShipmentCallBack", JSON.toJSONString(y800OpenRequestV3), ex, EmailReceiverGroup.DEVELOPER));
            return wrong(ex.getMessage());
        }
    }
}
