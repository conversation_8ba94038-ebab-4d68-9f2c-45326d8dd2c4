package moonstone.weShop.enums;

import com.google.common.base.Objects;

/**
 * Created by CaiZhy on 2018/12/17.
 */
public enum  WeShopProfitAvailableFlag {

    UNAVAILABLE(0),     //不可提现
    AVAILABLE(1);       //可提现

    private final int value;

    WeShopProfitAvailableFlag(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static WeShopProfitAvailableFlag fromInt(int value){
        for (WeShopProfitAvailableFlag weShopProfitAvailableFlag : WeShopProfitAvailableFlag.values()) {
            if(Objects.equal(weShopProfitAvailableFlag.getValue(), value)){
                return weShopProfitAvailableFlag;
            }
        }
        throw new IllegalArgumentException("unknown flag : " + value);
    }
}
