package moonstone.order.service;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import moonstone.order.dto.AccountStatementDetailCriteria;
import moonstone.order.model.AccountStatementDetail;
import moonstone.order.model.result.AccountStatementDetailDO;

import java.util.List;

public interface AccountStatementDetailReadService {

    Response<Paging<AccountStatementDetail>> findPage(AccountStatementDetailCriteria criteria);

    Response<List<AccountStatementDetail>> findPageList(AccountStatementDetailCriteria criteria);

    Response<List<AccountStatementDetail>> findByAccountStatementIds(List<Long> accountStatementIds);

    Response<List<AccountStatementDetail>> findByBalanceDetailIds(List<Long> balanceDetailIds);

    Response<Long> findMaxSkuOrderNum(Long accountStatementId);

    Response<List<AccountStatementDetailDO>> findCompletedWithNoWithdrawRecord(Long shopId, Long userId, List<Integer> withdrawStatus,
                                                                               Integer pageNO, Integer pageSize);

    Response<AccountStatementDetail> findValidByBalanceDetailId(Long balanceDetailId);

    Response<List<Long>> findOrderIds(Long accountStatementId, int pageNo, int pageSize);
}
