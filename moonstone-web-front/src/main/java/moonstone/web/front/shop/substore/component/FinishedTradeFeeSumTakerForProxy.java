package moonstone.web.front.shop.substore.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.utils.LogUtil;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.service.ShopOrderReadService;
import moonstone.user.model.StoreProxy;
import moonstone.user.service.StoreProxyReadService;
import moonstone.web.core.component.RecordManager;
import moonstone.web.core.model.Record;
import moonstone.web.core.model.dto.record.TradeAmountLTS;
import moonstone.web.core.model.enu.RecordDateLimitType;
import moonstone.web.core.user.StoreProxyManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class FinishedTradeFeeSumTakerForProxy {
    @Autowired
    private RecordManager recordManager;
    @Autowired
    private StoreProxyManager storeProxyManager;
    @Autowired
    private StoreProxyReadService storeProxyReadService;
    @Autowired
    private ShopOrderReadService shopOrderReadService;

    @Autowired
    private JedisPool jedisPool;

    /**
     * 获取 代理模式内 已完成交易(当确定收货时修改的)交易总额
     *
     * @param shopId 店铺Id
     * @param userId 用户Id
     * @return 总额
     */
    public Long getFinishedTradeFeeSum(Long shopId, Long userId) {
        Optional<StoreProxy> storeProxyOpt = storeProxyManager.getStoreProxyByShopIdAndUserId(shopId, userId);
        if (!storeProxyOpt.isPresent()) {
            return null;
        }
        StoreProxy storeProxy = storeProxyOpt.get();
        List<Record> selfList = recordManager.findRecord(userId, TradeAmountLTS.build(shopId, OrderOutFrom.LEVEL_Distribution, userId), RecordDateLimitType.longTerm);
        long sum = selfList.stream().findFirst().map(Record::getNum).orElse(0L);
        try (Jedis jedis = jedisPool.getResource()) {
            sum += Optional.ofNullable(jedis.get(String.format("[%s]%s(%s)", TradeAmountLTS.class.getSimpleName(), TradeAmountLTS.build(shopId, OrderOutFrom.LEVEL_Distribution, userId).getKey(), userId)))
                    .map(Long::valueOf).orElse(0L);
        } catch (Exception timeout) {
            log.error("{} redis read timeout", LogUtil.getClassMethodName(), timeout);
        }
        Long sumByOrder = shopOrderReadService.sumFeeBy(shopId, userId, OrderStatus.CONFIRMED.getValue(), OrderOutFrom.LEVEL_Distribution).take();
        if (Objects.equals(storeProxy.getLevel(), 2)) {
            // Just return what find
            return sumByOrder;
        }
        // return all include the sub storeProxy

        for (StoreProxy proxy : storeProxyReadService.findBySupperIdAndShopId(userId, shopId).take()) {
            sumByOrder += getFinishedTradeFeeSum(shopId, proxy.getUserId());
        }
        return sumByOrder;
    }
}
