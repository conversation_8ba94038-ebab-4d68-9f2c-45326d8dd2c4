package moonstone.shop.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.shop.model.StoreMonthlyPunch;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * author：书生
 */
@Repository
public class StoreMonthlyPunchDao extends MyBatisDao<StoreMonthlyPunch> {

    /**
     * 逻辑删除
     *
     * @param id 主键id
     * @return 默认都成功
     */
    public Boolean logicDelete(Long id) {
        this.getSqlSession().update(sqlId("logicDelete"), id);
        return true;
    }

    /**
     * 根据条件查询单条记录
     *
     * @param query 条件查询
     * @return 单条记录
     */
    public StoreMonthlyPunch selectOne(Map<String, Object> query) {
        return this.getSqlSession().selectOne(sqlId("selectOne"), query);
    }

    /**
     * 根据条件查询多条记录
     *
     * @param query 条件查询
     * @return 多条记录
     */
    public List<StoreMonthlyPunch> selectList(Map<String, Object> query) {
        return this.getSqlSession().selectList(sqlId("selectList"), query);
    }

    /**
     * 根据条件查询对应的数量
     *
     * @param query 条件查询
     * @return 数量
     */
    public Long count(Map<String, Object> query) {
        return this.getSqlSession().selectOne("count", query);
    }
}
