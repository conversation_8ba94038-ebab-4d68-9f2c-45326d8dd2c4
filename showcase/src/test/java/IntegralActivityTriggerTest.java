import moonstone.order.model.IntegralMallCodes;
import moonstone.showcase.ShowcaseApplication;
import moonstone.user.model.IntegralParanaDataStatus;
import moonstone.web.core.CoreWebConfiguration;
import moonstone.web.front.shop.jifen.IntegralGenImage;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

@RunWith(SpringRunner.class)
@ImportAutoConfiguration(CoreWebConfiguration.class)
@ActiveProfiles("dev")
@SpringBootTest(classes = ShowcaseApplication.class)
public class IntegralActivityTriggerTest {
    @Autowired
    private IntegralGenImage integralGenImage;

    @Rollback()
    @Transactional
    @Test
    public void triggerActivity() {
        IntegralMallCodes integralMallCodes = new IntegralMallCodes();

        integralMallCodes.setBatch("no_a_batch");
        integralMallCodes.setCode("here_is_my_code");
        integralMallCodes.setCreateAt(new Date().toInstant().getEpochSecond());
        integralMallCodes.setFaceValue(500L);
        integralMallCodes.setStatus(1);
        integralMallCodes.setShopId(82L);
        integralMallCodes.setThirdId(82L);
        integralMallCodes.setGenCode("not_a_bit");

        IntegralParanaDataStatus integralParanaDataStatus = new IntegralParanaDataStatus();
        BeanUtils.copyProperties(integralMallCodes, integralParanaDataStatus);
        integralParanaDataStatus.setStartNum("1");
        integralParanaDataStatus.setFlag(1);
        integralParanaDataStatus.setItemId(5480L);

        integralGenImage.addStoreIntegral(2334L, "here_is_my_code", 82, "192.168.1.1", false, integralMallCodes, integralParanaDataStatus, true, "", "", "");
        integralGenImage.addStoreIntegral(2333L, "here_is_my_code", 82, "192.168.1.1", true, integralMallCodes, integralParanaDataStatus, true, "", "", "");
    }
}
