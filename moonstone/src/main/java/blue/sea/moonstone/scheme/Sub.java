package blue.sea.moonstone.scheme;

import java.math.BigDecimal;

public class Sub implements SchemeFuc{

    @Override
    public AST execute(Executor executor, AST ast) {
        BigDecimal num = new BigDecimal("0");
        AST res = new AST();
        res.type = AST.AtomType.Number;
        if (ast.lisp.size() == 2) {
            AST value = executor.eval(ast.lisp.get(1));
            if (value.type != AST.AtomType.Number) {
                throw new RuntimeException("Sub require Number type arguments -> " + ast);
            }
            res.name = num.subtract(new BigDecimal(res.name)).toString();
            return res;
        } else if (ast.lisp.size() > 2) {
            num = new BigDecimal(executor.eval(ast.lisp.get(1)).name);
            for (int i = 2; i < ast.lisp.size(); i++) {
                num = num.subtract(new BigDecimal(executor.eval(ast.lisp.get(i)).name));
            }
            res.name = num.toString();
            return res;
        }
        throw new RuntimeException("Sub require one or more arguments -> " + ast);
    }
}
