CREATE TABLE parana.parana_withdraw_account (
	id BIGINT NULL,
	shop_id BIGINT NULL,
	user_id BIGINT NULL,
	name varchar(100) NULL,
	`from` varchar(100) NULL,
	account varchar(100) NULL,
	`type` INT NULL,
	status INT NULL,
	extra_str varchar(254) NULL,
	created_at DATE NULL,
	updated_at DATE NULL
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci
COMMENT='提现用的账户信息';
ALTER TABLE parana.parana_withdraw_account ADD CONSTRAINT parana_withdraw_account_PK PRIMARY KEY (id);
ALTER TABLE parana.parana_withdraw_account MODIFY COLUMN id bigint(20) NOT NULL AUTO_INCREMENT;
ALTER TABLE parana.parana_withdraw_account MODIFY COLUMN shop_id bigint(20) NOT NULL;
ALTER TABLE parana.parana_withdraw_account MODIFY COLUMN user_id bigint(20) NOT NULL;
CREATE INDEX parana_withdraw_account_shop_id_IDX USING BTREE ON parana.parana_withdraw_account (shop_id,user_id);

alter table parana.parana_with_draw_profit_apply add column withdraw_account_id bigint not null;
create index parana_with_draw_profit_apply_account_id using btree on parana_with_draw_profit_apply (withdraw_account_id);