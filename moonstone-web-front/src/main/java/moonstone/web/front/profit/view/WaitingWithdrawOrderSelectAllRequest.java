package moonstone.web.front.profit.view;

import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class WaitingWithdrawOrderSelectAllRequest extends WaitingWithdrawOrderQueryRequest {
    @Serial
    private static final long serialVersionUID = -7918432594120097084L;

    /**
     * 前端页已经勾选的支付单订单信息。
     * <br/>全选操作时，前端已经勾选的支付单订单信息将不会被改，如样返回给前端
     */
    private List<PaymentAndOrderPairVO> selectedList;
}
