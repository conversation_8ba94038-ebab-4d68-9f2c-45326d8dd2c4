package moonstone.shopMini.impl.dao;

import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.shopMini.model.ShopMiniBindQrCode;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * author：书生
 */
@Repository
public class ShopMiniBindQrCodeDao extends MyBatisDao<ShopMiniBindQrCode> {
    public List<ShopMiniBindQrCode> selectList(Map<String, Object> query) {
        return getSqlSession().selectList(sqlId("selectList"), query);
    }
}
