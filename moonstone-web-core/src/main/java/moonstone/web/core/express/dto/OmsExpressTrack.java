package moonstone.web.core.express.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class OmsExpressTrack extends OrderExpressTrack implements Serializable {

    @Serial
    private static final long serialVersionUID = -4706445059113772560L;

    private List<Node> operatorNodeList;

    @Data
    public static class Node {
        /**
         * 阶段状态描述，非枚举类型	String		仓库处理中
         */
        private String groupState;

        /**
         * 扫描状态描述，非枚举类型	String		派件
         */
        private String scanState;

        /**
         * 轨迹产生时间
         */
        private Date msgTime;

        /**
         * 节点描述
         */
        private String content;
    }
}
