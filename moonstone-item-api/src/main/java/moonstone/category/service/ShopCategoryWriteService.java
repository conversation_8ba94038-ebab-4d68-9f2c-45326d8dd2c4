/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.service;

import io.terminus.common.model.Response;
import moonstone.category.model.ShopCategory;

import java.util.List;

/**
 * 店铺内类目写服务
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-16
 */
public interface ShopCategoryWriteService {

    /**
     * 创建店铺内类目
     *
     * @param shopCategory 店铺内类目
     * @return 创建成功的店铺内类目
     */
    Response<ShopCategory> create(ShopCategory shopCategory);

    /**
     * 创建店铺内类目
     *
     * @param list 店铺内类目
     * @return
     */
    Response<Boolean> creates(List<ShopCategory> list);

    /**
     * 店铺内类目名称修改
     *
     * @param shopCategoryId 店铺内类目id
     * @param shopId         店铺id
     * @param name           类目名称
     * @return 是否更名成功
     */
    Response<Boolean> updateName(Long shopCategoryId, Long shopId, String name);

    /**
     * 删除店铺内类目
     *
     * @param shopCategoryId 店铺内类目id
     * @param shopId         店铺id
     * @return 删除结果
     */
    Response<Boolean> delete(Long shopCategoryId, Long shopId);

    /**
     * 移动类目
     *
     * @param shopCategoryId 类目 ID
     * @param direction      移动方向, 1: 向下, -1: 向上
     * @return 移动成功
     */
    Response<Boolean> move(Long shopCategoryId, int direction);

    /**
     * 更改是否默认展开
     *
     * @param shopCategoryId 类目 ID
     * @param disclosed      是否默认展开
     * @return 更新成功
     */
    Response<Boolean> updateDisclosed(Long shopCategoryId, Boolean disclosed);


    /**
     * 更新logo
     *
     * @param id   类目id
     * @param logo 类目logo
     * @return 是否更新成功
     */
    Response<Boolean> updateLogo(Long id, String logo);

    Response<Boolean> changeVisibility(Long shopCategoryId, Integer visibility);

    Response<Boolean> updatePid(Long shopCategoryId, Long newPid);
}
