package moonstone.integral.impl.service;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.integral.impl.dao.IntegralActivityDao;
import moonstone.integral.model.IntegralActivity;
import moonstone.integral.service.IntegralActivityWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class IntegralActivityWriteServiceImpl implements IntegralActivityWriteService {
    @Autowired
    private IntegralActivityDao integralActivityDao;

    @Override
    public Either<IntegralActivity> create(IntegralActivity integralActivity) {
        try {
            if (integralActivityDao.create(integralActivity)) {
                return Either.ok(integralActivity);
            }
            throw new RuntimeException("Sql return false");
        } catch (Exception ex) {
            log.error("{} fail to create [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(integralActivity), ex);
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> update(IntegralActivity integralActivity) {
        try {
            return Either.ok(integralActivity).map(integralActivityDao::update);
        } catch (Exception ex) {
            log.error("{} fail to update [{}]", LogUtil.getClassMethodName(), JSON.toJSONString(integralActivity), ex);
            return Either.error(ex);
        }
    }

    @Override
    public Either<Boolean> delete(Long id) {
        try {
            return Either.ok(id).map(integralActivityDao::delete);
        } catch (Exception ex) {
            log.error("{} fail to delete [{}]", LogUtil.getClassMethodName(), id, ex);
            return Either.error(ex);
        }
    }


}
