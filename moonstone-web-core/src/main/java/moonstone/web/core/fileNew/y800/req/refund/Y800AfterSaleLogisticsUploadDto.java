package moonstone.web.core.fileNew.y800.req.refund;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Y800AfterSaleLogisticsUploadDto {
	/**
	 * 访问代码
	 */
	private String accessCode;

	/**
	 * 订单号
	 */
	private String thirdNo;

	/**
	 * 售后单号
	 */
	private String afterSaleNo;

	/**
	 * 快递单号
	 */
	private String expressNo;

	/**
	 * 快递公司编号
	 */
	private String expressCode;

	/**
	 * 退货物流提交时间
	 */
	private Long returnTime;

}
