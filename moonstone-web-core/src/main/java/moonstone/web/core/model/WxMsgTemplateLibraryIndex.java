package moonstone.web.core.model;

import lombok.Data;

import java.util.List;

@Data
public class WxMsgTemplateLibraryIndex {
    Long totalCount;
    List<IndexList> list;

    public Long getTotal_count() {
        return getTotalCount();
    }

    public void setTotal_count(Long total_count) {
        setTotalCount(total_count);
    }

    @Data
    public static class IndexList {
        String id;// 模板标题id（获取模板标题下的关键词库时需要）
        String title;// 模板标题内容
    }
}
