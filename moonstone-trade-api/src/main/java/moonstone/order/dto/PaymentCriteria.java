package moonstone.order.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import moonstone.common.model.PagingCriteria;

import java.util.Date;

/**
 * 支付单查询条件
 *
 * DATE: 16/11/16 下午1:12 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PaymentCriteria extends PagingCriteria {
    private static final long serialVersionUID = -777855975666159433L;

    private String outId;
    private String paySerialNo;
    private Integer pushStatus;
    private Integer status;
    private String channel;
    /**
     * 支付完成开始时间
     */
    private Date paidStartAt;
    /**
     * 支付完成截止时间
     */
    private Date paidEndAt;
}
