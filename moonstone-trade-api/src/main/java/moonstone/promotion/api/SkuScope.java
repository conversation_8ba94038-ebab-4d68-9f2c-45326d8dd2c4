/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.api;

import moonstone.common.model.CommonUser;
import moonstone.order.dto.RichSku;

import java.util.Map;

/**
 * 营销面向的sku范围, 可以指定skuId, 也可以指定店铺内类目等
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-31
 */
public interface SkuScope {
    //  积分商品限定
    String REQUIRE_INTEGRAL = "require_integral";

    /**
     * 判断当前sku是否适用营销
     *
     * @param richSku        sku
     * @param skuScopeParams 涉及到的参数
     * @return 是否适用
     */
    boolean apply(RichSku richSku, CommonUser buyer, Map<String, String> skuScopeParams);

    /**
     * 检查优惠卷格式是否正确
     *
     * @param skuScopeParams 优惠卷范围
     * @return 正确或者错误
     */
    default boolean valid(Map<String, String> skuScopeParams) {
        return true;

    }
}
