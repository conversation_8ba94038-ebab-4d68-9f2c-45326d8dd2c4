package moonstone.web.front.shop.substore;

import com.alibaba.fastjson.JSON;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import moonstone.adv.model.Adv;
import moonstone.adv.service.AdvReadService;
import moonstone.adv.service.AdvWriteService;
import moonstone.category.model.ShopCategoryItem;
import moonstone.category.service.ShopCategoryItemReadService;
import moonstone.common.api.APIResp;
import moonstone.common.enums.CountryCode;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.model.IsPersistAble;
import moonstone.common.model.IsPresent;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.item.dto.paging.ItemCriteria;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.item.service.ItemWriteService;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.dto.*;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawAccount;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.WithdrawAccountReadService;
import moonstone.search.dto.IndexedItem;
import moonstone.search.dto.SearchedItem;
import moonstone.search.item.IndexedItemFactory;
import moonstone.shop.model.SubStore;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.shop.service.SubStoreWriteService;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.user.service.UserWriteService;
import moonstone.user.service.UserWxWriteService;
import moonstone.web.core.model.dto.CurrentProfitVO;
import moonstone.web.core.model.dto.WithdrawFeeRequest;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.core.user.UserRelationManager;
import moonstone.web.front.shop.substore.app.GuiderOrderSumApp;
import moonstone.web.front.shop.substore.app.SubStoreWithdrawAccountInitApp;
import moonstone.web.front.shop.substore.component.ItemVoPacker;
import moonstone.web.front.shop.substore.component.SubStoreProfitStatisticsComponent;
import moonstone.web.front.shop.substore.component.SubstoreImportter;
import moonstone.web.front.shop.substore.dto.RegisterInnerCodeDTO;
import moonstone.web.front.shop.substore.vo.ItemWithProfit;
import moonstone.web.front.shop.substore.vo.SubStoreProfitStatisticsVO;
import moonstone.web.front.shop.substore.web.SubStoreGuiderViewController;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RestController
@Slf4j
@RequestMapping("/api/subStore")
public class SubstoreInfoAndProfitViewController {
	/*
	暂时占用的门店source_id 当门店小程序单独投入使用后必须使用新的source_id
	推荐使用projectId来替代
	 */
	@Autowired
	SubStoreReadService subStoreReadService;
	@Autowired
	SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;
	@Autowired
	UserReadService<User> userUserReadService;
	@Autowired
	SubStoreWriteService subStoreWriteService;
	@Autowired
	ShopCategoryItemReadService shopCategoryItemReadService;
	@Autowired
	BalanceDetailManager balanceDetailManager;
	@Autowired
	AdvReadService advReadService;
	@Autowired
	AdvWriteService advWriteService;

	@Autowired
	ItemReadService itemReadService;
	@Autowired
	ItemWriteService itemWriteService;

	@Autowired
	ShopOrderReadService shopOrderReadService;
	@Autowired
	BalanceDetailReadService balanceDetailReadService;
	@Autowired
	ItemVoPacker itemVoPacker;

	@Autowired
	IndexedItemFactory indexedItemFacotry;

	@Autowired
	UserRelationManager userRelationManager;

	@Autowired
	UserWriteService<User> userWriteService;
	@Autowired
	WithdrawAccountReadService withdrawAccountReadService;

	@Autowired
	ShopWxaReadService shopWxaReadService;
	@Autowired
	UserWxWriteService userWxWriteService;
	@Autowired
	SubStoreWithdrawAccountInitApp subStoreWithdrawAccountInitApp;

	@Autowired
	GuiderCache guiderCache;
	@Autowired
	GuiderOrderSumApp guiderOrderSumApp;

	Cache<String, ByteArrayOutputStream> outputStreamCache = Caffeine.newBuilder().expireAfterWrite(1, TimeUnit.MINUTES).build();
	@Autowired
	SubstoreImportter importter;

	@Autowired
	private SubStoreCache subStoreCache;

	@Autowired
	private ServiceProviderCache serviceProviderCache;

	@Autowired
	private SubStoreProfitStatisticsComponent subStoreProfitStatisticsComponent;

	@PostMapping("/adv/add")
	public Long addAdv(@RequestParam(defaultValue = "28") Long sourceId, @RequestBody Adv adv) {
		/// 为了避免冲突 所以使用删除
		/// 且所有广告位占用 供应商的位置
		adv.setShopId(sourceId);
		adv.setStatus(-1);
		if (advWriteService.create(adv).isSuccess()) {
			return adv.getId();
		}
		return null;
	}

	@PostMapping("/adv/update")
	public boolean updateAdv(@RequestParam(defaultValue = "28") Long sourceId, @RequestBody Adv adv) {
		Adv found = Optional.ofNullable(advReadService.findById(adv.getId()).getResult()).orElseThrow(() -> new JsonResponseException(new Translate("查找失败,广告不存在或者已删除").toString()));
		if (found.getStatus() == -1 && found.getShopId().equals(sourceId)) {
			adv.setShopId(sourceId);
			adv.setStatus(-1);
			return advWriteService.update(adv).isSuccess();
		}
		return false;
	}

	@DeleteMapping("/adv/{id}")
	public boolean deleteAdv(@RequestParam(defaultValue = "28") Long sourceId, @PathVariable("id") long id) {
		Adv adv = Optional.ofNullable(advReadService.findById(id).getResult()).orElseThrow(() -> new JsonResponseException(new Translate("查找失败,广告不存在或者已删除").toString()));
		if (adv.getStatus() == -1 && adv.getShopId().equals(sourceId)) {
			return advWriteService.delete(adv.getId()).isSuccess();
		}
		return false;
	}

	/// 获取自己的门店
	public SubStore info(@RequestParam(defaultValue = "28") Long sourceId) throws RuntimeException {
		return info(sourceId, null);
	}

	@GetMapping("/info")
	public SubStore info(@RequestParam(defaultValue = "28", required = false) Long sourceId, @Nullable @RequestParam(required = false) Long id) {
		Long visitorId = UserUtil.getUserId();
		/// id 为null时返回自己的门店
		if (id == null) {
			User user = Optional.ofNullable(userUserReadService.findById(UserUtil.getUserId()).getResult()).orElseThrow(() -> new JsonResponseException("user.not.login"));
			if (user == null) {
				throw new JsonResponseException("user.not.login");
			}
			var rSubStores = subStoreReadService.findUserIdAndShopId(user.getId(), sourceId);
			if (rSubStores.isSuccess() && rSubStores.getResult() != null) {
				/// 只获取第一个门店
				return new SubStoreVo(rSubStores.getResult());
			}
			log.error("[subStores](info) no subStore find by UserId :{},cause: {}", user.getId(), rSubStores.getError());
			throw new JsonResponseException(new Translate("门店不存在").toString());

		}
		if (id == -1) {
			return new SubStore();
		}
		/// 当是获取他人的门店时 判断是否为其的会员
		var rSubStore = subStoreReadService.findById(id);
		if (rSubStore.isSuccess() && rSubStore.getResult() != null) {
			if (visitorId != null && !rSubStore.getResult().getUserId().equals(visitorId)) {
				userRelationManager.addSubStoreMember(rSubStore.getResult().getShopId(), visitorId);
			}
			return new SubStoreVo(rSubStore.getResult());
		}
		log.error("[subStores](info) no subStore find by id:{},cause: {}", id, rSubStore.getError());
		throw new JsonResponseException("subStore.not.find");
	}


	/**
	 * @see SubStoreGuiderViewController 已重构
	 */
	@Deprecated
	public List<CommonUser> guider_list(@RequestParam(defaultValue = "28") Long sourceId) {
		CommonUser user = UserUtil.getCurrentUser();
		Stack<Response<?>> responsesStack = new Stack<>();
		var rSubStores = subStoreReadService.findUserIdAndShopId(user.getId(), sourceId);
		responsesStack.push(rSubStores);
		if (rSubStores.isSuccess() && Objects.nonNull(rSubStores.getResult())) {
			var rSubStoreGuiderList = subStoreTStoreGuiderReadService.findBySubStoreId(rSubStores.getResult().getId());
			Map<Long, SubStoreTStoreGuider> guiderIdMapSubStoreTStoreGuider = new HashMap<>(8);
			Consumer<CommonUser> packNameAndShopName = (storeGuider) -> {
				Map<String, String> extra = guiderIdMapSubStoreTStoreGuider.get(storeGuider.getId()).getExtra();
				extra.put("name", guiderIdMapSubStoreTStoreGuider.get(storeGuider.getId()).getStoreGuiderNickname());
				guiderCache.findByShopIdAndUserId(sourceId, storeGuider.getId()).map(SubStoreTStoreGuider::getSubStoreId)
						.map(subStoreReadService::findById).map(Response::getResult).map(SubStore::getName)
						.ifPresent(name -> extra.put("shopName", name));
				if (storeGuider.getExtra() != null) {
					extra.putAll(storeGuider.getExtra());
				}
				storeGuider.setExtra(extra);
			};
			Function<User, CommonUser> fromUser = (originUser) -> {
				CommonUser guider = new CommonUser();
				BeanUtils.copyProperties(originUser, guider);
				Map<String, String> extra = guider.getExtra() == null ? new HashMap<>(8) : guider.getExtra();
				if (originUser.getMobile() != null) {
					if (!originUser.getFullMobile().startsWith(CountryCode.China.getCode())) {
						extra.put("mobile", originUser.getFullMobile());
					} else {
						extra.put("mobile", originUser.getMobile());
					}
				}
				guider.setExtra(extra);
				return guider;
			};
			responsesStack.push(rSubStores);
			if (rSubStoreGuiderList.isSuccess() && !CollectionUtils.isEmpty(rSubStoreGuiderList.getResult())) {
				rSubStoreGuiderList.getResult().forEach(relation -> guiderIdMapSubStoreTStoreGuider.put(relation.getStoreGuiderId(), relation));
				List<Long> userId = rSubStoreGuiderList.getResult().stream().map(SubStoreTStoreGuider::getStoreGuiderId).collect(Collectors.toList());
				var rGuiders = userUserReadService.findByIds(userId);
				responsesStack.push(rGuiders);
				if (rGuiders.isSuccess() && !CollectionUtils.isEmpty(rGuiders.getResult())) {
					return rGuiders.getResult().stream().map(fromUser)
							.peek(packNameAndShopName)
							.collect(Collectors.toList());
				}
			}
		}
		while (!responsesStack.empty()) {
			var res = responsesStack.pop();
			if (!res.isSuccess()) {
				log.error("[SubStore](guider-list) fail at find list,cause: {}", res.getError());
			}
		}
		return new ArrayList<>();
	}

	@PostMapping
	@RequestMapping("/info")
	public boolean updateSubStore(@RequestBody SubStore subStore) {
		User user = Optional.ofNullable(userUserReadService.findById(UserUtil.getUserId()).getResult()).orElseThrow(() -> new JsonResponseException("user.not.login"));
		CommonUser commonUser = UserUtil.getCurrentUser();
		var rSubStore = subStoreReadService.findById(subStore.getId());
		if (rSubStore.getResult() != null) {
			if (!user.getId().equals(rSubStore.getResult().getUserId()) && !commonUser.getShopId().equals(rSubStore.getResult().getShopId())) {
				throw new JsonResponseException("authorize.fail");
			}
			if (subStore.isAuthed()) {
				return false;
			}
			subStore.setShopId(null);
			subStore.setUserId(null);
			subStore.setAuthAt(null);
			if (subStore.getStatus() == -1) {
				subStore.setStatus(null);
			}
			subStore.initAuth();
			return subStoreWriteService.update(subStore).isSuccess();
		} else {
			log.error("[SubStore](update) fail to update subStore,cause: {}", rSubStore.getError());
			throw new JsonResponseException(rSubStore.getError());
		}
	}

	@PostMapping("/valid-code")
	public boolean validCode(@RequestBody RegisterInnerCodeDTO registerInnerCodeDTO) {
		return registerInnerCodeDTO.decode().isPresent();
	}

	/// 门店注册规则
	private void checkRuleOfSubStore(User user, SubStore subStore) {
		if (subStoreReadService.findUserIdAndShopId(user.getId(), subStore.getShopId()).getResult() != null) {
			throw new JsonResponseException(new Translate("该号码已为门店店主,请不要重复注册").toString());
		}
		Either<List<SubStoreTStoreGuider>> rExistsGuiders = subStoreTStoreGuiderReadService.findByStoreGuiderIdAndShopIdWithStatusBit(user.getId(), subStore.getShopId(), null);
		if (!rExistsGuiders.isSuccess()) {
			throw new JsonResponseException(new Translate("判断用户信息失败").toString());
		}
		if (!rExistsGuiders.take().isEmpty()) {
			throw new JsonResponseException(new Translate("该号码已经是导购员,无法成为门店店主").toString());
		}
	}

	@PostMapping
	@RequestMapping("/register")
	public Long register(@RequestBody RegisterInnerCodeDTO dto) {
		SubStore applySubStore = new SubStore();
		Long shopId = dto.decode()
				.orElseThrow(() -> new JsonResponseException(new Translate("邀请码无效").toString()));
		/// 初始化数值
		User user = Optional.ofNullable(userUserReadService.findById(UserUtil.getUserId()).getResult())
				.orElseThrow(() -> new JsonResponseException(new Translate("用户未登录").toString()));

		SubStore subStore = subStoreReadService.findUserIdAndShopId(user.getId(), shopId).getResult();
		if (subStore != null) {
			return subStore.getId();
		}
		applySubStore.setName(user.getMobile() + "的新门店");
		applySubStore.setShopId(shopId);
		applySubStore.setUserId(user.getId());
		applySubStore.setMobile(user.getFullMobile());/// 设置手机号

		applySubStore.setStatus(1);/// 默认1 为可用
		applySubStore.initAuth();
		applySubStore.setAuthAt(null);/// 设置未验证
		checkRuleOfSubStore(user, applySubStore);
		var rPersist = subStoreWriteService.create(applySubStore);
		if (rPersist.isSuccess() && rPersist.getResult()) {
			return applySubStore.getId();
		}
		log.error("[SubStore](persis) create SubStore failed by {},cause: {}", applySubStore, rPersist.getError());
		return null;
	}

	private Paging<ItemWithProfit> packItemIntoSearchedItem(Paging<Item> itemPaging) {
		Paging<ItemWithProfit> searchedItemPaging = Paging.empty();
		searchedItemPaging.setTotal(itemPaging.getTotal());
		searchedItemPaging.setData(itemPaging.getData().stream()
				.map(item -> itemReadService.findItemWithAttributeById(item.getId()).getResult())
				.filter(Objects::nonNull)
				.map(itemWithAttribute ->
				{
					IndexedItem indexedItem = indexedItemFacotry.create(itemWithAttribute.getItem(), itemWithAttribute.getItemAttribute(), new ArrayList<>());
					SearchedItem searchedItem = new SearchedItem();
					BeanUtils.copyProperties(indexedItem, searchedItem);
					ItemWithProfit itemWithProfit = itemVoPacker.packSearchedItemVo(searchedItem);
					itemWithProfit.setHighPrice(itemWithAttribute.getItem().getHighPrice());
					itemWithProfit.setLowPrice(itemWithAttribute.getItem().getLowPrice());
					itemWithProfit.setItem(itemWithAttribute.getItem());
					return itemWithProfit;
				}).collect(Collectors.toList())
		);
		if (searchedItemPaging.getData().size() != itemPaging.getData().size()) {
			log.warn("{} here is some item missed attribute in {}", LogUtil.getClassMethodName("makeAttribute"), Arrays.toString(itemPaging.getData().stream().map(Item::getId).toArray()));
		}
		return searchedItemPaging;
	}


	@GetMapping
	@RequestMapping("/item/paging")
	public Paging<ItemWithProfit> paging(
			@RequestParam(required = false) Long subStoreId,
			@RequestParam(required = false) Long shopId
			, @RequestParam(required = false) String name
			, @RequestParam(required = false) Long shopCatId
			, @RequestParam(defaultValue = "1") Integer type
			, @RequestParam(defaultValue = "0") int page
			, @RequestParam(defaultValue = "20") int pageSize) {
		CommonUser user = UserUtil.getCurrentUser();
		Optional<SubStore> aimSubStoreOpt = subStoreId == null ? Optional.empty() : Optional.ofNullable(info(null, subStoreId));
		/// 添加用户管理中的用户实体
		if (Objects.equals(shopId, null) && Objects.equals(shopId, subStoreId)) {
			log.warn("{} user:{} shopId:{} subStoreId:{}", LogUtil.getClassMethodName("forbidden-page-action"), user, shopId, subStoreId);
		}
		if (user != null) {
			try {
				if (shopId != null) {
					userRelationManager.addSubStoreMember(shopId, user.getId());
				} else {
					aimSubStoreOpt.ifPresent(subStore -> userRelationManager.addSubStoreMember(subStore.getShopId(), user.getId()));
				}
			} catch (Exception ex) {
				log.error("{} subStoreId:{} userId:{}", LogUtil.getClassMethodName("add-member-ship"), subStoreId, user.getId());
				ex.printStackTrace();
			}
		}
		ItemCriteria itemCriteria = new ItemCriteria();
		itemCriteria.setPageNo(page);
		itemCriteria.setPageSize(pageSize);
		itemCriteria.setName(name);
		itemCriteria.setStatus(1);
		itemCriteria.setType(type);
		itemCriteria.setCategoryId(shopCatId);
		if (shopId == null) {
			shopId = aimSubStoreOpt.map(SubStore::getShopId).orElse(null);
		}
		if (shopCatId != null) {
			var rCategoryItems = shopCategoryItemReadService.findByShopIdsAndShopCategoryId(Collections.singletonList(shopId), shopCatId);
			itemCriteria.setIds(rCategoryItems.getResult().stream().map(ShopCategoryItem::getItemId).collect(Collectors.toList()));
		}
		//itemCriteria.setSellOnSubStore(true);
		itemCriteria.setShopId(shopId);
		itemCriteria.setSortBy("updatedAt");
		var rPaging = itemReadService.paging(itemCriteria);
		if (!rPaging.isSuccess()) {
			log.error("[subStore](paging) item paging failed,criteria:{} cause:{}", itemCriteria, rPaging.getError());
			throw new JsonResponseException(rPaging.getError());
		}
		return packItemIntoSearchedItem(rPaging.getResult());
	}

	@PostMapping
	@RequestMapping("/item/onSell/{itemId}")
	public boolean onSell(boolean sell, @PathVariable(name = "itemId") long itemId) {
		CommonUser user = UserUtil.getCurrentUser();
		final String SELL_FLAG = "SOSS";
		var rItem = itemReadService.findById(itemId);
		if (!rItem.isSuccess()) {
			log.error("[Supplier](on-sell) fail to find item by itemId:{} cause: {}", itemId, rItem.getError());
			throw new JsonResponseException(rItem.getError());
		}
		Item item = rItem.getResult();
		if (!item.getShopId().equals(user.getShopId())) {
			throw new JsonResponseException("authorize.fail");
		}
		if (1 != (item.getStatus() & 1)) {
			throw new JsonResponseException(new Translate("商品未上架").toString());
		}
		Map<String, String> extra = item.getExtra() == null ? new HashMap<>(8) : item.getExtra();
		if (sell) {
			extra.put(SELL_FLAG, "1");
		} else {
			extra.remove(SELL_FLAG);
		}
		item.setExtra(extra);
		var rUpdate = itemWriteService.updateItem(item);
		if (rUpdate.isSuccess()) {
			return true;
		}
		log.error("[Supplier](on-sell) update item failed by itemId:{} cause: {}", itemId, rUpdate.getError());
		return false;
	}

	@GetMapping
	@RequestMapping("/profit")
	public CurrentProfitVO profit(@RequestParam(defaultValue = "28") Long sourceId) {
		/// 不需要保证门店的存在 因为是和导购员共用
		if (UserUtil.getCurrentUser() == null){
			throw new JsonResponseException("user.not.login");
		}
		long userId = Objects.requireNonNull(UserUtil.getUserId());
		var res = balanceDetailManager.getCash(userId, sourceId);
		if (!res.isSuccess()) {
			log.error("[Profit](search) find cash error cause:{}", res.getError());
			throw new JsonResponseException(res.getError());
		}
		if (CollectionUtils.isEmpty(res.getResult())) {
			return new CurrentProfitVO();
		}
		CurrentProfitVO dto = new CurrentProfitVO();
		for (InComeDetail inComeDetail : res.getResult()) {
			if (inComeDetail.isPresent()) {
				dto.setAvalidProfit(inComeDetail.getFee());
			} else {
				dto.setForecastProfit(inComeDetail.getFee());
			}
		}
		dto.setProfit(dto.getAvalidProfit() + dto.getForecastProfit());
		return dto;
	}

	/**
	 * 获取可提现金额
	 * 2019-06-12 yjp
	 */
	@GetMapping
	@RequestMapping("/profit-balance")
	public CurrentProfitVO profit_balance(@RequestParam(defaultValue = "28") Long sourceId) {
		/// 不需要保证门店的存在 因为是和导购员共用
		Long userId = UserUtil.getUserId();
		if (userId == null) {
			throw new JsonResponseException("user.not.login");
		}
		log.info("[profit_balance] userId={}", userId);
		var res = balanceDetailManager.getCash(userId, sourceId);
		log.info("[profit_balance1] res={}", res);
		if (!res.isSuccess()) {
			log.error("[Profit](search) find cash error cause:{}", res.getError());
			throw new JsonResponseException(res.getError());
		}
		if (CollectionUtils.isEmpty(res.getResult())) {
			return new CurrentProfitVO();
		}
		CurrentProfitVO dto = new CurrentProfitVO();
		for (InComeDetail inComeDetail : res.getResult()) {
			if (inComeDetail.isPresent()) {
				dto.setAvalidProfit(inComeDetail.getFee());
				if (inComeDetail.getExtra().containsKey("pw")) {
					dto.setPasswordExists(true);
				}
			} else {
				dto.setForecastProfit(inComeDetail.getFee());
			}
		}
		dto.setProfit(dto.getAvalidProfit() + dto.getForecastProfit());
		subStoreWithdrawAccountInitApp.initSubStoreWithdrawAccount(userId, sourceId);
		return dto;
	}

	@PostMapping
	@RequestMapping("/profit-withdraw")
	public boolean withDraw(@RequestParam(defaultValue = "28") Long sourceId, @RequestBody WithdrawFeeRequest fee) {
		CommonUser user = UserUtil.getCurrentUser();
		if (user == null) {
			throw new JsonResponseException("user.not.login");
		}
		try {
			if (fee.fee <= 0) {
				throw new JsonResponseException(new Translate("金额数目不对").toString());
			}
			Optional<WithdrawAccount> accountOpt = withdrawAccountReadService.findById(fee.getWithdrawAccount()).orElse(Optional.empty());
			if (accountOpt.isEmpty()) {
				throw new JsonResponseException(new Translate("请选择一个可用帐号进行提现").toString());
			}
			if (Objects.equals(accountOpt.get().getUserId(), UserUtil.getUserId())) {
				throw new JsonResponseException(new Translate("目标提现帐号不属于你").toString());
			}
			var rApply = balanceDetailManager.withdraw(user.getId(), fee.getFee(), sourceId, WithDrawProfitApply.WithdrawPaidType.from(accountOpt.get().getType()).orElse(WithDrawProfitApply.WithdrawPaidType.OTHER), accountOpt.get());
			return rApply.isSuccess();
		} catch (Exception ex) {
			ex.printStackTrace();
			log.warn("[Profit](withDraw) failed cause:{}", ex.getMessage());
			return false;
		}
	}

	/**
	 * 获取过去几天的收益汇总(排除提现影响)
	 *
	 * @param day 今天到前day天
	 * @return 汇总VO层
	 */
	@GetMapping("/profit-total")
	public BalanceDetailManager.ProfitDataVO profitTotal(@RequestParam(defaultValue = "28") Long sourceId, @RequestParam(defaultValue = "0") int day) {
		long userId = Optional.ofNullable(UserUtil.getUserId()).orElseThrow(() -> new JsonResponseException("user.not.login"));
		Date startAt =
				DateUtil.withTimeAtStartOfDay(
						Date.from(LocalDateTime.now().minusDays(day)
								.atZone(ZoneId.systemDefault())
								.toInstant()));
		Date endAt = day == 0 ? Date.from(Instant.now()) : DateUtil.withTimeAtEndOfDay(Date.from(LocalDateTime.now().minusDays(1).atZone(ZoneId.systemDefault()).toInstant()));
		if (guiderCache.findByShopIdAndUserId(sourceId, userId).isPresent()) {
			return guiderOrderSumApp.queryTheOrderSum(sourceId, userId, startAt, endAt);
		}
		Optional<BalanceDetailManager.ProfitDataVO> voOptional = balanceDetailManager.queryProfitList(userId, sourceId, startAt, Date.from(Instant.now())
				, Arrays.asList(BalanceDetail.maskBit.WithDrawRelated.getValue(), BalanceDetail.maskBit.RefundRelated.getValue()), null);
		return voOptional.orElseThrow(() -> new JsonResponseException(new Translate("获取信息失败").toString()));
	}

	/**
	 * 获取过去day天的每天收益汇总列表
	 *
	 * @param day 过去day天, 0-今日，1-昨日，7-近7日（不包含今天），30-近30日（不包含今天）
	 * @return 由`{day:汇总}`合成的map
	 */
	@GetMapping("/profit-list")
	public Map profitList(@RequestParam(defaultValue = "28") Long sourceId, @RequestParam(defaultValue = "7") int day) {
		/// LinkedHashMap来保障其day顺序
		Map<String, Object> resultMap = new LinkedHashMap<>();
		long userId = Optional.ofNullable(UserUtil.getUserId()).orElseThrow(() -> new JsonResponseException("user.not.login"));

		//导购
		var guider = guiderCache.findByShopIdAndUserId(sourceId, userId);
		if (guider.isPresent()) {
			return subStoreProfitStatisticsComponent.findStatistics(guider.get(), day);
		}

		//门店
		var subStore = subStoreCache.findByShopIdAndUserId(sourceId, userId);
		if (subStore.isPresent()) {
			return subStoreProfitStatisticsComponent.findStatistics(subStore.get(), day);
		}

		//服务商
		var serviceProvider = serviceProviderCache.findByMongo(userId, sourceId);
		if (serviceProvider != null) {
			return subStoreProfitStatisticsComponent.findStatistics(serviceProvider, day);
		}

		return Collections.emptyMap();
	}

	/**
	 * 更换新的店主
	 *
	 * @param sourceId 店铺id
	 * @param storeId  门店id
	 * @param mobile   新的店主手机号
	 * @return 是否更换成功
	 */
	@PutMapping("/{storeId}/changeOwner")
	public Response<Boolean> changeOwner(Long sourceId, @PathVariable("storeId") Long storeId, String mobile) {
		CommonUser user = UserUtil.getCurrentUser();
		if (user == null) {
			return Response.fail("user.not.login");
		}
		if (user.getShopId() == null || !Objects.equals(user.getShopId(), sourceId)) {
			return Response.fail(new Translate("你并非为该平台管理员").toString());
		}
		Response<SubStore> rSubStore = subStoreReadService.findById(storeId);
		if (!rSubStore.isSuccess()) {
			log.error("{} sourceId:{} storeId:{} mobile:{},error:{}", LogUtil.getClassMethodName(), sourceId, storeId, mobile, rSubStore.getError());
			return Response.fail(new Translate("获取门店信息失败").toString());
		}
		if (rSubStore.getResult() == null) {
			log.debug("{} sourceId:{} storeId:{}", LogUtil.getClassMethodName("not-exists"), sourceId, storeId);
			return Response.fail(new Translate("门店不存在").toString());
		}
		if (!Objects.equals(rSubStore.getResult().getShopId(), sourceId)) {
			log.error("{} sourceId:{} storeId:{}", LogUtil.getClassMethodName("not-owned"), sourceId, storeId);
			return Response.fail(new Translate("门店不属于你").toString());
		}
		User exists = userUserReadService.findByMobile(mobile).getResult();
		if (exists != null) {
			return Response.fail(new Translate("该用户已存在").toString());
		}
		OrderCriteria notCompleteOrderCriteria = new OrderCriteria();
		Set<Integer> completeStatus = Stream.of(OrderStatus.CONFIRMED, OrderStatus.SETTLED, OrderStatus.RETURN, OrderStatus.REFUND, OrderStatus.DELETED
				, OrderStatus.BUYER_CANCEL, OrderStatus.SELLER_CANCEL, OrderStatus.TIMEOUT_CANCEL).map(OrderStatus::getValue).collect(Collectors.toSet());
		notCompleteOrderCriteria.setBuyerId(rSubStore.getResult().getUserId());
		notCompleteOrderCriteria.setShopId(sourceId);
		notCompleteOrderCriteria.setStatus(Stream.of(OrderStatus.values()).map(OrderStatus::getValue).filter(v -> !completeStatus.contains(v)).collect(Collectors.toList()));
		Response<Long> rCount = shopOrderReadService.count(notCompleteOrderCriteria);
		if (!rCount.isSuccess()) {
			return Response.fail(new Translate("检测门店以往订单失败").toString());
		}
		if (rCount.getResult() > 0) {
			log.error("{} order-criteria:{} rCount:{}", LogUtil.getClassMethodName(), notCompleteOrderCriteria, rCount);
			return Response.fail(new Translate("门店购买端尚有订单未处理完毕").toString());
		}
		User ownerUpdate = new User();
		ownerUpdate.setId(rSubStore.getResult().getUserId());
		ownerUpdate.setMobile(mobile);
		Response<Boolean> rUserUpdate = userWriteService.update(ownerUpdate);
		if (!rUserUpdate.isSuccess()) {
			log.error("{} sourceId:{} storeId:{} mobile:{}", LogUtil.getClassMethodName("update-failed"), sourceId, storeId, mobile);
			return Response.fail(new Translate("用户数据更新失败").toString());
		}
		SubStore subStore = new SubStore();
		subStore.setId(rSubStore.getResult().getId());
		subStore.setMobile(mobile);
		Response<Boolean> rUpdate = subStoreWriteService.update(subStore);
		List<ShopWxa> wxaList = shopWxaReadService.findByShopId(sourceId).getResult();
		if (wxaList != null) {
			wxaList.stream().map(ShopWxa::getAppId)
					.forEach(appId ->
							userWxWriteService.removeByUserIdAndAppId(rSubStore.getResult().getUserId(), appId)
					);
		}
		if (!rUpdate.isSuccess()) {
			log.error("{} sourceId:{} storeId:{} mobile:{}", LogUtil.getClassMethodName("update-failed"), sourceId, storeId, mobile);
			return Response.fail(new Translate("更换店主失败").toString());
		}
		return rUpdate;
	}

	@GetMapping("/profit-page")
	public List<BalanceDetails.BalanceDetailVo> profitPage(@RequestParam(defaultValue = "28") Long sourceId, BalanceDetailQueryDTO balanceDetailQueryDTO) {
		BalanceDetailCriteria balanceDetailCriteria = new BalanceDetailCriteria();
		balanceDetailCriteria.setUserId(UserUtil.getUserId());
		BeanUtils.copyProperties(balanceDetailQueryDTO, balanceDetailCriteria);
		balanceDetailCriteria.setType(null);
		balanceDetailCriteria.setSourceId(sourceId);
		switch (balanceDetailQueryDTO.getType()) {
			case 1 -> /// 全部
					balanceDetailCriteria.setStatusBitMarks(Collections.singletonList(
							IsPersistAble.maskBit.PersistAble.getValue()
					));
			case 5 -> /// 退款
					balanceDetailCriteria.setStatusBitMarks(Arrays.asList(
							OutComeDetail.orderRelatedMask.ShopOrder.getValue(),
							OutComeDetail.maskBit.RefundRelated.getValue(),
							IsPersistAble.maskBit.PersistAble.getValue(),
							ProfitType.OutCome.getValue()
					));
			case 2 -> /// 已收益
					balanceDetailCriteria.setStatusBitMarks(
							Arrays.asList(
									IsPersistAble.maskBit.PersistAble.getValue(),
									IsPresent.presentMaskBit.Present.getValue(),
									ProfitType.InCome.getValue()
							));
			case 3 -> {
				/// 待收益
				balanceDetailCriteria.setStatusBitMarks(
						Arrays.asList(
								IsPersistAble.maskBit.PersistAble.getValue(),
								ProfitType.InCome.getValue(),
								OutComeDetail.orderRelatedMask.ShopOrder.getValue()
						)
				);
				balanceDetailCriteria.setNotStatusBitMarks(Collections.singletonList(IsPresent.presentMaskBit.Present.getValue()));
			}
			case 4 -> /// 提现
					balanceDetailCriteria.setStatusBitMarks(Arrays.asList(
							OutComeDetail.maskBit.WithDrawRelated.getValue(),
							IsPersistAble.maskBit.PersistAble.getValue()
					));
			default -> {

			}
		}
		return balanceDetailReadService.paging(balanceDetailCriteria.toMap()).getResult().getData().stream()
				.filter(detail -> detail.getType() != ProfitType.CacheInCome.getValue())
				.map(BalanceDetails.BalanceDetailVo::new).collect(Collectors.toList());
	}

	@RequestMapping("/import-subStore")
	public void importSubStore(@RequestParam MultipartFile file, HttpServletResponse response) {
		CommonUser user = UserUtil.getCurrentUser();
		if (user == null) {
			throw new JsonResponseException("user.not.login");
		}
		try {
			//这里设置一下让浏览器弹出下载提示框，而不是直接在浏览器中打开
			ByteArrayOutputStream excelByteOutStream = new ByteArrayOutputStream();
			OutputStream requestOutPutStream = response.getOutputStream();
			importter.mainImportSubStore(user.getShopId(), file.getInputStream(), requestOutPutStream, excelByteOutStream);
			Either<Optional<String>> fileCodeResult = insertCacheWithOutPutStream(excelByteOutStream);
			if (!fileCodeResult.isSuccess()) {
				requestOutPutStream.write(JSON.toJSONBytes(Response.fail(fileCodeResult.getErrorMsg())));
			} else {
				if (fileCodeResult.take().isPresent()) {
					requestOutPutStream.write(JSON.toJSONBytes(Response.ok(fileCodeResult.take().get())));
				}
			}
		} catch (Exception ex) {
			log.error("{} fail to import subStore for user:{}", LogUtil.getClassMethodName(), user, ex);
		}
	}

	@RequestMapping("/import-guider")
	public void importGuider(@RequestParam MultipartFile file, HttpServletResponse response) {
		CommonUser user = UserUtil.getCurrentUser();
		if (user == null) {
			throw new JsonResponseException("user.not.login");
		}
		try {
			ByteArrayOutputStream excelByteOutStream = new ByteArrayOutputStream();
			OutputStream requestOutPutStream = response.getOutputStream();
			importter.mainImportGuider(user.getShopId(), file.getInputStream(), requestOutPutStream, excelByteOutStream);
			Either<Optional<String>> fileCodeResult = insertCacheWithOutPutStream(excelByteOutStream);
			if (!fileCodeResult.isSuccess()) {
				requestOutPutStream.write(JSON.toJSONBytes(Response.fail(fileCodeResult.getErrorMsg())));
			} else {
				if (fileCodeResult.take().isPresent()) {
					requestOutPutStream.write(JSON.toJSONBytes(Response.ok(fileCodeResult.take().get())));
				}
			}
		} catch (Exception ex) {
			log.error("{} fail to import guider for user:{}", LogUtil.getClassMethodName(), user, ex);
		}
	}

	@GetMapping("/get-excel-by-code")
	public void getExcelByCode(String code, HttpServletResponse response) {
		response.setHeader("Content-Disposition", "attachment; filename=\"" + "wrongfile.xls" + "\"");
		response.setContentType("application/octet-stream;charset=utf-8");
		ByteArrayOutputStream outputStream = outputStreamCache.getIfPresent(code);
		try {
			if (outputStream == null) {
				response.getOutputStream().write(JSON.toJSONBytes(Response.fail(new Translate("数据仅可以下载一次,文件已经被删除").toString())));
			} else {
				outputStream.writeTo(response.getOutputStream());
				outputStreamCache.invalidate(code);
			}
		} catch (Exception ex) {
			log.error("{} io wrong", LogUtil.getClassMethodName(), ex);
		}
	}

	/**
	 * 获取当前用户角色（导购、门店、服务商）的收益业绩统计信息
	 *
	 * @return
	 */
	@GetMapping("/getProfitStatistics")
	public APIResp<SubStoreProfitStatisticsVO> getProfitStatistics(@RequestParam("shopId") Long shopId) {
		CommonUser user = UserUtil.getCurrentUser();
		if (user == null) {
			return APIResp.error("用户未登录");
		}

		try {
			return APIResp.ok(subStoreProfitStatisticsComponent.findStatistics(shopId, user.getId()));
		} catch (Exception ex) {
			log.error("SubStoreInfoAndProfitViewController.getProfitStatistics error, userId={} ", UserUtil.getUserId(), ex);
			return APIResp.error(ex.getMessage());
		}
	}

	private String genRandomCode() {
		long userId = UserUtil.getUserId() == null ? 6723L : UserUtil.getUserId();
		String code = (Instant.now().toEpochMilli() ^ System.currentTimeMillis()) + " " + (Instant.now().getEpochSecond() ^ userId ^ 'd');
		return Base64.getEncoder().encodeToString(code.getBytes());
	}

	private Either<Optional<String>> insertCacheWithOutPutStream(ByteArrayOutputStream excelByteOutStream) {
		String randomCode = genRandomCode();
		if (excelByteOutStream.size() > 0) {
			if (outputStreamCache.asMap().size() > 5000) {
				return (Either.error(new Translate("缓存内存空间超限，请等待3分钟").toString()));
			}
			outputStreamCache.put(randomCode, excelByteOutStream);
			return Either.ok(Optional.of(randomCode));
		}
		return Either.ok(Optional.empty());
	}

	@EqualsAndHashCode(callSuper = true)
	@Data
	public class SubStoreVo extends SubStore {
		List<Adv> advs;

		SubStoreVo(SubStore subStore) {
			BeanUtils.copyProperties(subStore, this);
			if (CollectionUtils.isEmpty(getBannerList())) {
				advs = new ArrayList<>();
			} else {
				advs = getBannerList().stream().map(advReadService::findById).map(Response::getResult).filter(Objects::nonNull)
						.collect(Collectors.toList());
			}
		}
	}
}
