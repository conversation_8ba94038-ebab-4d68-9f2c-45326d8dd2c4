<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ShopExtraUser">

    <resultMap id="ShopExtraUserMap" type="ShopExtraUser">
        <id property="id" column="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="api_id" property="apiId"/>
        <result column="api_secret" property="apiSecret"/>
        <result column="status" property="status"/>
        <result column="extra" property="extraJson"/>
        <result column="version" property="version"/>
    </resultMap>

    <sql id="tb">
        parana_shop_extra_user
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        shop_id,api_id,api_secret,status,extra,version
    </sql>

    <sql id="vals">
        #{shopId}, #{apiId}, #{apiSecret}, #{status}, #{extraJson},#{version}
    </sql>


    <sql id="criteria">
        <if test="ids != null">AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="skuId!=null">AND sku_id = #{skuId}</if>
        <if test="apiId!=null">AND api_id = #{apiId}</if>
        <if test="apiSecret != null">AND api_secret = #{apiSecret}</if>
        <if test="version != null">AND version = #{version}</if>
        <if test="status != null">AND status = #{status}</if>
        <if test="statuses != null">
            and status in
            <foreach collection="statuses" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'shopId'">ORDER BY shop_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'apiId'">ORDER BY api_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'apiSecret'">ORDER BY api_secret
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'version'">ORDER BY version
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>
    <insert id="create" parameterType="ShopExtraUser" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.shopId}, #{i.apiId}, #{i.apiSecret}, #{i.status}, #{i.extraJson}, #{i.version}
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="ShopExtraUserMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="ShopExtraUserMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByApiId" parameterType="long" resultMap="ShopExtraUserMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE api_id = #{apiId}
    </select>

    <select id="findNormalByApiId" parameterType="long" resultMap="ShopExtraUserMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE api_id = #{apiId} and status != -1
    </select>
    <select id="findByShopId" parameterType="long" resultMap="ShopExtraUserMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId}
    </select>

    <select id="findNormalByShopId" parameterType="long" resultMap="ShopExtraUserMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id = #{shopId} and status != -1
    </select>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>


    <update id="update" parameterType="ShopExtraUser">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="apiId != null">api_id = #{apiId},</if>
            <if test="apiSecret != null">api_secret = #{apiSecret},</if>
            <if test="extraJson !=null">extra = #{extraJson},</if>
            <if test="status!=null">status=#{status},</if>
            <if test="version!=null">version=#{version},</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateStatusById" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}
        WHERE id = #{id}
    </update>

    <update id="updateStatusByIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateStatusByShopIds" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}
        WHERE shop_id IN
        <foreach item="shopId" collection="shopIds" open="(" separator="," close=")">
            #{shopId}
        </foreach>
        and `status` != -1
    </update>

    <update id="deletes" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET status=-1
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="ShopExtraUserMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>

</mapper>
