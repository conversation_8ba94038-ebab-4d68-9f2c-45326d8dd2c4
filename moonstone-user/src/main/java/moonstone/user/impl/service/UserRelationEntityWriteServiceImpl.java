package moonstone.user.impl.service;

import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.user.impl.dao.UserRelationEntityDao;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserRelationEntityWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@RpcProvider
@Slf4j
@Service
public class UserRelationEntityWriteServiceImpl implements UserRelationEntityWriteService {
    @Autowired
    private UserRelationEntityDao userRelationEntityDao;

    @Override
    public Response<Long> create(UserRelationEntity entity) {
        try {
            if (userRelationEntityDao.create(entity)) {
                return Response.ok(entity.getId());
            } else {
                return Response.fail("fail.create.user.relation.entity");
            }
        } catch (Exception ex) {
            log.error("{} fail to create entity :{}", LogUtil.getClassMethodName(), entity, ex);
            return Response.fail("fail.create.user.relation.entity");
        }
    }

    @Override
    public Response<Boolean> update(UserRelationEntity entity) {
        try {
            return Response.ok(userRelationEntityDao.update(entity));
        } catch (Exception ex) {
            log.error("{} fail to update entity :{}", LogUtil.getClassMethodName(), entity, ex);
            ex.printStackTrace();
            return Response.fail("fail.update.user.relation.entity");
        }
    }

    @Override
    public Response<Boolean> createWhenNotExists(Long userId, Long shopId, Long relationIdA, Long relationIdB, UserRelationEntity.UserRelationType type) {
        try {
            return Response.ok(userRelationEntityDao.createWhenNotExists(userId, shopId, relationIdA, relationIdB, type.getType()));
        } catch (Exception ex) {
            log.error("{} fail to init userId:{} shopId:{} relationIdA:{} relationIdB:{} type:{}", LogUtil.getClassMethodName(), userId, shopId, relationIdA, relationIdB, type, ex);
            return Response.fail("fail.update.user.relation.entity");
        }
    }

    @Override
    public Response<Boolean> updateOrCreate(Long userId, Long shopId, Long relationIdA, Long relationIdB, UserRelationEntity.UserRelationType type) {
        try {
            createWhenNotExists(userId, shopId, relationIdA, relationIdB, type);
            return Response.ok(userRelationEntityDao.updateByUserIdAndShopIdAndType(userId, shopId, relationIdA, relationIdB, type.getType()));
        } catch (Exception ex) {
            log.error("{} fail to update userId:{} shopId:{} relationIdA:{} relationIdB:{} type:{}", LogUtil.getClassMethodName(), userId, shopId, relationIdA, relationIdB, type, ex);
            return Response.fail("fail.create.user.relation.entity");
        }
    }

    @Override
    public Response<Boolean> deleteByUserId(Long userId) {
        try {
            userRelationEntityDao.deleteByUserId(userId);
            return Response.ok(true);
        } catch (Exception ex) {
            log.error("{} fail to delete relation by userId[{}]", LogUtil.getClassMethodName(), userId, ex);
            return Response.fail("fail.delete.user.relation.entity");
        }
    }

    @Override
    public Response<Boolean> deleteById(Long id) {
        try {
            return Response.ok(userRelationEntityDao.delete(id));
        } catch (Exception ex) {
            log.error("UserRelationEntityWriteServiceImpl.deleteById", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> delete(Long userId, Long shopId, UserRelationEntity.UserRelationType type) {
        try {
            return Response.ok(userRelationEntityDao.deleteByUserIdAndShopIdAndType(userId, shopId, type.getType()));
        } catch (Exception ex) {
            log.error("UserRelationEntityWriteServiceImpl.delete error", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Long> create(Long userId, Long relationId, UserRelationEntity.UserRelationType type, Long additionRelationA) {
        try {
            UserRelationEntity newRelation = new UserRelationEntity();
            newRelation.setUserId(userId);
            newRelation.setRelationId(relationId);
            newRelation.setType(type.getType());
            newRelation.setCreatedAt(new Date());
            newRelation.setUpdatedAt(newRelation.getCreatedAt());
            newRelation.setAdditionRelationA(additionRelationA);

            return create(newRelation);
        } catch (Exception ex) {
            log.error("UserRelationEntityWriteServiceImpl.create", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Override
    public Response<Boolean> updateParent(Long userId, Long shopId, Long newParentId) {
        try {
            UserRelationEntity parameter = new UserRelationEntity();
            parameter.setUserId(userId);
            parameter.setAdditionRelationA(shopId);
            parameter.setType(UserRelationEntity.UserRelationType.SUPER.getType());
            parameter.setRelationId(newParentId);

            return Response.ok(userRelationEntityDao.updateRelationId(parameter));
        } catch (Exception ex) {
            log.error("UserRelationEntityWriteServiceImpl.updateParent", ex);
            return Response.fail(ex.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<Boolean> creates(List<UserRelationEntity> list) {
        if (!userRelationEntityDao.creates(list).equals(list.size())) {
            throw new RuntimeException("创建失败");
        }

        return Response.ok(true);
    }
}
