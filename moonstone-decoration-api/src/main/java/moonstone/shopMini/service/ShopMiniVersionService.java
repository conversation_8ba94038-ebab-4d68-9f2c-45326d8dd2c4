package moonstone.shopMini.service;

import io.terminus.common.model.Paging;
import moonstone.shopMini.model.ShopMiniVersion;

import java.util.List;
import java.util.Map;

/**
 * author：书生
 */
public interface ShopMiniVersionService {
    /**
     * 保存店铺小程序迭代版本信息
     * @param shopMiniVersion 店铺小程序迭代版本信息
     */
    void save(ShopMiniVersion shopMiniVersion);

    /**
     * 根据查询条件获取店铺小程序迭代版本信息
     * @param query 查询条件
     * @return 店铺小程序迭代版本信息
     */
    ShopMiniVersion getOne(Map<String, Object> query);

    /**
     * 更新店铺小程序迭代版本信息
     * @param shopMiniVersion 店铺小程序迭代版本信息
     */
    void update(ShopMiniVersion shopMiniVersion);

    /**
     * 根据查询条件分页查询店铺小程序迭代版本信息列表
     * @param query 查询条件
     * @return 店铺小程序迭代版本信息列表
     */
    Paging<ShopMiniVersion> pages(Map<String, Object> query);
    /**
     * 根据查询条件获取店铺小程序迭代版本信息列表
     * @param query 查询条件
     * @return 店铺小程序迭代版本信息列表
     */
    List<ShopMiniVersion> list(Map<String, Object> query);

}
