package moonstone.adv.dto;

import lombok.Getter;
import lombok.Setter;
import moonstone.adv.model.Adv;
import moonstone.common.utils.StringUtils;

/**
 * Created by CaiZhy on 2018/10/9.
 */
public class FullAdv extends Adv {

    @Getter
    @Setter
    private String columnName;//广告位名称

    public String validate() {
        if(StringUtils.isBlank(getUrl())){
            return "地址未填写";
        }
        if(getColumnId()<1){
            return "请选择广告位";
        }
        return null;
    }
}
