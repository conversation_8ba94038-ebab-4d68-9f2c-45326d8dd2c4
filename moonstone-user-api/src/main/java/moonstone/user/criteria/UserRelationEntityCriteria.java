package moonstone.user.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserRelationEntityCriteria extends PagingCriteria {
    List<Long> userIds;
    List<Long> relationIds;
    Long userId;
    Long relationId;
    Integer type;
    Integer status;
    Date createdFrom;
    Date createdTo;
    String mobile;
    String name;
    List<Integer> statuses;
    List<Integer> maskbit;
    List<Integer> notMaskBit;
    List<Long> relationIdA;
    Long relationIdB;

    String sortBy;
    /**
     * 注册开始时间
     */
    Long registrationTimestampStart;
    /**
     * 注册结束时间
     */
    Long registrationTimestampEnd;
}
