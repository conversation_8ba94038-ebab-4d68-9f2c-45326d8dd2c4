package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.WithdrawPaymentReceiver;
import org.springframework.stereotype.Repository;

@Repository
public class WithdrawPaymentReceiverDao extends MyBatisDao<WithdrawPaymentReceiver> {

    public int insertSelective(WithdrawPaymentReceiver parameter) {
        return getSqlSession().insert(sqlId("insertSelective"), parameter);
    }

    public WithdrawPaymentReceiver findByWithdrawPaymentId(Long withdrawPaymentId) {
        return getSqlSession().selectOne(sqlId("findByWithdrawPaymentId"), ImmutableMap.of("withdrawPaymentId", withdrawPaymentId));
    }
}
