/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.api;

import moonstone.promotion.model.Promotion;

import java.util.List;

/**
 * 选择默认的营销活动
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-06-21
 */
public interface PromotionPicker {

    /**
     * 选择默认的sku级别的营销活动
     *
     * @param promotions   sku营销活动列表
     * @return  默认的营销活动
     */
    Long defaultSkuPromoiton(List<Promotion> promotions);


    /**
     * 选择默认的店铺级别的营销活动
     *
     * @param promotions   店铺营销活动列表
     * @return 默认的营销活动
     */
    Long defaultShopPromotion(List<Promotion> promotions);


    /**
     * 选择默认的店铺级别的运费营销活动
     *
     * @param promotions   店铺运费营销活动列表
     * @return 默认的运费营销活动
     */
    Long defaultShipmentPromotion(List<Promotion> promotions);


    /**
     * 选择默认的支付级别的营销活动
     *
     * @param promotions   支付营销活动列表
     * @return 默认的营销活动
     */
    Long defaultGlobalPromotion(List<Promotion> promotions);
}
