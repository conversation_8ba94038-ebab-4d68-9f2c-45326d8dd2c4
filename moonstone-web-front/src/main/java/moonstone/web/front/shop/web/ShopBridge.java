package moonstone.web.front.shop.web;

import blue.sea.moonstone.bridge.app.HttpServletVerticle;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import moonstone.shop.model.Shop;
import moonstone.shop.model.ShopPayInfo;
import moonstone.web.core.bridge.vertx.model.BridgeInject;
import moonstone.web.core.component.ParanaUserWrapper;
import moonstone.web.front.shop.ShopPayInfos;
import moonstone.web.front.shop.Shops;
import org.springframework.stereotype.Component;

@AllArgsConstructor
@Component
@Slf4j
@Getter
public class ShopBridge extends HttpServletVerticle implements BridgeInject {
    Shops shops;
    ShopPayInfos shopPayInfos;
    ParanaUserWrapper paranaUserWrapper;

    @Override
    public void start() throws Exception {
        serve("shop.show", json(Shop.class,
                (shop, user) -> shops.findShopById(shop.getId())));
        serve("shop.current", json((request, user) -> shops.findShopById(user.getShopId())));
        serve("shop.pay.create", json(ShopPayInfo.class,
                (payInfo, user) -> shopPayInfos.createShopPayInfo(payInfo)));
        serve("shop.pay.delete", json(ShopPayInfo.class,
                (payInfo, user) -> shopPayInfos.deleteShopPayInfoById(payInfo.getId())));
        serve("shop.pay.list", json((request, user) -> result(shopPayInfos.findShopPayInfoByShopIdNew())));
        //serve("shop.pay.uploadCert", )
    }
}