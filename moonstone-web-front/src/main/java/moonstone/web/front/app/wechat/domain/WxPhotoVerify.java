package moonstone.web.front.app.wechat.domain;

import lombok.Data;

import java.time.LocalDate;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class WxPhotoVerify {
    Type type;
    String name;
    String code;
    LocalDate validUntil;
    Integer validYear;

    enum Type {
        /**
         * 身份证方向
         */
        Front, Back, Unknown
    }

    public void setType(String type) {
        if (Objects.isNull(type) || type.isEmpty()) {
            this.type = null;
            return;
        }
        try {
            this.type = Type.valueOf(type);
        } catch (Exception ignore) {
            this.type = Type.Unknown;
        }
    }
}
