/*
 * Copyright (c) 2014 杭州端点网络科技有限公司
 */

package moonstone.cart.service;

import io.terminus.common.model.Response;
import moonstone.cart.model.CartItem;
import moonstone.common.model.Either;

import java.util.List;

/**
 * Copyright (c) 2015 杭州端点网络科技有限公司
 * Date: 3/10/16
 * Time: 4:49 PM
 * Author: 2015年 <a href="mailto:<EMAIL>">张成栋</a>
 */
public interface CartReadService {
    /**
     * 查询当前登录用户购物车（某店铺）的sku的总数
     * @param userId 当前登录用户
     * @param shopId 店铺id
     */
    Response<Integer> count(Long userId, Long shopId);

    /**
     * 列出用户的所有购物车商品记录
     * @param buyerId    买家id
     * @return 购物车商品记录
     */
    Response<List<CartItem>> findByUserId(Long buyerId);

    /**
     * 列出用户某店铺的所有购物车商品记录
     * @param buyerId    买家id
     * @param shopId     店铺id
     * @return 购物车商品记录
     */
    Response<List<CartItem>> findByUserIdAndShopId(Long buyerId, Long shopId);

    Response<List<CartItem>> findByUserIdAndSkuId(Long buyerId, Long skuId);

    Either<CartItem> findById(Long id);

    Either<List<CartItem>> findCartsByIds(List<Long> ids);
}
