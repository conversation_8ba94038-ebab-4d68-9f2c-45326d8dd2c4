package moonstone.web.core.user.service;

import io.terminus.common.model.Paging;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.utils.Translate;
import moonstone.shop.model.Shop;
import moonstone.shop.model.SubStore;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.shop.service.SubStoreReadService;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.user.criteria.UserRelationEntityCriteria;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.model.view.UserLevelView;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.web.core.user.api.SubStoreRoleServiceApi;
import moonstone.web.core.user.view.Who;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class SubStoreRoleService extends AbstractVerticle implements SubStoreRoleServiceApi {
    @Autowired
    ShopCacheHolder shopCacheHolder;
    @Autowired
    UserRelationEntityReadService userRelationEntityReadService;
    @Autowired
    SubStoreReadService subStoreReadService;
    @Autowired
    SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;

    @Override
    public void start() throws Exception {
        vertx.eventBus().<JsonObject>localConsumer(SubStoreRoleServiceApi.getAddress(ACTION.QUERY))
                .handler(query ->
                        queryRole(query.body().getLong("userId"), query.body().getLong("shopId"))
                                .onSuccess(who -> query.reply(JsonObject.mapFrom(who)))
                                .onFailure(e -> query.fail(-1, e.getMessage()))
                );
    }

    public Future<Who> queryRole(Long userId, Long shopId) {
        return vertx.executeBlocking(promise -> {
            // 1. check if is platform
            if (checkIfPlatform(userId, shopId, promise)) {
                return;
            }
            // 2. check if is service provider
            if (checkIfServiceProvider(userId, shopId, promise)) {
                return;
            }
            // 3. check if is subStore
            if (checkIfSubStore(userId, shopId, promise)) {
                return;
            }
            // 4. check if is the subStoreGuider
            if (checkIfGuider(userId, shopId, promise)) {
                return;
            }
            promise.complete(new Who());
        });
    }

    private boolean checkIfServiceProvider(Long userId, Long shopId, Promise<Who> promise) {
        Shop shop = shopCacheHolder.findShopById(shopId);
        UserRelationEntityCriteria criteria = new UserRelationEntityCriteria();
        criteria.setUserId(userId);
        criteria.setRelationId(shop.getUserId());
        criteria.setType(UserRelationEntity.UserRelationType.SUPER.getType());
        List<UserLevelView> viewList = Optional.ofNullable(userRelationEntityReadService.paging(criteria).getResult())
                .map(Paging::getData).orElse(Collections.emptyList()).stream().map(UserLevelView::new).collect(Collectors.toList());
        Optional<UserLevelView> userLevelView = viewList.stream()
                .filter(view -> Objects.equals(view.getShopId(), shopId))
                .findFirst();
        userLevelView.ifPresent(view -> promise.tryComplete(Who.buildServiceProvider(view)));
        return userLevelView.isPresent();
    }

    private boolean checkIfGuider(Long userId, Long shopId, Promise<Who> promise) {
        SubStoreTStoreGuider guider = Optional.ofNullable(subStoreTStoreGuiderReadService.findByStoreGuiderUserId(userId, shopId)
                .getResult())
                .filter(list -> !list.isEmpty())
                .map(onlyOne -> onlyOne.get(0))
                .orElse(null);
        if (guider == null) {
            return false;
        }
        promise.tryComplete(Who.buildGuider(guider));
        return true;
    }

    private boolean checkIfSubStore(Long userId, Long shopId, Promise<Who> promise) {
        SubStore subStore = subStoreReadService.findUserIdAndShopId(userId, shopId).getResult();
        if (subStore != null) {
            promise.complete(Who.buildSubStore(subStore));
            return true;
        }
        return false;
    }

    private boolean checkIfPlatform(Long userId, Long shopId, Promise<Who> promise) {
        Shop shop = shopCacheHolder.findShopById(shopId);
        if (shop == null) {
            return promise.tryFail(Translate.of("店铺不存在"));

        }
        if (shop.getUserId().equals(userId)) {
            return promise.tryComplete(Who.buildShop(shop.getName(), userId, shopId));
        }
        return false;
    }
}
