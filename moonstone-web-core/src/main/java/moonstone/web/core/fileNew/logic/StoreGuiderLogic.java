package moonstone.web.core.fileNew.logic;

import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.fileNew.dto.StoreGuiderChangeSubStore;
import moonstone.web.core.user.UserRelationManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * author：书生
 */
@Service
@Slf4j
public class StoreGuiderLogic {

    @Resource
    private UserRelationManager userRelationManager;

    /**
     * 修改导购上级门店
     *
     * @param req 请求参数
     * @return 是否修改成功
     */
    public Boolean changeSubStore(StoreGuiderChangeSubStore req) {
        return userRelationManager.changeSubStore(req.getSubStoreStoreGuiderId(), req.getNewSubStorePhone());
    }
}
