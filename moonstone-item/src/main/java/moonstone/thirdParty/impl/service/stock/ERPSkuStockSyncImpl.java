package moonstone.thirdParty.impl.service.stock;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Y800PlatformAPI;
import moonstone.common.api.remote.RemoteAPI;
import moonstone.common.constants.Y800ServiceName;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.event.EnvEvent;
import moonstone.common.event.ForceNewVersionByShopIdNotifyEvent;
import moonstone.common.model.Either;
import moonstone.common.model.Y800OpenRequest;
import moonstone.common.model.Y800ResponseModel;
import moonstone.common.model.rpcAPI.y800dto.*;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.NumberUtil;
import moonstone.common.utils.Translate;
import moonstone.event.MethodSwitchNotify;
import moonstone.event.ThirdPartyLogLevelChangeEvent;
import moonstone.thirdParty.impl.dao.ThirdPartySkuDao;
import moonstone.thirdParty.impl.dao.ThirdPartySkuShopDao;
import moonstone.thirdParty.impl.dao.ThirdPartySkuStockDao;
import moonstone.thirdParty.impl.dao.ThirdPartyUserShopDao;
import moonstone.thirdParty.model.ThirdPartySku;
import moonstone.thirdParty.model.ThirdPartySkuShop;
import moonstone.thirdParty.model.ThirdPartySkuStock;
import moonstone.thirdParty.model.ThirdPartyUserShop;
import moonstone.thirdParty.service.stock.StockSyncTrait;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class ERPSkuStockSyncImpl implements StockSyncTrait {

    @Value("${Y800.open.api.gate}")
    String y800OpenApiGate;
    @Value("${Y800.partnerCode}")
    String y800PartnerCode;
    @Value("${Y800.partnerKey}")
    String y800PartnerKey;

    @Autowired
    ThirdPartySkuStockDao thirdPartySkuStockDao;
    @Autowired
    ThirdPartySkuDao thirdPartySkuDao;
    @Autowired
    ThirdPartyUserShopDao thirdPartyUserShopDao;
    @Autowired
    ThirdPartySkuShopDao thirdPartySkuShopDao;
    @Autowired
    Y800V1SkuSync y800V1SkuSync;
    @RemoteAPI
    Y800PlatformAPI y800PlatformAPI;
    // 利用信号解偶
    private boolean logTheOutSystemAPIInvoke = false;
    private boolean onlineEnv;
    // 不需要考虑并发问题,无并发修改场景无需担忧,操作上应禁止并发修改
    private final Map<String, String> methodVersion = new HashMap<>();
    // 强制使用新平台的控件
    private final Map<Long, Boolean> forceNewVersionSet = new HashMap<>();

    @Override
    public ThirdPartySystem syncSystem() {
        return ThirdPartySystem.Y800_V2;
    }

    @PostConstruct
    void postConstruct() {
        logTheOutSystemAPIInvoke = !onlineEnv;
    }


    /**
     * 同步Y800商品,其中包括老系统兼容
     *
     * @param thirdPartySystem   第三方系统类型
     * @param thirdPartyUserShop 第三方系统用户依据
     */
    @Override
    public void synchronize(ThirdPartySystem thirdPartySystem, ThirdPartyUserShop thirdPartyUserShop) {
        try {
            log.debug("{} forceNewVersionSet [{}] at system [{}] userShop [{}] with methodVersion [{}]", LogUtil.getClassMethodName(), forceNewVersionSet, thirdPartySystem, thirdPartyUserShop, methodVersion);
            boolean newPlatform = forceNewVersionSet.getOrDefault(thirdPartyUserShop.getShopId(), "new".equals(methodVersion.getOrDefault("synchronize", "new")));
            if (!newPlatform) {
                // 如果老接口使用的数据量为0 则使用新平台接口
                if (syncByOldY800WDPlatform(thirdPartySystem, thirdPartyUserShop)) {
                    return;
                }
            }

            List<Y800ThirdSkuDataDTO> thirdPartySkuList = queryAllThirdSku(thirdPartyUserShop);

            log.debug("{} we got skus size:{} for ShopId:{}", LogUtil.getClassMethodName(), thirdPartySkuList.size(), thirdPartyUserShop.getShopId());

            List<ThirdPartySkuStock> thirdPartySkuStocks = mergeAllStock(thirdPartyUserShop, thirdPartySkuList);

            log.debug("{} we got skuStocks size:{} for ShopId:{}", LogUtil.getClassMethodName(), thirdPartySkuStocks.size(), thirdPartyUserShop.getShopId());

            //把所有该店铺该平台的都假删，然后遍历存数据
            List<ThirdPartySkuShop> thirdPartySkuShops = thirdPartySkuShopDao.findByThirdPartyIdAndShopId(thirdPartySystem.Id(), thirdPartyUserShop.getShopId());
            for (ThirdPartySkuShop thirdPartySkuShop : thirdPartySkuShops) {
                thirdPartySkuStockDao.updateStatusByThirdPartyAndSkuId(thirdPartySkuShop.getShopId(), thirdPartySkuShop.getThirdPartyId(), thirdPartySkuShop.getOuterSkuId(), -1);
                thirdPartySkuDao.updateStatusByThirdPartyAndSkuId(thirdPartySkuShop.getThirdPartyId(), thirdPartySkuShop.getOuterSkuId(), -1);
            }
            thirdPartySkuShopDao.updateStatusByThirdPartyIdAndShopId(thirdPartySystem.Id(), thirdPartyUserShop.getShopId(), -1);


            for (ThirdPartySkuStock thirdPartySkuStock : thirdPartySkuStocks) {
                //同步ThirdPartySkuShop相关数据
                ThirdPartySkuShop thirdPartySkuShop = thirdPartySkuShopDao.findByThreeId(thirdPartySystem.Id(), thirdPartyUserShop.getShopId(), thirdPartySkuStock.getOuterSkuId());
                if (thirdPartySkuShop != null) {
                    thirdPartySkuShopDao.updateStatus(thirdPartySkuShop.getId(), 1);
                } else {
                    thirdPartySkuShop = createThirdPartySkuShop(thirdPartySystem, thirdPartyUserShop.getShopId(), thirdPartySkuStock);
                }

                //同步ThirdPartySku相关数据
                ThirdPartySku skuExist = thirdPartySkuDao.findByThirdPartyAndSkuId(thirdPartySystem.Id(), thirdPartySkuStock.getOuterSkuId());
                if (skuExist != null) {
                    thirdPartySkuDao.updateStatus(skuExist.getId(), 1);
                } else {
                    skuExist = createSku(thirdPartySystem, thirdPartySkuStock);
                }

                //同步ThirdPartySkuStock相关数据
                ThirdPartySkuStock stockExist = thirdPartySkuStockDao.findByParams(thirdPartySkuShop.getShopId(),
                        thirdPartySystem.Id(), thirdPartySkuStock.getOuterSkuId(),
                        thirdPartySkuStock.getDepotCode(), thirdPartySkuStock.getBatch()
                );
                try {
                    Optional.ofNullable(thirdPartyUserShopDao.findByThirdPartyIdAndShopId(thirdPartySkuShop.getShopId(), thirdPartySkuShop.getThirdPartyId()))
                            .map(ThirdPartyUserShop::getThirdPartyCode)
                            .ifPresent(y800PlatformAPI::setAccessCode);
                    y800PlatformAPI.goodsQuery(new ListDTO<>(Collections.singletonList(new Y800SkuCodeDTO(thirdPartySkuStock.getOuterSkuId()))))
                            .map(List::stream)
                            .map(Stream::findFirst)
                            .orElse(Optional.empty())
                            .ifPresent(data -> {
                                thirdPartySkuStock.setDepotCode(data.getDepotCode());
                                thirdPartySkuStock.setDepotName(data.getDepotName());
                                if (thirdPartySkuStock.getVolume() == null) {
                                    NumberUtil.parseNumber(data.getVolume(), BigDecimal.class)
                                            .ifSuccess(thirdPartySkuStock::setVolume);
                                }
                                NumberUtil.parseNumber(data.getWeight(), BigDecimal.class)
                                        .ifSuccess(thirdPartySkuStock::setWeight);
                                Optional.ofNullable(data.getSupplierName())
                                        .ifPresent(thirdPartySkuStock::setSupplierName);
                            });

                } catch (Exception ex) {
                    log.error("{} fuck Y800 outSkuId:{}", LogUtil.getClassMethodName(), thirdPartySkuStock.getOuterSkuId(), ex);
                }
                if (stockExist != null) {
                    thirdPartySkuStock.setId(stockExist.getId());
                    thirdPartySkuStock.setThirdPartyId(stockExist.getThirdPartyId());
                    thirdPartySkuStock.setStatus(1);
                    thirdPartySkuStockDao.update(thirdPartySkuStock);
                } else {
                    thirdPartySkuStock.setThirdPartyId(thirdPartySystem.Id());
                    thirdPartySkuStock.setStatus(1);
                    thirdPartySkuStockDao.create(thirdPartySkuStock);
                }
            }
            log.debug("synchronize success, shopId={}", thirdPartyUserShop.getShopId());
        } catch (Exception ex) {
            log.error("{} sync shopId:{}", LogUtil.getClassMethodName(), thirdPartyUserShop.getShopId(), ex);
            if (!syncByOldY800WDPlatform(thirdPartySystem, thirdPartyUserShop)) {
                throw ex;
            }
            if (forceNewVersionSet.getOrDefault(thirdPartyUserShop.getShopId(), false)) {
                throw ex;
            }
        }
    }

    private ThirdPartySku createSku(ThirdPartySystem thirdPartySystem, ThirdPartySkuStock thirdPartySkuStock) {
        ThirdPartySku newSku = new ThirdPartySku();
        newSku.setThirdPartyId(thirdPartySystem.Id());
        newSku.setOuterSkuId(thirdPartySkuStock.getOuterSkuId());
        newSku.setOuterSkuName(thirdPartySkuStock.getOuterSkuName());
        newSku.setStatus(1);
        thirdPartySkuDao.create(newSku);
        return newSku;
    }

    private List<ThirdPartySkuStock> mergeAllStock(ThirdPartyUserShop thirdPartyUserShop, List<Y800ThirdSkuDataDTO> thirdPartySkuList) {
        List<ThirdPartySkuStock> thirdPartySkuStocks = new LinkedList<>();
        Map<String, Y800ThirdSkuDataDTO> codeMapSku = new HashMap<>();
        for (Y800ThirdSkuDataDTO skuData : thirdPartySkuList) {
            codeMapSku.put(skuData.getCode(), skuData);
        }
        final int PAGE_SIZE = onlineEnv ? 300 : 1;
        // 1024 page
        for (int i = 0; i * PAGE_SIZE < thirdPartySkuList.size(); i++) {
            int max = Math.min((i + 1) * PAGE_SIZE, thirdPartySkuList.size());
            try {
                mergeStock(thirdPartyUserShop.getShopId(), codeMapSku, thirdPartySkuStocks, queryStockByThirdSkuData(thirdPartySkuList.subList(i * PAGE_SIZE, max), thirdPartyUserShop));
            }
            catch (Exception stockSync){
                log.error("Fail to Stock Sync For Shop[{}]", thirdPartyUserShop.getShopId(), stockSync);
            }
        }
        return thirdPartySkuStocks;
    }

    private List<Y800ThirdSkuDataDTO> queryAllThirdSku(ThirdPartyUserShop thirdPartyUserShop) {
        List<Y800ThirdSkuDataDTO> thirdPartySkuList = new LinkedList<>();
        Page<Y800ThirdSkuDataDTO> skuPage = null;
        Pageable pageAble = PageRequest.of(1, 50);
        while (skuPage == null || !skuPage.getContent().isEmpty()) {
            skuPage = queryThirdSkuByPage(pageAble, thirdPartyUserShop).logException(ex -> log.error("{} failed ex:", LogUtil.getClassMethodName(), ex)).elseThrow(() -> new RuntimeException("查询失败"));
            if (skuPage != null) {
                thirdPartySkuList.addAll(skuPage.getContent());
                if (!onlineEnv) {
                    log.debug("{} list:{}", LogUtil.getClassMethodName(), JSON.toJSONString(thirdPartySkuList));
                }
                if (skuPage.getContent().size() < 50) {
                    break;
                }
            } else {
                break;
            }
            pageAble = pageAble.next();
        }
        return thirdPartySkuList;
    }

    private ThirdPartySkuShop createThirdPartySkuShop(ThirdPartySystem thirdPartySystem, Long shopId, ThirdPartySkuStock thirdPartySkuStock) {
        ThirdPartySkuShop thirdPartySkuShop = new ThirdPartySkuShop();
        thirdPartySkuShop.setThirdPartyId(thirdPartySystem.Id());
        thirdPartySkuShop.setShopId(shopId);
        thirdPartySkuShop.setOuterSkuId(thirdPartySkuStock.getOuterSkuId());
        thirdPartySkuShop.setOuterSkuName(thirdPartySkuStock.getOuterSkuName());
        thirdPartySkuShop.setStatus(1);
        thirdPartySkuShopDao.create(thirdPartySkuShop);
        return thirdPartySkuShop;
    }

    private boolean syncByOldY800WDPlatform(ThirdPartySystem thirdPartySystem, ThirdPartyUserShop thirdPartyUserShop) {
        try {
            y800V1SkuSync.synchronize(thirdPartySystem, thirdPartyUserShop);
            return true;
        } catch (Exception ex) {
            log.error("{} fail to synchronize the sku from thirdPartySystem[{}] userShop [{}]", LogUtil.getClassMethodName(), thirdPartySystem, thirdPartyUserShop, ex);
            return false;
        }
    }

    /**
     * 查询仓储库存
     *
     * @param list               商品代码
     * @param thirdPartyUserShop 数据
     * @return 库存数据
     */
    private Either<List<Y800SkuStockDataContainDTO>> queryStockByThirdSkuData(List<Y800ThirdSkuDataDTO> list, ThirdPartyUserShop thirdPartyUserShop) {
        StockQueryBiz stockQueryBiz = new StockQueryBiz();
        stockQueryBiz.setSkuCode(list.stream().map(Y800ThirdSkuDataDTO::getCode).collect(Collectors.toList()));
        stockQueryBiz.setAccessCode(thirdPartyUserShop.getThirdPartyCode());

        Y800OpenRequest openRequest = new Y800OpenRequest();
        openRequest.setPartnerId(y800PartnerCode);
        openRequest.setBizData(stockQueryBiz);
        openRequest.setServiceName(Y800ServiceName.GoodsDepotSearchList.getServiceName());
        openRequest.sign(y800PartnerKey);
        logTheApiInvoke(LogUtil.getClassMethodName(), openRequest, stockQueryBiz);
        HttpRequest apiRequest = HttpRequest.post(y800OpenApiGate)
                .contentType(HttpRequest.CONTENT_TYPE_FORM)
                .form(openRequest.toMap());
        String rawBody = "HTTP-FAIL";
        try {
            if (apiRequest.ok()) {
                rawBody = apiRequest.body();
                log.debug("{} OMS stock sync result [{}]", LogUtil.getClassMethodName(), rawBody);
                Y800ResponseModel<List<Y800SkuStockDataContainDTO>> apiResponse = JSON.parseObject(rawBody, new TypeReference<Y800ResponseModel<List<Y800SkuStockDataContainDTO>>>() {
                }.getType());
                return Either.ok(apiResponse.getData());
            }
            throw new RuntimeException(new Translate("Http请求错误 m:%s b:%s", apiRequest.message(), apiRequest.body()).toString());
        } catch (Exception ex) {
            log.error("{} url:{} error:{} res:{} request-raw:{}", LogUtil.getClassMethodName(), y800OpenApiGate, ex.getMessage(), rawBody, openRequest.toMap(), ex);
            return Either.error(ex);
        }
    }


    @org.springframework.context.event.EventListener(EnvEvent.class)
    void envChange(EnvEvent envEvent) {
        onlineEnv = envEvent.online();
    }

    @org.springframework.context.event.EventListener(ThirdPartyLogLevelChangeEvent.class)
    public void changeTheLogLevel(ThirdPartyLogLevelChangeEvent event) {
        logTheOutSystemAPIInvoke = event.isLogAPIInvoke();
    }

    /**
     * 版本切换
     *
     * @param methodSwitchNotify 版本切换通知
     */
    @org.springframework.context.event.EventListener(MethodSwitchNotify.class)
    public void changeMethodVersion(MethodSwitchNotify methodSwitchNotify) {
        String className = this.getClass().getSimpleName();
        if (methodSwitchNotify.className().equals(className)) {
            methodVersion.put(methodSwitchNotify.methodName(), methodSwitchNotify.methodVersion());
        }
    }

    /**
     * 强制设置某个店使用新版本数据
     *
     * @param forceNewVersionByShopIdNotifyEvent 强制设置的通知
     */
    @EventListener(ForceNewVersionByShopIdNotifyEvent.class)
    public void ForceNewVersionSetBeNotified(ForceNewVersionByShopIdNotifyEvent forceNewVersionByShopIdNotifyEvent) {
        forceNewVersionSet.put(forceNewVersionByShopIdNotifyEvent.shopId(), forceNewVersionByShopIdNotifyEvent.force());
    }

    /**
     * 打印api调用参数
     *
     * @param logPlace 位置调用 see LogUtil
     * @param arg      参数
     */
    private void logTheApiInvoke(String logPlace, Object... arg) {
        if (logTheOutSystemAPIInvoke) {
            log.debug("{} invoke:{}", logPlace, JSON.toJSONString(arg));
        }
    }


    /**
     * 批量查询商品数据
     *
     * @param pageArg            page参数
     * @param thirdPartyUserShop 访问key
     * @return 数据
     */
    private Either<Page<Y800ThirdSkuDataDTO>> queryThirdSkuByPage(Pageable pageArg, ThirdPartyUserShop thirdPartyUserShop) {
        Y800OpenRequest openRequest = new Y800OpenRequest();
        openRequest.setServiceName(Y800ServiceName.GoodsSearch.getServiceName());
        openRequest.setPartnerId(y800PartnerCode);
        var bizData = new QueryBiz(pageArg.getPageNumber(), pageArg.getPageSize(), thirdPartyUserShop.getThirdPartyCode());
        openRequest.setBizData(bizData);
        openRequest.sign(y800PartnerKey);

        logTheApiInvoke(LogUtil.getClassMethodName(), openRequest, bizData);
        log.info("Querying the Sku for Shop[{}] -> {}", thirdPartyUserShop.getShopId(), openRequest);
        HttpRequest apiRequest = HttpRequest.get(y800OpenApiGate, openRequest.toMap(), true);
        String rawBody = "";
        try {
            if (apiRequest.ok()) {
                rawBody = apiRequest.body();
                log.info("Querying the Sku for Shop[{}] -> {}", thirdPartyUserShop.getShopId() ,rawBody);
                Y800ResponseModel<Y800ThirdSkuStockPageDTO> y800Response = JSON.parseObject(rawBody, new TypeReference<Y800ResponseModel<Y800ThirdSkuStockPageDTO>>() {
                }.getType());
                if (y800Response.isSuccess()) {
                    return Either.ok(new PageImpl<>(y800Response.getData().getList(), PageRequest.of(pageArg.getPageNumber(), pageArg.getPageSize()), y800Response.getData().getTotal()));
                }
            }
            throw new RuntimeException(new Translate("请求失败 m=%s b=%s", apiRequest.message(), apiRequest.body()).toString());
        } catch (Exception ex) {
            log.error("{} fail invoke api res:{} by {}", LogUtil.getClassMethodName(), openRequest.toMap(), rawBody, ex);
            return Either.error(ex);
        }
    }

    /**
     * 合并库存
     *
     * @param codeMapSku               外部商品编码映射
     * @param stockList                外部库存映射
     * @param queryStockByThirdSkuData 查询结果
     * @apiNote 虽然外部系统支持多个仓库, 但是目前我们不支持多仓库, 做好准备吧
     */
    private void mergeStock(Long shopId, Map<String, Y800ThirdSkuDataDTO> codeMapSku
            , List<ThirdPartySkuStock> stockList
            , Either<List<Y800SkuStockDataContainDTO>> queryStockByThirdSkuData) {
        List<ThirdPartySkuStock> invalidSkuStockList = new ArrayList<>();
        if (!queryStockByThirdSkuData.isSuccess()) {
            log.error("{} Fail to query the stock [{}]", LogUtil.getClassMethodName(), queryStockByThirdSkuData.getErrorMsg());
        }
        for (Y800SkuStockDataContainDTO stockDataDTO : queryStockByThirdSkuData.take()) {
            for (Y800SkuStockDataDTO y800SkuStockDataDTO : stockDataDTO.getList()) {
                ThirdPartySkuStock thirdPartySkuStock = new ThirdPartySkuStock();
                thirdPartySkuStock.setShopId(shopId);
                // 复制可能需要的字段
                Optional.ofNullable(codeMapSku.get(stockDataDTO.getTotal().getSkuCode())).ifPresent(source -> BeanUtils.copyProperties(source, thirdPartySkuStock));
                BeanUtils.copyProperties(y800SkuStockDataDTO, thirdPartySkuStock);
                // 可用分销库存才是我们要用的正品,保留的认为次品
                thirdPartySkuStock.setAuthenticStock(y800SkuStockDataDTO.getTotal());
                thirdPartySkuStock.setDefectiveStock(y800SkuStockDataDTO.getSelftInventory());
                // 设置重要索引(系统内部的)
                thirdPartySkuStock.setThirdPartyId(ThirdPartySystem.Y800_V2.Id());
                thirdPartySkuStock.setOuterSkuId(stockDataDTO.getTotal().getSkuCode());
                // 重要但是累赘的虚拟批次号,因为原系统依赖 但是我又不想改
                if (Stream.of(thirdPartySkuStock.getDepotCode(), thirdPartySkuStock.getDepotName()).anyMatch(Objects::isNull)) {
                    log.warn("{} sku-stock:{} break Depot ruler that (name|code) can't be null", LogUtil.getClassMethodName(), JSON.toJSONString(thirdPartySkuStock));
                }
                if (thirdPartySkuStock.getDepotName() == null) {
                    thirdPartySkuStock.setDepotName("");
                }
                if (thirdPartySkuStock.getDepotCode() == null) {
                    thirdPartySkuStock.setDepotCode("");
                }
                Function<String, String> getHashCode = (str) ->
                {
                    String code = Math.abs(str.hashCode()) + "";
                    return code.length() > 3 ? code.substring(0, 3) : code;
                };
                String batchMaybe = "";
                try {
                    batchMaybe = String.format("(%s)@%s-%s#%s:%s|%s"
                            , getHashCode.apply(thirdPartySkuStock.getOuterSkuId())
                            , getHashCode.apply(thirdPartySkuStock.getDepotName())
                            , getHashCode.apply(thirdPartySkuStock.getDepotCode())
                            , getHashCode.apply(thirdPartySkuStock.getDepotName().concat(thirdPartySkuStock.getDepotCode()))
                            , getHashCode.apply(thirdPartySkuStock.getDepotName().concat(thirdPartySkuStock.getDepotName()))
                            , getHashCode.apply(thirdPartySkuStock.getDepotCode().concat(thirdPartySkuStock.getDepotName()))
                    );
                    thirdPartySkuStock.setBatch(batchMaybe.length() > 31 ? batchMaybe.substring(0, 32) : batchMaybe);
                } catch (Exception ex) {
                    log.error("{} fuck the batch:{} len:{}", LogUtil.getClassMethodName(), batchMaybe, batchMaybe.length(), ex);
                    thirdPartySkuStock.setBatch("");
                }
                // 设置缓存名字而已
                Optional.ofNullable(codeMapSku.get(stockDataDTO.getTotal().getSkuCode())).map(Y800ThirdSkuDataDTO::getName).ifPresent(thirdPartySkuStock::setOuterSkuName);
                if (Stream.of(thirdPartySkuStock.getOuterSkuId(), thirdPartySkuStock.getOuterSkuName()).allMatch(Objects::nonNull)) {
                    stockList.add(thirdPartySkuStock);
                } else {
                    if (!onlineEnv) {
                        log.error("{} null data:{}", LogUtil.getClassMethodName(), JSON.toJSONString(thirdPartySkuStock));
                    }
                    invalidSkuStockList.add(thirdPartySkuStock);
                }
            }
        }
        if (!invalidSkuStockList.isEmpty()) {
            log.error("{} we got {} empty Name stock,example of [0]={}", LogUtil.getClassMethodName(), invalidSkuStockList.size(), JSON.toJSON(invalidSkuStockList.get(0)));
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class QueryBiz extends Y800OpenRequest.BizData {
        Integer page;
        Integer pageSize;
        Integer status = 4;

        QueryBiz(int page, int size, String accessCode) {
            this.page = page;
            this.pageSize = size;
            setAccessCode(accessCode);
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class StockQueryBiz extends Y800OpenRequest.BizData {
        List<String> skuCode;

        void setSkuCode(List<String> skuCodeList) {
            this.skuCode = (skuCodeList);
        }
    }

}
