package moonstone.web.front.open.thirdParty.callback;

import moonstone.common.model.Y800OpenRequestV3;
import moonstone.common.utils.LogUtil;
import moonstone.web.front.open.dto.Y800CallBack;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.ParameterizedType;
import java.util.Objects;

/**
 * 为Y800特化的一个服务类,可抽象出Y800CallBack成为通用服务类
 *
 * @param <T>
 */

public abstract class Y800CallBackService<T extends Y800CallBack> implements AutoCloseable {

    final Logger log = LoggerFactory.getLogger(getClass());
    private final ThreadLocal<Y800OpenRequestV3> threadLocal = ThreadLocal.withInitial(() -> null);

    /**
     * 回调服务名
     *
     * @return 服务
     */
    public abstract String getServiceName();

    /**
     * 真实调用
     *
     * @param t 类型
     * @return 通常为true
     */
    public abstract Object handle(T t);

    /**
     * 检查签名
     *
     * @param requestV3 请求
     * @param secret    密钥
     * @throws RuntimeException 如果签名不匹配则抛出
     */
    public void checkTheSign(Y800OpenRequestV3 requestV3, String secret) {
        Y800OpenRequestV3 check = new Y800OpenRequestV3();
        BeanUtils.copyProperties(requestV3, check);
        check.sign(secret);
        if (!Objects.equals(check.getSign(), requestV3.getSign())) {
            log.error("{} request[{}] expect sign[{}] but got [{}]", LogUtil.getClassMethodName("NOT-MATCH"), requestV3.toMap(), check.getSign(), requestV3.getSign());
            throw new RuntimeException("签名不匹配");
        }
    }

    /**
     * 处理这个服务调用 不做服务检测,只直接解码
     *
     * @param requestV3 请求
     * @return 返回结果, 不会返回null
     * @throws RuntimeException 如果有无法处理的则抛出
     */
    public Object handle(Y800OpenRequestV3 requestV3) {
        threadLocal.set(requestV3);
        T data;
        try {
            data = requestV3.decodeBizData(getCallBackClass());
        } catch (Exception ex) {
            log.error("{} fail to decode [{}]", LogUtil.getClassMethodName(), requestV3, ex);
            throw new RuntimeException("无法解构信息结构");
        }
        return handle(data);
    }


    @Override
    public void close() throws Exception {
        threadLocal.remove();
    }

    public Y800OpenRequestV3 getRequest() {
        return threadLocal.get();
    }


    /**
     * 回调的实体
     *
     * @return 实体
     */
    public Class<T> getCallBackClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }
}
