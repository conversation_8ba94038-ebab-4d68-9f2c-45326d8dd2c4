/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.api;

import moonstone.order.dto.RichSkusByShop;
import moonstone.promotion.dto.PromotionContext;

import java.util.Map;

/**
 * 运费营销方式
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-05-31
 */
public interface ShipmentFeeBehavior extends Behavior {

    /**
     * 判断店铺级别的运费营销是否满足执行条件
     *
     * @param richSkusByShop   店铺订单
     * @param promotionContext  营销执行上下文
     * @param conditionParams  营销方式涉及的条件参数
     * @return  是否满足
     */
    boolean condition(RichSkusByShop richSkusByShop, PromotionContext promotionContext, Map<String, String> conditionParams);


    /**
     * 运费营销的结果, 会在对应的字段中设置计算结果
     *
     * 注意, 不要有任何持久化操作!!
     *
     * @param richSkusByShop    店铺订单
     * @param promotionContext 营销执行上下文
     * @param shipmentFeeParams 运费营销涉及的参数
     */
    void execute(RichSkusByShop richSkusByShop,
                 PromotionContext promotionContext,
                 Map<String, String> shipmentFeeParams);
}
