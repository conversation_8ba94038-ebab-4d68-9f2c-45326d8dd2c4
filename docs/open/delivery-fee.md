## 商品详情页运费计算

* 请求url
```
GET delivery.fee.charge.for.sku
```

* 请求参数示例
```
skuId=123&quantity=1&addressId=10000
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| skuId | 整形 | sku id | 是 |  |
| quantity | 整形 | sku数量 | 否 | 默认为1 |
| addressId | 整形 | 地址id | 是 |  |


* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": 100
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
|  result | 整形 | 所需运费 | 单位为分 |


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "运费计算失败"
     }
    ```


## 订单预览页运费计算

* 请求url
```
GET delivery.fee.charge.for.preview
```

* 请求参数示例
```
skuInfo=[{"skuId":1499,"quantity":1},{"skuId":5763,"quantity":1}]&receiverInfoId=1
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| skuInfo | 字符串 | skuId及数量 | 是 |  |
| receiverInfoId | 整形 | 收货地址id | 是 |  |


* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": {1:100,2:300}
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
|  result | map | 每个店铺所需运费 | key为店铺id;value为对应的运费，单位为分 |


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "运费计算失败"
     }
    ```