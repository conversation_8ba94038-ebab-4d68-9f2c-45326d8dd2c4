package moonstone.order.service;

import io.terminus.common.model.Response;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawPayment;
import moonstone.order.model.WithdrawPaymentReceiver;

public interface WithdrawPaymentWriteService {

    Response<Boolean> create(WithdrawPayment parameter);

    Response<Boolean> create(WithdrawPayment withdrawPayment, WithdrawPaymentReceiver receiver);

    Response<Boolean> update(WithDrawProfitApply applyOrder, WithdrawPayment withdrawPayment);

    Response<Boolean> update(WithdrawPayment paymentUpdate);
}
