package moonstone.weShop.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.weShop.model.WeShopAccountDetail;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Author:  CaiZhy
 * Date:    2018/12/28
 */
@Repository
public class WeShopAccountDetailDao extends MyBatisDao<WeShopAccountDetail> {
    public List<WeShopAccountDetail> findByWeShopId(Long weShopId){
        return getSqlSession().selectList(sqlId("findByWeShopId"), weShopId);
    }

    public List<WeShopAccountDetail> findByShopId(Long shopId){
        return getSqlSession().selectList(sqlId("findByShopId"), shopId);
    }

    public List<WeShopAccountDetail> findByWeShopIdAndShopId(Long weShopId, Long shopId){
        return getSqlSession().selectList(sqlId("findByWeShopIdAndShopId"), ImmutableMap.of("weShopId", weShopId, "shopId", shopId));
    }

    public Boolean updateStatus(Long id, Integer status) {
        return getSqlSession().update(sqlId("updateStatus"), ImmutableMap.of("id", id, "status", status)) == 1;
    }
}
