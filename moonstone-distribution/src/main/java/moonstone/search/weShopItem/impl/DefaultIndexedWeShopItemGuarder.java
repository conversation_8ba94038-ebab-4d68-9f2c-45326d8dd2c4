package moonstone.search.weShopItem.impl;

import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.weShop.model.WeShopItem;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by CaiZhy on 2018/12/24.
 */
public class DefaultIndexedWeShopItemGuarder implements IndexedWeShopItemGuarder {
    @Autowired
    private ItemReadService itemReadService;

    @Override
    public boolean indexable(WeShopItem weShopItem) {
        Item item = itemReadService.findById(weShopItem.getItemId()).getResult();
        return item != null && item.getStatus() > 0 && weShopItem.getStatus() != -3;
    }
}
