package moonstone.promotion.service;

import io.terminus.common.model.Response;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.model.UserPromotion;

/**
 * Desc: 营销工具写服务
 * Mail: <EMAIL>
 * Data: 16/6/23
 * Author: yangzefeng
 */
public interface PromotionWriteService {

    /**
     * 创建营销工具, 权限校验在调用方
     *
     * @param promotion 营销工具对象
     * @return id
     */
    Response<Long> create(Promotion promotion);

    /**
     * 更新营销工具, 权限校验在调用方
     *
     * @param promotion 营销工具对象
     * @return 是否更新成功
     */
    Response<Boolean> update(Promotion promotion);

    /**
     * @param userPromotion
     * @return 实际分发的数量
     */
    int doIssue(UserPromotion userPromotion);

    /**
     * 根据id删除营销工具实例
     *
     * @param promotionId 营销工具实例id
     * @return 是否删除成功
     */
    Response<Boolean> delete(Long promotionId);

    /**
     * 更新营销工具状态, 权限校验在调用方
     *
     * @param promotionId 营销工具id
     * @param status      待更新的状态
     * @return 是否更新成功
     */
    Response<Boolean> updateStatus(Long promotionId, Integer status);

    /**
     * 营销活动状态转移,这是给job用的
     * <p>
     * 将已发布且当前时间处于有效期的活动设为进行中
     * </p>
     * <p>
     * 将状态为进行中但当前时间已不在有效期的活动设为已过期
     * </p>
     */
    Response<Boolean> transferStatus();
}
