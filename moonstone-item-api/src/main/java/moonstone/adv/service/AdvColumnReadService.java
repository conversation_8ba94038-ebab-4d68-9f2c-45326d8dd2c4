package moonstone.adv.service;

import io.terminus.common.model.Response;
import moonstone.adv.model.AdvColumn;

import java.util.List;

/**
 * Created by CaiZhy on 2018/10/9.
 */
public interface AdvColumnReadService {
    Response<List<AdvColumn>> list();

    Response<AdvColumn> findById(Long id);

    Response<List<AdvColumn>> findBy(List<Long> ids, String sn,
                                     Integer platform, Integer canSellerEdit,
                                     String width, String height, Integer position);

    Response<List<AdvColumn>> findByPlatformAndCanSellerEdit(Integer platform, Integer canSellerEdit);
}
