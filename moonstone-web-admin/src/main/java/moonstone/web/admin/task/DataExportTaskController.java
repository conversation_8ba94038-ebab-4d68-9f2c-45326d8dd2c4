package moonstone.web.admin.task;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.user.convert.DataExportTaskConvertor;
import moonstone.user.service.DataExportTaskReadService;
import moonstone.user.vo.DataExportTaskVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * author：书生
 */
@RestController
@Slf4j
public class DataExportTaskController {

    @Resource
    private DataExportTaskReadService dataExportTaskReadService;

    /**
     * 根据任务id查询导出任务信息
     *
     * @param taskId 任务id
     * @return 任务信息
     */
    @GetMapping("/api/dataExportTask/findById")
    public Result<DataExportTaskVO> findById(@RequestParam("taskId") Long taskId) {
        var task = dataExportTaskReadService.findById(taskId).getResult();

        return Result.data(DataExportTaskConvertor.convert(task));
    }
}
