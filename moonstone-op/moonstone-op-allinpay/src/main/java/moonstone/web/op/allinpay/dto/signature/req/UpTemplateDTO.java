package moonstone.web.op.allinpay.dto.signature.req;

import lombok.Data;
import moonstone.web.op.allinpay.AllinPayMethodEnum;
import moonstone.web.op.allinpay.dto.signature.BaseRequest;

import java.io.Serializable;
import java.util.List;

@Data
public class UpTemplateDTO extends BaseRequest implements Serializable {
    /**
     * 统一客户号
     */
    private String merId;

    /**
     * 商户订单号（由商户生成），不重复值
     */
    private String custOrderId;

    /**
     * 操作者用户帐号，必须要指定一个用户帐号作为操作者
     */
    private String account;

    /**
     * 文件名称
     */
    private String fname;

    /**
     * 文件类型，当前仅支持PDF
     */
    private String ftype;

    /**
     * 文件页数，表示文件的总页数
     */
    private Long fpages;

    /**
     * 调用文件系统上传返回的token值
     */
    private String token;

    /**
     * 元素信息列表
     */
    private List<ElementInfo> elements;

    @Override
    public Class responseClass() {
        return UpTemplateDTO.class;
    }

    @Override
    public AllinPayMethodEnum method() {
        // "allinpay.uas.upTemplate"
        return AllinPayMethodEnum.SIGNATURE_UP_TEMPLATE;
    }

    /**
     * 证书申请状态信息内部类
     */
    @Data
    public static class ElementInfo implements Serializable {
        /**
         * 第几页
         */
        private Long pageNum;

        /**
         * X 坐标，详见坐标计算方法
         */
        private String x;

        /**
         * Y 坐标，详见坐标计算方法
         */
        private String y;

        /**
         * 默认字体 14 不支持小数
         */
        private Long fontSize;

        /**
         * 数据格式，文本：text，图片: image
         */
        private String type;
    }
}
