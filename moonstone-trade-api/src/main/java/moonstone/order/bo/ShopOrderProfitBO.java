package moonstone.order.bo;

import lombok.Data;

@Data
public class ShopOrderProfitBO {

    //A 开头的为服务商相关
    private String ARole;

    private String AName;

    private String AProvince;

    private String AMobile;

    private String AStatus;

    private String AProfit;

    //B 开头的为门店相关

    private String BId;
    private String BRole;

    private String BName;

    private String BProvince;
    private String BCity;

    private String BStatus;

    private String BProfit;

    private String BMobile;

    //C 开头的为导购相关
    private String CRole;

    private String CName;

    private String CStatus;

    private String CProfit;

    private String CMobile;

    /**
     * 导购存在时为true
     */
    private Boolean CExisted;
}
