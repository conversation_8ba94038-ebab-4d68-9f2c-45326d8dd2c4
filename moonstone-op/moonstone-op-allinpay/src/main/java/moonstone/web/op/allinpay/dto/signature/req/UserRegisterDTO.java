package moonstone.web.op.allinpay.dto.signature.req;

import lombok.Data;
import moonstone.web.op.allinpay.AllinPayMethodEnum;
import moonstone.web.op.allinpay.dto.signature.BaseRequest;
import moonstone.web.op.allinpay.dto.signature.res.UserRegisterResponse;

import java.io.Serializable;

@Data
public class UserRegisterDTO extends BaseRequest implements Serializable {

    /**
     * 统一客户号
     */
    private String merId;

    /**
     * 商户订单号（由商户生成），不重复值
     */
    private String custOrderId;

    /**
     * 用户帐号，用户的唯一标识，可以是邮箱、手机号、身份证号等不限，不能带有特殊字符。建议使用邮箱或手机号码
     */
    private String account;

    /**
     * 用户类型，1表示个人，2表示企业
     */
    private String userType;

    /**
     * 用户证件号，企业为法人代表证件号或经办人证件号，必须和证件上登记的姓名一致
     */
    private String identity;

    /**
     * 用户名称，个人为证件号上的姓名，企业为法人名称
     */
    private String name;

    /**
     * 证件类型，默认为 "0", 0-身份证, 1-护照, B-港澳居民往来内地通行证, C-台湾居民来往大陆通行证, E-户口簿, F-临时居民身份证
     */
    private String identityType;

    /**
     * 企业名称，userType为企业时必填，企业必须和企业证件上登记的名称一致，如个体工商户在营业执照上无企业名称的，请填“经营者”名字
     */
    private String corpName;

    /**
     * 工商注册号，三证合一的统一社会信用代码这样传：regcode = “统一社会信用代码”，orgcode = “统一社会信用代码”，taxcode = “统一社会信用代码”，
     * 老三证这样传：regcode = “工商注册号”，orgcode = “组织机构代码证”，taxcode = “税务登记证”，个体户这样传：regcode = “工商注册号”，orgcode = “”，taxcode = “”
     */
    private String regCode;

    /**
     * 组织机构代码
     */
    private String orgCode;

    /**
     * 税务登记证号
     */
    private String taxCode;

    /**
     * 联系手机，企业必填，用户为企业时联系手机必填，为 CA 年检抽查时联系使用
     */
    private String contactMobile;

    @Override
    public Class responseClass() {
        return UserRegisterResponse.class;
    }

    @Override
    public AllinPayMethodEnum method() {
        // "allinpay.uas.userRegister"
        return AllinPayMethodEnum.SIGNATURE_USER_REGISTER;
    }
}
