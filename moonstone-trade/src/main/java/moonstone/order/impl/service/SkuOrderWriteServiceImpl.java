package moonstone.order.impl.service;

import moonstone.order.impl.dao.SkuOrderDao;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.SkuOrderWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SkuOrderWriteServiceImpl implements SkuOrderWriteService {
    @Autowired
    private SkuOrderDao skuOrderDao;

    @Override
    public boolean updatePushStatus(Long skuOrderId, Integer pushStatus, Integer currentStatus) {
        return skuOrderDao.updatePushStatus(skuOrderId, pushStatus, currentStatus);
    }

    @Override
    public boolean updatePushResult(Long skuOrderId, Integer pushStatus, String pushErrorMsg, Integer currentPushStatus) {
        return skuOrderDao.updatePushResult(skuOrderId, pushStatus, pushErrorMsg, currentPushStatus);
    }

    @Override
    public boolean update(SkuOrder skuOrder) {
        return skuOrderDao.update(skuOrder);
    }

    @Override
    public void updateShippingWarehouseTypeByOrderId(Long shopOrderId, Integer shippingWarehouseType, Boolean shipping) {
        skuOrderDao.updateShippingWarehouseTypeByOrderId(shopOrderId, shippingWarehouseType, shipping);
    }
}
