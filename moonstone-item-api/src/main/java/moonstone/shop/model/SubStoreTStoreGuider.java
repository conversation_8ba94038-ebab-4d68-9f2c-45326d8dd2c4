package moonstone.shop.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.AuthAble;
import moonstone.common.model.EntityBase;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
public class SubStoreTStoreGuider extends EntityBase implements AuthAble {
    Long shopId;
    Long subStoreId;
    Long storeGuiderId;
    String storeGuiderNickname;
    String storeGuiderMobile;

    // impl it for tmp
    @Override
    public boolean isAuthed() {
        return true;
    }

    @Override
    public boolean isReject() {
        return false;
    }

    @Override
    public boolean auth() {
        return false;
    }

    @Override
    public boolean reject() {
        return false;
    }

    @Override
    public boolean revokeAuth() {
        return false;
    }

    @Override
    public boolean initAuth() {
        return false;
    }

    @Override
    public Date getAuthAt() {
        return null;
    }

    @Override
    public void setAuthAt(Date date) {

    }
}
