package moonstone.web.front.profit.domain.value.object;

import lombok.AllArgsConstructor;
import lombok.Data;
import moonstone.web.core.model.dto.CurrentProfitVO;
import moonstone.web.core.model.enu.CertificationEnu;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class WithdrawRuleResultVO {
    /**
     * 是否需要身份验证
     */
    CertificationEnu certificationStatus;
    /**
     * 提现金额剩余限制
     */
    Map<String, String> validCashWithdrawLeft;
    /**
     * 提现次数剩余限制
     */
    Map<String, String> validWithdrawTimeLeft;

    /**
     * 将内部数据装饰入 外部valueObject
     *
     * @param currentProfitVO 外部数据
     */
    public void decorate(CurrentProfitVO currentProfitVO) {
        currentProfitVO.setCertification(certificationStatus);
        currentProfitVO.setValidCashWithdrawLeft(validCashWithdrawLeft);
        currentProfitVO.setValidWithdrawTimeLeft(validWithdrawTimeLeft);
    }
}
