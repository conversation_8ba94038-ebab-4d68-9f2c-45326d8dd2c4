<?xml version="1.0" encoding="UTF-8" ?>

<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="ShopCategory">

    <resultMap id="ShopCategoryMap" type="ShopCategory">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="pid" property="pid"/>
        <result column="name" property="name"/>
        <result column="logo" property="logo"/>
        <result column="level" property="level"/>
        <result column="has_children" property="hasChildren"/>
        <result column="has_item" property="hasItem"/>
        <result column="index" property="index"/>
        <result column="disclosed" property="disclosed"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="visible" property="visible"/>
        <result column="extra_json" property="extraJson"/>
    </resultMap>

    <sql id="tb">
        parana_shop_categories
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        shop_id, pid, name,logo, level, has_children, has_item, `index`, disclosed, created_at, updated_at, visible,
        extra_json
    </sql>

    <sql id="vals">
        #{shopId}, #{pid}, #{name},#{logo}, #{level},#{hasChildren}, #{hasItem}, #{index}, #{disclosed}, now(), now(), #{visible},
        #{extraJson}
    </sql>

    <insert id="create" parameterType="ShopCategory" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.shopId}, #{i.pid}, #{i.name}, #{i.logo}, #{i.level}, #{i.hasChildren}, #{i.hasItem}, #{i.index}, #{i.disclosed},
            now(), now(), #{i.visible}, #{i.extraJson}
            )
        </foreach>
    </insert>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <select id="findNameById" parameterType="long" resultMap="ShopCategoryMap">
        SELECT
        `name`
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findById" parameterType="long" resultMap="ShopCategoryMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="ShopCategoryMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByShopIdAndPid" parameterType="long" resultMap="ShopCategoryMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id=#{shopId} and pid = #{pid}
    </select>

    <select id="findByShopId" parameterType="long" resultMap="ShopCategoryMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id=#{shopId}
    </select>

    <select id="findByShopIds" parameterType="list" resultMap="ShopCategoryMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id IN
        <foreach collection="shopIds" item="shopId"
                 open="(" separator="," close=")">
            #{shopId}
        </foreach>
    </select>
    <!--<update id="updateHasChildren" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET has_children = #{hasChildren}
        WHERE id = #{id}
    </update>

    <update id="updateHasItem" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET has_item = #{hasItem}
        WHERE id = #{id}
    </update>-->

    <update id="update" parameterType="ShopCategory">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="name != null">name = #{name},</if>
            <if test="logo !=null">logo = #{logo},</if>
            <if test="hasChildren!=null">has_children=#{hasChildren},</if>
            <if test="hasItem!=null">has_item=#{hasItem},</if>
            <if test="index != null">`index` = #{index},</if>
            <if test="disclosed != null">disclosed = #{disclosed},</if>
            <if test="visible != null">visible = #{visible},</if>
            <if test="extraJson != null">extra_json = #{extraJson},</if>
            updated_at=now()
        </set>
        WHERE id=#{id}
    </update>

    <update id="updatePid" parameterType="map">
        UPDATE <include refid="tb"/>
        set pid = #{pid},
            updated_at = now()
        where id=#{id}
    </update>

    <select id="findEntireTreeBycategoriesId" parameterType="long" resultMap="ShopCategoryMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id=#{categoriesId}
    </select>


    <select id="findByShopIdAndSort" parameterType="long" resultMap="ShopCategoryMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE shop_id  = #{shopId}
        and `level` =1 ORDER BY `index` asc
    </select>

</mapper>