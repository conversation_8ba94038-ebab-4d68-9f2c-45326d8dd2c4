package moonstone.order.enu;

/**
 * todo 不完整，待补充
 */
public enum ProfitWithdrawRecordStatusEnum {
    WAIT_FOR_AUDIT(1, "待审核"),
    AUDIT_FINISHED(2, "已审核"),
    ;


    private Integer code;
    private String description;

    private ProfitWithdrawRecordStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
