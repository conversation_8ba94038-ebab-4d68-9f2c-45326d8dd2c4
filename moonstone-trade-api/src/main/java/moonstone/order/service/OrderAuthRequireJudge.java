package moonstone.order.service;

import moonstone.common.model.SimpleRulerJudgeBean;
import moonstone.order.model.SkuOrder;

/**
 * 裁决订单是否需要审核,底层使用SimpleRuleJudgeBean
 *
 * @see moonstone.common.model.SimpleRulerJudgeBean#allow true则不需要审核
 */
public interface OrderAuthRequireJudge extends SimpleRulerJudgeBean<SkuOrder> {
    /**
     * 需要审核
     *
     * @param skuOrder 子订单
     * @return 是否需要
     */
    default boolean require(SkuOrder skuOrder) {
        return !allow(skuOrder);
    }
}
