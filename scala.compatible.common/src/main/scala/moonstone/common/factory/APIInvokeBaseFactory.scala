package moonstone.common.factory

import moonstone.common.api.remote.RemoteAPI
import moonstone.common.factory.api.RpcInvokeAdvise
import org.aopalliance.intercept.MethodInterceptor
import org.springframework.aop.framework.ProxyFactory

/**
 * 初始代理创建工厂
 */
class APIInvokeBaseFactory {

  /**
   * 默认切入点处理
   *
   * @return 切入点
   */
  private def baseInvocation(advise: RpcInvokeAdvise, apiName: String, remoteAPI: RemoteAPI): MethodInterceptor = invocation => {
    invocation.getMethod match {
      //  toString 返回字符串
      case toString if toString.getName.equals("toString") => apiName.concat("#RemoteProxy")
      //  获取配置信息
      case getConfig if getConfig.getName.equals("getConfig") && getConfig.getReturnType.equals(classOf[Option[RemoteAPI]]) => Option.apply(remoteAPI)
      //  调用advise方法
      case adviseMethod if advise.getMethodMap.contains(invocation.getMethod.getName) => adviseMethod.invoke(advise, invocation.getArguments: _*)

      /**
       *adviseMethod.getParameters match {
       * case empty if empty.isEmpty => adviseMethod.invoke(advise)
       * case single if single.size == 1 => adviseMethod.invoke(advise, invocation.getArguments()(0))
       * case _ =>
       *invocation.getMethod.invoke(advise,invocation.getArguments:_*)
       * //MethodUtil.invoke(invocation.getMethod, advise, invocation.getArguments)
       * }**/
      //  rpc调用顺序
      case rpcMethod => advise.beforeInvoke(rpcMethod, advise.prepareArg(invocation.getArguments.toArray)) match {
        case (method, args) => advise.decodeAfterInvoke(method, advise.proceed(method, args))
      }
    }
  }

  /**
   * 获取该类的代理实现对象
   *
   * @param bean     代理
   * @param beanType 类型
   * @tparam T 类型
   * @return
   */
  def getBeanProxy[T](bean: RpcInvokeAdvise, remoteAPI: RemoteAPI, beanType: Class[T]): T = {
    val proxyFactory: ProxyFactory = new ProxyFactory
    proxyFactory.setTargetClass(beanType)
    proxyFactory.addAdvice(baseInvocation(bean, beanType.getName, remoteAPI))
    proxyFactory.getProxy.asInstanceOf[T]
  }
}

