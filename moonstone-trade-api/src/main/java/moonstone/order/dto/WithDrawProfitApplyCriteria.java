package moonstone.order.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class WithDrawProfitApplyCriteria extends PagingCriteria {
    List<Long> ids;
    Date applyStartAt;
    Date applyEndAt;
    Date withDrawStartAt;
    Date withDrawEndAt;
    // 支付流水帐号
    String paySerialNo;
    /// 体现人的id
    List<Integer> bitmasks;
    List<Integer> notBitMasks;
    Long userId;
    List<Long> userIds;
    /// 费用
    Long fee;
    /// 平台Id(目前等于shopId)
    Long sourceId;

    Long shopUserId;
    String userType;

    /**
     * 提现人的身份角色 user_role（导购/门店/服务商）
     * 显辉小程序改版新增字段，通过月度账单生成的提现单，此字段才会有值
     */
    Boolean hasUserRole;

    /**
     * 提现人的身份角色 user_role（导购/门店/服务商）
     * 显辉小程序改版新增字段，通过月度账单生成的提现单，此字段才会有值
     */
    Integer userRole;

    /**
     * 提现单状态
     *
     * @see moonstone.common.enums.AccountStatementWithdrawStatusEnum
     */
    Integer withdrawApplyStatus;

}
