<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="Refund">

    <resultMap id="RefundMap" type="Refund">
        <id column="id" property="id"/>
        <result column="refund_type" property="refundType"/>
        <result column="fee" property="fee"/>
        <result column="diff_fee" property="diffFee"/>
        <result column="source_type" property="sourceType"/>
        <result column="shop_id" property="shopId"/>
        <result column="shop_name" property="shopName"/>
        <result column="buyer_id" property="buyerId"/>
        <result column="buyer_name" property="buyerName_"/>
        <result column="out_id" property="outId"/>
        <result column="integral" property="integral"/>
        <result column="balance" property="balance"/>
        <result column="status" property="status"/>
        <result column="refund_serial_no" property="refundSerialNo"/>
        <result column="payment_id" property="paymentId"/>
        <result column="trade_no" property="tradeNo"/>
        <result column="pay_serial_no" property="paySerialNo"/>
        <result column="refund_account_no" property="refundAccountNo"/>
        <result column="channel" property="channel"/>
        <result column="promotion_id" property="promotionId"/>
        <result column="reason_type" property="reasonType"/>
        <result column="buyer_note" property="buyerNote"/>
        <result column="seller_note" property="sellerNote"/>
        <result column="buyer_received_status" property="buyerReceivedStatus"/>
        <result column="shipping" property="shipping"/>
        <result column="images_json" property="imagesJson"/>
        <result column="extra_json" property="extraJson"/>
        <result column="tags_json" property="tagsJson"/>
        <result column="refund_at" property="refundAt"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_refunds
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `refund_type`,`fee`,`diff_fee`,`source_type`,`shop_id`,`shop_name`,`buyer_id`, `buyer_name`, `out_id`,`integral`,`balance`,`status`,`refund_serial_no`,`payment_id`,
        `trade_no`,`pay_serial_no`,`refund_account_no`, `channel`,`promotion_id`, `reason_type`, `buyer_note`, `seller_note`,`buyer_received_status`,`shipping`,`images_json`,
        `extra_json`,`tags_json`,`refund_at`,`created_at`,`updated_at`
    </sql>

    <sql id="columns_with_table_alias">
        rf.id,
        rf.`refund_type` ,rf.`fee`, rf.`diff_fee`,rf.`source_type`, rf.`shop_id`, rf.`shop_name`, rf.`buyer_id`, rf.`buyer_name`, rf.`out_id`,
        rf.`integral`, rf.`balance`, rf.`status`, rf.`refund_serial_no`, rf.`payment_id`,
        rf.`trade_no`, rf.`pay_serial_no`, rf.`refund_account_no`, rf.`channel`, rf.`promotion_id`, rf.`reason_type`, rf.`buyer_note`,
        rf.`seller_note`,rf.`shipping`, rf.`buyer_received_status`,rf.`images_json`, rf.`extra_json`, rf.`tags_json`, rf.`refund_at`, rf.`created_at`, rf.`updated_at`
    </sql>

    <sql id="vals">
        #{refundType},#{fee},#{diffFee},#{sourceType},#{shopId},#{shopName},#{buyerId},#{buyerName_},#{outId},#{integral},#{balance},#{status},#{refundSerialNo},#{paymentId},
        #{tradeNo},#{paySerialNo},#{refundAccountNo},#{channel},#{promotionId},#{reasonType},#{buyerNote},#{sellerNote},#{buyerReceivedStatus},#{shipping},#{imagesJson},
        #{extraJson},#{tagsJson},#{refundAt},now(),now()
    </sql>

    <insert id="create" parameterType="Refund" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findById" parameterType="long" resultMap="RefundMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByIds" parameterType="list" resultMap="RefundMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByRefundSerialNo" parameterType="string" resultMap="RefundMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `refund_serial_no`= #{refundSerialNo}
    </select>

    <select id="findByOutId" parameterType="string" resultMap="RefundMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `out_id` = #{outId}
    </select>
    <select id="findByTradeNo" parameterType="string" resultMap="RefundMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE `trade_no` = #{tradeNo}
    </select>


    <sql id="criteria">
        <if test="id!=null">AND rf.id=#{id}</if>
        <if test="shopId!=null">AND rf.shop_id=#{shopId}</if>
        <if test="buyerId!=null"> AND rf.buyer_id=#{buyerId}</if>
        <if test="channels!=null">AND
            rf.channel IN
            <foreach collection="channels" open="(" separator="," close=")" item="s">
                #{s}
            </foreach>
        </if>

        <if test="buyerIds!=null">AND
            rf.buyer_id IN
            <foreach collection="buyerIds" open="(" separator="," close=")" item="s">
                #{s}
            </foreach>
        </if>

        <if test="sellerNote!=null"> AND rf.seller_note=#{sellerNote}</if>
        <if test="status!=null">AND
            rf.status IN
            <foreach collection="status" open="(" separator="," close=")" item="s">
                #{s}
            </foreach>
        </if>
        <if test="startAt != null">AND <![CDATA[rf.updated_at >= #{startAt}]]> </if>
        <if test="endAt != null">AND <![CDATA[rf.updated_at <= #{endAt}]]> </if>
        <if test="refundStartAt != null">AND <![CDATA[rf.refund_at >= #{refundStartAt}]]> </if>
        <if test="refundEndAt != null">AND <![CDATA[rf.refund_at <= #{refundEndAt}]]> </if>
        <if test="ids != null">
            AND rf.id IN
            <foreach collection="ids" open="(" separator="," close=")" item="i">
                #{i}
            </foreach>
        </if>
        <if test="reasonType != null">AND rf.`reason_type` = #{reasonType}</if>
        <if test="refundType != null">AND rf.`refund_type` = #{refundType}</if>
        <if test="refundTypes != null">
            AND rf.`refund_type` IN
            <foreach collection="refundTypes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="tradeNo != null">AND rf.`trade_no` = #{tradeNo}</if>
        <if test="paymentId != null">AND rf.`payment_id` = #{paymentId}</if>
        <if test="paySerialNo != null">AND rf.`pay_serial_no` = #{paySerialNo}</if>
        <if test="idList!=null">
            AND rf.id in
            <foreach collection="idList" open="(" close=")" item="item" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)

        FROM `parana_refunds` rf
        <if test="subStoreIdList != null or guiderUserId != null">
            JOIN `parana_order_refunds` o_rf ON rf.`id`= o_rf.`refund_id` AND o_rf.`order_type` = 1
            JOIN parana_shop_orders orders ON o_rf.`order_id` = orders.`id`
            <if test="subStoreIdList != null">
                AND orders.out_shop_id in
                <foreach collection="subStoreIdList" open="(" item="item" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="guiderUserId != null">
                AND orders.referer_id = #{guiderUserId}
            </if>
        </if>

        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="RefundMap">
        SELECT
        <include refid="columns_with_table_alias"/>

        FROM `parana_refunds` rf
        <if test="subStoreIdList != null or guiderUserId != null">
            JOIN `parana_order_refunds` o_rf ON rf.`id`= o_rf.`refund_id` AND o_rf.`order_type` = 1
            JOIN parana_shop_orders orders ON o_rf.`order_id` = orders.`id`
                                           <if test="subStoreIdList != null">
                                               AND orders.out_shop_id in
                                               <foreach collection="subStoreIdList" open="(" item="item" separator="," close=")">
                                                   #{item}
                                               </foreach>
                                           </if>
                                           <if test="guiderUserId != null">
                                               AND orders.referer_id = #{guiderUserId}
                                           </if>
        </if>

        <where>
            <include refid="criteria"/>
        </where>
        ORDER by rf.id desc
        LIMIT #{offset}, #{limit}
    </select>


    <update id="update" parameterType="Refund">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now()
            <if test="refundType != null">, `refund_type` = #{refundType}</if>
            <if test="fee != null">,`fee` = #{fee}</if>
            <if test="sourceType != null">,`source_type` = #{sourceType}</if>
            <if test="outId != null">,`out_id` = #{outId}</if>
            <if test="integral != null">,`integral` = #{integral}</if>
            <if test="balance != null">,`balance` = #{balance}</if>
            <if test="status != null">,`status` = #{status}</if>
            <if test="refundSerialNo != null">,`refund_serial_no` = #{refundSerialNo}</if>
            <if test="channel != null">,`channel` = #{channel}</if>
            <if test="promotionId != null">,`promotion_id` = #{promotionId}</if>
            <if test="reasonType != null">,`reason_type` = #{reasonType}</if>
            <if test="sellerNote!=null">,`seller_note` = #{sellerNote}</if>
            <if test="buyerReceivedStatus!=null">,`buyer_received_status` = #{buyerReceivedStatus}</if>
            <if test="shipping!=null">,`shipping` = #{shipping}</if>
            <if test="buyerNote!=null">,`buyer_note` = #{buyerNote}</if>
            <if test="imagesJson != null">,`images_json` = #{imagesJson}</if>
            <if test="extraJson != null">,`extra_json` = #{extraJson}</if>
            <if test="tagsJson != null">,`tags_json` = #{tagsJson}</if>
            <if test="refundAt != null">,`refund_at` = #{refundAt}</if>
        </set>
        WHERE id=#{id}
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now(),
            status = #{status}
        </set>
        WHERE id = #{refundId} and status = #{originStatus}
    </update>


    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <select id="findRefundIdsByRefundAt" parameterType="map" resultType="long">
        SELECT `id`
        FROM
        <include refid="tb"/>
        WHERE `refund_at` &gt;= #{refundStartAt} and `refund_at` &lt;= #{refundEndAt}
    </select>

    <select id="findByShopOrderIds" parameterType="map" resultMap="RefundMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE
    </select>

    <update id="updateStatusOfOrders" parameterType="map">
        UPDATE parana_shop_orders t
            INNER JOIN parana_sku_orders t_s ON t.id = t_s.order_id
            SET t.status = -4,
                t_s.status = -4
        WHERE
            t.id IN (
            SELECT
            order_id
            FROM
            parana_order_refunds
            WHERE
            refund_id = #{refundId}
            )        
    </update>

    <update id="updateStatusOfRefunds" parameterType="map">
        UPDATE parana_refunds t
            INNER JOIN parana_order_refunds t_or ON t.id = t_or.refund_id
            SET t_or.status = -4,
                t.status = -4,
                t.refund_type = 1
        WHERE
            t.id = #{refundId};
    </update>

    <select id="findRefundAfterShipped" parameterType="map" resultType="Long">
        SELECT COUNT(1)
        FROM parana_order_shipments
        WHERE (order_type = 1 AND
               order_id IN (SELECT DISTINCT(order_id) FROM parana_order_refunds WHERE refund_id = #{refundId}))
           OR (order_type = 2 AND order_id IN (SELECT t_sku.id
                                               FROM parana_sku_orders t_sku
                                               WHERE t_sku.`order_id` IN (
                                                   SELECT DISTINCT(order_id)
                                                   FROM parana_order_refunds
                                                   WHERE refund_id = #{refundId}
                                               )
        ))

    </select>

    <!-- 当前的退款单，只会关联到主订单 -->
    <select id="findForExport" parameterType="map" resultType="moonstone.order.model.result.RefundExportDO">
        SELECT t.id as refundId,
               t_so.id as shopOrderId,
               t_so.out_from as outFrom,
               t_so.referer_id as guiderUserId,
               t_so.out_shop_id as subStoreId,
               t_sku_o.id as skuOrderId,
               t.shop_id as shopId

        FROM parana_refunds t

        JOIN parana_order_refunds t_or ON t_or.`refund_id` = t.`id`

        JOIN parana_shop_orders t_so ON t_so.`id` = t_or.`order_id`
                                    AND t_or.`order_type` = 1

        JOIN parana_sku_orders t_sku_o ON t_sku_o.`order_id` = t_so.`id`

        WHERE t.`shop_id` = #{shopId}
        <if test="refundType != null">
            and t.refund_type = #{refundType}
        </if>
        <if test="statusList != null">
            and t.status in
            <foreach collection="statusList" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="createdAtStart != null">
            and <![CDATA[ t.created_at >= #{createdAtStart} ]]>
        </if>
        <if test="createdAtEnd != null">
            and <![CDATA[ t.created_at <= #{createdAtEnd} ]]>
        </if>
        <if test="refundIds != null">
            and t.id in
            <foreach collection="refundIds" open="(" item="item" separator="," close=")">
                #{item}
            </foreach>
        </if>

        LIMIT #{offset}, #{limit}
    </select>
</mapper>