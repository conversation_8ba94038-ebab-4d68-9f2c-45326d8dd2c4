package moonstone.web.core.settle.dto;

import lombok.Data;
import moonstone.common.utils.NumberUtil;
import moonstone.settle.enums.CheckStatus;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
public class ManualSettlement {
    Long id;                //  对账Id
    Boolean settleSuccess = true;  //  对账成功
    String reason;          //  原因
    String mark;            //  备注

    /**
     * 封装
     *
     * @param sheet   表单
     * @param nameMap 表单属性名对应
     * @return 数据
     */
    public static List<ManualSettlement> buildFrom(Sheet sheet, Map<String, String> nameMap) {
        class NameIndex {
            final String name;
            int index = -1;

            NameIndex(String name) {
                this.name = name;
            }
        }
        List<NameIndex> nameIndices = Stream.of("id", "checkStatus", "mark").map(NameIndex::new).collect(Collectors.toList());
        if (sheet.getLastRowNum() <= 1) return new ArrayList<>();
        Row firstRow = sheet.getRow(0);
        for (int i = firstRow.getFirstCellNum(); i < firstRow.getLastCellNum(); i++) {
            final int cellIndex = i;
            Cell cell = firstRow.getCell(i);
            if (ObjectUtils.isEmpty(cell.getStringCellValue())) continue;
            nameIndices.stream().filter(index -> nameMap.getOrDefault(index.name, "NOT_FOUND_NOT_EQUAL_POELKNUUPSNAPUSNHPASNUPAOS").equals(cell.getStringCellValue()))
                    .findFirst().ifPresent(index -> index.index = cellIndex);
        }
        List<ManualSettlement> manualSettlements = new ArrayList<>();
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            ManualSettlement manualSettlement = new ManualSettlement();
            for (NameIndex nameIndex : nameIndices) {
                String content = Optional.ofNullable(row.getCell(nameIndex.index)).map(Cell::getStringCellValue).orElse("");
                switch (nameIndex.name) {
                    case "id": {
                        NumberUtil.parseNumber(content, Long.TYPE).ifSuccess(manualSettlement::setId);
                        break;
                    }
                    case "checkStatus": {
                        if (content.equals(CheckStatus.WAIT_CHECK.toString()) || content.equals(CheckStatus.WAIT_GET_THIRD_ACCOUNTS.toString())) {
                            manualSettlement.setSettleSuccess(true);
                            manualSettlement.setReason("");
                        } else {
                            manualSettlement.setSettleSuccess(false);
                            manualSettlement.setReason(content);
                        }
                        break;
                    }
                    case "mark": {
                        if (!content.isEmpty())
                            manualSettlement.setMark(content);
                        break;
                    }
                }
            }
            manualSettlements.add(manualSettlement);
        }
        return manualSettlements;
    }
}
