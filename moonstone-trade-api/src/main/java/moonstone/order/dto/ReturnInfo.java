package moonstone.order.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * Author:  <PERSON><PERSON><PERSON><PERSON>
 * Date:    2018/12/25
 */
@Data
public class ReturnInfo implements Serializable {
    private static final long serialVersionUID = -6872469255411205598L;

    public static String returnNameName = "returnName";

    public static String returnMobileName = "returnMobile";

    public static String returnAddressName = "returnAddress";

    private Long refundId;

    private String returnName;

    private String returnMobile;

    private String returnAddress;

}
