package moonstone.order.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.order.impl.manager.OrderCommentManager;
import moonstone.order.model.OrderComment;
import moonstone.order.service.OrderCommentWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Author:cp,yzf
 * Created on 4/22/16.
 */
@Slf4j
@Service
@RpcProvider
public class OrderCommentWriteServiceImpl implements OrderCommentWriteService {

    private final OrderCommentManager orderCommentManager;

    @Autowired
    public OrderCommentWriteServiceImpl(OrderCommentManager orderCommentManager) {
        this.orderCommentManager = orderCommentManager;
    }

    @Override
    public Response<Long> create(OrderComment orderComment) {
        try {
            //todo: 查询敏感词汇
            orderCommentManager.create(orderComment);
            return Response.ok(orderComment.getId());
        } catch (ServiceException se) {
            return Response.fail(se.getMessage());
        } catch (Exception e) {
            log.error("fail to create order comment by {}, cause:{}", orderComment, Throwables.getStackTraceAsString(e));
            return Response.fail("order.comment.create.fail");
        }
    }

    @Override
    public Response<List<Long>> batchCreate(List<OrderComment> orderComments) {
        try {
            List<Long> commentIds = orderCommentManager.batchCreate(orderComments);
            return Response.ok(commentIds);
        } catch (ServiceException e) {
            log.error("fail to batch create comments:{},cause:{}",
                    orderComments, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        } catch (Exception e) {
            log.error("fail to batch create comments:{},cause:{}",
                    orderComments, Throwables.getStackTraceAsString(e));
            return Response.fail("order.comment.create.fail");
        }
    }

    @Override
    public Response<Boolean> updateStatus(Long id, Integer status) {
        try {
            if (id == null) {
                return Response.fail("order.comment.id.not.found");
            }
            OrderComment update = new OrderComment();
            update.setId(id);
            update.setStatus(status);
            return Response.ok(orderCommentManager.update(update));
        } catch (Exception e) {
            log.error("fail to update order comment by id {}, status {}, cause:{}",
                    id, status, Throwables.getStackTraceAsString(e));
            return Response.fail("order.comment.update.fail");
        }
    }
}
