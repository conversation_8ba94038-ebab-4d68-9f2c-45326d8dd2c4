/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.front.category;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.BackCategoryCacher;
import moonstone.cache.CategoryAttributeCacher;
import moonstone.category.dto.GroupedCategoryAttribute;
import moonstone.category.model.BackCategory;
import moonstone.category.model.FrontCategory;
import moonstone.category.service.BackCategoryReadService;
import moonstone.common.api.Result;
import moonstone.common.exception.ApiException;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品后台类目相关接口
 */
@RestController
@Slf4j
@RequestMapping("/api/backCategories")
public class BackCategories {

    @RpcConsumer
    private BackCategoryCacher backCategoryCacher;

    @RpcConsumer
    private BackCategoryReadService backCategoryReadService;

    @RpcConsumer
    private CategoryAttributeCacher categoryAttributeCacher;

    @RequestMapping(value = "/children", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<BackCategory> findChildrenByPid(@RequestParam(value = "pid", defaultValue = "0") Long pid) {
        try {
            return backCategoryCacher.findChildrenOf(pid);
        } catch (Exception e) {
            log.error("failed to find back category children of category(id={}), cause:{}",
                    pid, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("category.children.find.fail");
        }

    }

    /**
     * 获取后台类目
     * @param pid 父id
     * @return 后台类目
     */
    @RequestMapping(value = "/children/v2", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<BackCategory>> findChildrenByPidV2(@RequestParam(value = "pid", defaultValue = "0") Long pid) {
        Response<List<BackCategory>> r = backCategoryReadService.findChildrenByPid(pid);
        if (!r.isSuccess()) {
            log.warn("failed to find children of back category(id={}), error code:{}", pid, r.getError());
            throw new ApiException(r.getError());
        }
        return Result.data(r.getResult());
    }

    @RequestMapping(value = "/{id}/children-nocache", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Response<List<BackCategory>> findChildrenByPidNoCache(@PathVariable Long id) {
        try {
            return backCategoryReadService.findChildrenByPid(id);
        } catch (Exception e) {
            log.error("failed to find back category without cache children of category(id={}), cause:{}",
                    id, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("category.children.find.fail");
        }
    }

    @RequestMapping(value = "/{id}/grouped-attribute", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<GroupedCategoryAttribute> findGroupedAttributeByCategoryId(@PathVariable Long id) {
        return categoryAttributeCacher.findGroupedAttributeByCategoryId(id);
    }

    /**
     * 获取类目属性
     * @param id 类目id
     * @return 类目属性
     */
    @GetMapping("/grouped-attribute/v2")
    public Result<List<GroupedCategoryAttribute>> findGroupedAttributeByCategoryIdV2(@RequestParam("id") Long id) {
        log.info("获取类目属性 请求参数 id {}",id);
        return Result.data(categoryAttributeCacher.findGroupedAttributeByCategoryId(id));
    }

    @GetMapping("/{id}/tree")
    public List<FrontCategory> buildTree(@PathVariable Long id) {
        return backCategoryCacher.buildFrontCategoryLine(id);
    }
}
