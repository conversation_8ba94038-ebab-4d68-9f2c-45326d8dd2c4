package moonstone.web.admin.jobs.settle.component;

import com.google.common.base.Stopwatch;
import com.google.common.base.Throwables;
import lombok.extern.slf4j.Slf4j;
import moonstone.web.core.settle.SettleCreateService;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * DATE: 16/11/13 下午3:10 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Slf4j
@Component
public class SettleRefundJob {

    @Autowired
    private SettleCreateService settleCreateService;

    @Resource
    private RedissonClient redissonClient;

    private static final DateTimeFormatter DFT = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");

    public void settleRefunds(Date startAt, Date endAt) {

        Lock iLock = redissonClient.getLock(getClass().getName() + "#settleRefunds");
        if (!iLock.tryLock()) {
            return;
        }
        log.info("[CRON-JOB] settleRefunds begin {}", DFT.print(DateTime.now()));
        Stopwatch stopwatch = Stopwatch.createStarted();

        try {
            settleCreateService.settleRefunds(startAt, endAt);
        } catch (Exception e) {
            log.error("settle refunds daily fail, startAt={}, endAt={}, error:{}", startAt, endAt, Throwables.getStackTraceAsString(e));
        } finally {
            iLock.unlock();
        }

        stopwatch.stop();
        log.info("[CRON-JOB] settleRefunds done at {} cost {} ms", DFT.print(DateTime.now()), stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }
}
