/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.component.spu.component;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.rule.RuleEngine;
import moonstone.spu.dto.FullSpu;
import moonstone.spu.service.SpuWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 创建和编辑spu
 * <p/>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-23
 */
@Component
@Slf4j
public class SpuWriter {

    @RpcConsumer
    private SpuWriteService spuWriteService;

    @Autowired
    private RuleEngine ruleEngine;

    /**
     * 创建spu
     *
     * @param fullSpu 所有的spu信息
     * @return 新建的spu id
     */
    public Response<Long> create(FullSpu fullSpu) {
        final Long categoryId = fullSpu.getSpu().getCategoryId();

        try {
            ruleEngine.handleInboundData(fullSpu, null);
            return spuWriteService.create(fullSpu);
        } catch (JsonResponseException e) {
            log.error("failed to validate spu attributes against category(id={}), cause:{}",
                    categoryId, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }

    }

    /**
     * 更新spu信息
     *
     * @param fullSpu spu信息
     * @return 是否更新成功
     */
    public Response<Boolean> update(FullSpu fullSpu) {
        final Long categoryId = fullSpu.getSpu().getCategoryId();

        try {
            ruleEngine.handleInboundData(fullSpu,null);
            return spuWriteService.update(fullSpu);
        } catch (JsonResponseException e) {
            log.error("failed to validate spu attributes against category(id={}), cause:{}",
                    categoryId, Throwables.getStackTraceAsString(e));
            return Response.fail(e.getMessage());
        }
    }
}
