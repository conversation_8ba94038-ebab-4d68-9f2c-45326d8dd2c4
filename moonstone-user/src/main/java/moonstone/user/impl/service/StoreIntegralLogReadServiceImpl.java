package moonstone.user.impl.service;

import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.user.impl.dao.StoreIntegralLogDao;
import moonstone.user.impl.dao.StoreIntegralLogSumDao;
import moonstone.user.model.StoreIntegralLog;
import moonstone.user.model.StoreIntegralLogSum;
import moonstone.user.service.StoreIntegralLogReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/27 18:56
 */
@Service
@Slf4j
public class StoreIntegralLogReadServiceImpl implements StoreIntegralLogReadService {
    @Autowired
    StoreIntegralLogDao storeIntegralLogDao;

    @Autowired
    StoreIntegralLogSumDao storeIntegralLogSumDao;

    @Override
    public Response<List<StoreIntegralLog>> findStoreIntegralLogByUserId(long userId) {
        return null;
    }

    @Override
    public Either<List<StoreIntegralLogSum>> findIntegralTotal(List<Long> userIds) {
        try {
            return Either.ok(storeIntegralLogSumDao.findIntegralTotal(userIds));
        } catch (Exception ex) {
            log.error("{} criteria:{}", LogUtil.getClassMethodName(), userIds);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }

    @Override
    public Either<List<StoreIntegralLog>> findIntegralBySupperId(Long userId) {
        try {
            return Either.ok(storeIntegralLogDao.findIntegralBySupperId(userId));
        } catch (Exception ex) {
            log.error("{} shopId:{}", LogUtil.getClassMethodName(), userId);
            ex.printStackTrace();
            return Either.error(ex);
        }
    }
}
