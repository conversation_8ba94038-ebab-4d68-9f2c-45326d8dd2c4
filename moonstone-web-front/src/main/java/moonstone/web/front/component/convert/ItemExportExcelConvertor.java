package moonstone.web.front.component.convert;

import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.GroupedOtherAttribute;
import moonstone.attribute.dto.OtherAttribute;
import moonstone.attribute.dto.PreservedGroup;
import moonstone.common.enums.SingleProductCommissionEnum;
import moonstone.item.dto.SkuWithItemForList;
import moonstone.item.emu.SkuCustomTaxHolderEnum;
import moonstone.item.emu.SkuExtraIndex;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.model.ItemAttribute;
import moonstone.item.model.SkuCustom;
import moonstone.web.front.item.app.ItemConvertor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ItemExportExcelConvertor {

    @Resource
    private ItemConvertor itemConvertor;

    private static final String ATTRIBUTE_KEY_WEIGHT = "weight";
    private static final String ATTRIBUTE_KEY_ORIGIN = "origin";

    private static final String CLOSE = "关闭";
    private static final String OPEN = "开启";

    public void appendItemAttribute(SkuWithItemForList target, ItemAttribute itemAttribute) {
        if (target == null || itemAttribute == null) {
            return;
        }

        appendWeight(target, itemAttribute);
        appendOrigin(target, itemAttribute);
    }

    private void appendOrigin(SkuWithItemForList target, ItemAttribute itemAttribute) {
        if (CollectionUtils.isEmpty(itemAttribute.getOtherAttrs())) {
            return;
        }

        var origin = itemAttribute.getOtherAttrs().stream()
                .filter(attribute -> PreservedGroup.BASIC.name().equals(attribute.getGroup()))
                .map(GroupedOtherAttribute::getOtherAttributes)
                .flatMap(Collection::stream)
                .filter(otherAttribute -> ATTRIBUTE_KEY_ORIGIN.equals(otherAttribute.getAttrKey()))
                .map(OtherAttribute::getAttrVal)
                .findAny()
                .orElse(StringUtils.EMPTY);

        target.setOrigin(origin);
    }

    private void appendWeight(SkuWithItemForList target, ItemAttribute itemAttribute) {
        if (CollectionUtils.isEmpty(itemAttribute.getOtherAttrs())) {
            return;
        }

        var weight = itemAttribute.getOtherAttrs().stream()
                .filter(attribute -> PreservedGroup.BASIC.name().equals(attribute.getGroup()))
                .map(GroupedOtherAttribute::getOtherAttributes)
                .flatMap(Collection::stream)
                .filter(otherAttribute -> ATTRIBUTE_KEY_WEIGHT.equals(otherAttribute.getAttrKey()))
                .map(OtherAttribute::getAttrVal)
                .findAny()
                .orElse(StringUtils.EMPTY);

        target.setWeight(weight);
    }

    public void appendSkuCustom(SkuWithItemForList target, SkuCustom skuCustom) {
        if (target == null || skuCustom == null) {
            return;
        }

        target.setHsCode(skuCustom.getHsCode());
        target.setCustomTaxHolder(SkuCustomTaxHolderEnum.getDescriptionByCode(skuCustom.getCustomTaxHolder()));
    }

    public String centToYuan(String price) {
        if (StringUtils.isBlank(price)) {
            return StringUtils.EMPTY;
        }

        try {
            return new BigDecimal(price).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).toPlainString();
        } catch (Exception ex) {
            log.error("ItemExportExcelConvertor.centToYuan error, price={}", price, ex);
            return null;
        }
    }

    public String convertActivityTime(Map<String, String> skuExtra) {
        if (CollectionUtils.isEmpty(skuExtra)) {
            return StringUtils.EMPTY;
        }

        String start = skuExtra.get(SkuExtraIndex.activityStartTime.getCode());
        String end = skuExtra.get(SkuExtraIndex.activityEndTime.getCode());
        if (StringUtils.isBlank(start) && StringUtils.isBlank(end)) {
            return StringUtils.EMPTY;
        }

        var sb = new StringBuilder();
        if (StringUtils.isNotBlank(start)) {
            sb.append(start);
        }
        if (StringUtils.isNotBlank(end)) {
            sb.append("至").append(end);
        }

        return sb.toString();
    }

    public void appendCommission(SkuWithItemForList target, IntermediateInfo defaultConfig, List<IntermediateInfo> itemConfigs) {
        if (target == null || (defaultConfig == null && CollectionUtils.isEmpty(itemConfigs))) {
            return;
        }
        target.setUseItemCommission(parseUseItemCommission(itemConfigs));

        var targetConfig = itemConvertor.chooseConfig(defaultConfig, itemConfigs);
        if (targetConfig == null) {
            log.warn("shopId={}, itemId={}, 没有查询到可用的佣金配置", target.getShopId(), target.getItemId());
            return;
        }

        appendRoleCommission(target, targetConfig);
    }

    private void appendRoleCommission(SkuWithItemForList target, IntermediateInfo targetConfig) {
        targetConfig.checkFlag();
        var calculateType = IntermediateInfo.CalculateType.parse(targetConfig.getFlag());
        if (calculateType == null) {
            return;
        }

        target.setItemCommissionType(calculateType.getDescription());
        switch (calculateType) {
            case FIXED_AMOUNT -> setByFixAmount(target, targetConfig);
            case BY_RATIO -> setByRatio(target, targetConfig);
        }
    }

    private void setByRatio(SkuWithItemForList target, IntermediateInfo targetConfig) {
        target.setServiceProviderCommission(toPercent(targetConfig.getServiceProviderRate()));
        target.setSubStoreCommission(toPercent(targetConfig.getFirstRate()));
        target.setGuiderCommission(toPercent(targetConfig.getSecondRate()));
    }

    private void setByFixAmount(SkuWithItemForList target, IntermediateInfo targetConfig) {
        target.setServiceProviderCommission(centToYuan(targetConfig.getServiceProviderFee()));
        target.setSubStoreCommission(centToYuan(targetConfig.getFirstFee()));
        target.setGuiderCommission(centToYuan(targetConfig.getSecondFee()));
    }

    private String toPercent(Long rate) {
        if (rate == null) {
            return "0.00%";
        }

        return new BigDecimal(rate)
                .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                .toPlainString()
                + "%";
    }

    private String centToYuan(Long price) {
        if (price == null) {
            return "0.00";
        }

        return new BigDecimal(price)
                .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                .toPlainString();
    }

    private String parseUseItemCommission(List<IntermediateInfo> itemConfigs) {
        if (CollectionUtils.isEmpty(itemConfigs)) {
            return CLOSE;
        }

        var open = itemConfigs.stream()
                .anyMatch(config -> SingleProductCommissionEnum.OPEN.getCode().equals(config.getIsCommission()));
        return open ? OPEN : CLOSE;
    }
}
