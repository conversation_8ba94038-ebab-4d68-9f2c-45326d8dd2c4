package moonstone.user.auth;

import moonstone.common.enums.UserRole;
import moonstone.user.ext.UserTypeBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Component
public class DefaultAdminRoleProvider implements RoleProvider {

    @Autowired
    private RoleProviderRegistry roleProviderRegistry;

    @Autowired
    private UserTypeBean userTypeBean;

    @PostConstruct
    public void init() {
        roleProviderRegistry.addRoleProvider(this);
    }

    @Override
    public int acceptType() {
        return userTypeBean.getAdminType();
    }

    @Override
    public Role getRoleByUserId(Long userId) {
        return Role.createStatic(UserRole.ADMIN.name());
    }
}
