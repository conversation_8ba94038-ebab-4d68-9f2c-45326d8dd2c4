package moonstone.user.impl.service;

import com.google.common.base.Throwables;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.user.dto.StoreProxyCriteria;
import moonstone.user.impl.dao.StoreProxyDao;
import moonstone.user.model.StoreProxy;
import moonstone.user.service.StoreProxyReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class StoreProxyReadServiceImpl implements StoreProxyReadService {
    @Autowired
    private StoreProxyDao storeProxyDao;
    private final String FAIL_QUERY = "fail.query.storeProxy";

    @Override
    public Either<Optional<StoreProxy>> findByShopIdAndUserId(long shopId, long userId) {
        try {
            return Either.ok(Optional.ofNullable(storeProxyDao.findByShopIdAndUserId(shopId, userId)));
        } catch (Exception ex) {
            log.error("{} shopId:{} userId:{}", LogUtil.getClassMethodName(), shopId, userId, ex);
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<List<StoreProxy>> findByShopIdAndLevel(long shopId, Integer level) {
        try {
            return Either.ok(storeProxyDao.findByShopIdAndLevel(shopId, level));
        } catch (Exception ex) {
            log.error("{} shopId:{} level:{}", LogUtil.getClassMethodName(), shopId, level);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<List<StoreProxy>> findByUserIdAndLevel(long userId, Integer level) {
        try {
            return Either.ok(storeProxyDao.findUserIdAndLevel(userId, level));
        } catch (Exception ex) {
            log.error("{} userId:{} level:{}", LogUtil.getClassMethodName(), userId, level);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<Optional<StoreProxy>> findById(Long id) {
        try {
            return Either.ok(Optional.ofNullable(storeProxyDao.findById(id)));
        } catch (Exception ex) {
            log.error("{} id:{}", LogUtil.getClassMethodName(), id);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<Paging<StoreProxy>> paging(StoreProxyCriteria storeProxyCriteria) {
        try {
            return Either.ok(storeProxyDao.paging(storeProxyCriteria.toMap()));
        } catch (Exception ex) {
            log.error("{} criteria:{}", LogUtil.getClassMethodName(), storeProxyCriteria);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<List<StoreProxy>> findBySupperIdAndShopId(long supperId, long shopId) {
        try {
            return Either.ok(storeProxyDao.findBySupperIdAndShopId(supperId, shopId));
        } catch (Exception ex) {
            log.error("{} supperId:{} shopId:{}", LogUtil.getClassMethodName(), supperId, shopId);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<Long> count(StoreProxyCriteria storeProxyCriteria) {
        try {
            return Either.ok(storeProxyDao.count(storeProxyCriteria));
        } catch (Exception ex) {
            log.error("{} criteria:{}", LogUtil.getClassMethodName(), storeProxyCriteria);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Long findIdByUserId(long userId, Long shopId) {
        try {
            return storeProxyDao.findIdByUserId(userId, shopId);
        } catch (Exception ex) {
            log.error("{} criteria:{}", LogUtil.getClassMethodName(), userId);
            ex.printStackTrace();
            return null;
        }
    }

    @Override
    public Response<StoreProxy> findBySupperIdUseUserIdAndShopId(Long supperId,long shopId) {
        Response<StoreProxy> resp = new Response<StoreProxy>();
        try {
            StoreProxy storeProxy = storeProxyDao.findBySupperIdUseUserIdAndShopId(supperId, shopId);
            if (storeProxy == null){
                return Response.ok(new StoreProxy());
            }
            resp.setResult(storeProxy);
        } catch (Exception e){
            log.error("StoreProxys failed to find current login user(id={}), cause: {}", supperId, Throwables.getStackTraceAsString(e));
            resp.setError("StoreProxys user.profile.find.fail");
        }
        return resp;
    }

    @Override
    public Either<Optional<StoreProxy>> findByShopIdAndUserIdAndStatus(Long shopId, Long userId, long status) {
        try {
            log.debug("{} shopId:{} userId:{}", LogUtil.getClassMethodName(), shopId, userId);
            return Either.ok(Optional.ofNullable(storeProxyDao.findByShopIdAndUserIdAndStatus(shopId, userId, status)));
        } catch (Exception ex) {
            log.error("{} shopId:{} userId:{}", LogUtil.getClassMethodName(), shopId, userId);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<Long> getSubProxy(long userId, long shopId) {
        try {
            return Either.ok(Optional.ofNullable(storeProxyDao.getSubProxy(userId, shopId)).orElse(0L));
        } catch (Exception ex) {
            log.error("{} shopId:{} userId:{}", LogUtil.getClassMethodName(), shopId, userId);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<List<StoreProxy>> findByShopId(long shopId) {
        try {
            return Either.ok(storeProxyDao.findByShopIdAndLevel(shopId, null));
        } catch (Exception ex) {
            log.error("{} shopId:{}", LogUtil.getClassMethodName(), shopId);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<Long> getSubProxyIsAuth(Long userId, Long shopId) {
        try {
            return Either.ok(Optional.ofNullable(storeProxyDao.getSubProxyIsAuth(userId, shopId)).orElse(0L));
        } catch (Exception ex) {
            log.error("{} shopId:{} userId:{}", LogUtil.getClassMethodName(), shopId, userId);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<List<StoreProxy>> findBySupperIdAndShopIdAndStatus(long userId, Long shopId, long status) {
        try {
            return Either.ok(storeProxyDao.findBySupperIdAndShopIdAndStatus(userId, shopId, status));
        } catch (Exception ex) {
            log.error("{} supperId:{} shopId:{} status:{}", LogUtil.getClassMethodName(), userId, shopId, status);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<List<StoreProxy>> findByProxyShopName(String proxyShopName) {
        try {
            return Either.ok(storeProxyDao.findByProxyShopName(proxyShopName));
        } catch (Exception ex) {
            log.error("{} proxyShopName:{}  ", LogUtil.getClassMethodName(), proxyShopName);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<Optional<StoreProxy>> findByUserId(Long userId) {
        try {
            log.debug("{}  userId:{}", LogUtil.getClassMethodName(), userId);
            return Either.ok(Optional.ofNullable(storeProxyDao.findByUserId(userId)));
        } catch (Exception ex) {
            log.error("{}  userId:{}", LogUtil.getClassMethodName(), userId);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<List<StoreProxy>> findByIds(List<Long> proxyIds) {
        try {
            return Either.ok(storeProxyDao.findByIds(proxyIds));
        } catch (Exception ex) {
            log.error("{} proxyIds:{}  ", LogUtil.getClassMethodName(), proxyIds);
            ex.printStackTrace();
            return Either.error(ex, FAIL_QUERY);
        }
    }

    @Override
    public Either<Long> findUserIdByProxyName(String proxyName, Long shopId) {
        try {
            return Either.ok(storeProxyDao.findUserIdByProxyName(proxyName, shopId));
        } catch (Exception exception) {
            log.error("{} fail to act by proxyName[{}] shopId[{}]", LogUtil.getClassMethodName(), proxyName, shopId, exception);
            return Either.error(Translate.of("查询失败"));
        }
    }
}
