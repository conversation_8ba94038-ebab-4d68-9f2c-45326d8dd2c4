package moonstone.user.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.common.model.AuthAble;
import moonstone.common.utils.DateUtil;

import java.time.temporal.Temporal;


/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountCertificationForSellerVO {
    Long id;
    String shopName;
    String applierNickName;
    String name;
    String mobile;
    String identityCode;
    String frontImageUrl;
    String backImageUrl;
    String authStatusDesc;
    Integer authStatus;
    String createdAt;
    String authAt;
    String reason;
    AuthAble authAble;

    public void setCreatedAt(Temporal time) {
        createdAt = DateUtil.toString(time);
    }

    public void setAuthAt(Temporal time) {
        authAt = DateUtil.toString(time);
    }
}
