package moonstone.web.front.component.order.view;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * 退款单导出对象
 */
@Data
public class RefundExcelExportView {

    /**
     * 店铺平台id
     * <br/>非导出字段
     */
    @ExcelIgnore
    private Long shopId;

    /**
     * 主订单id
     * <br/>非导出字段
     */
    @ExcelIgnore
    private Long shopOrderId;

    /**
     * 子订单id
     * <br/>非导出字段
     */
    @ExcelIgnore
    private Long skuOrderId;

    /**
     * 退款单id
     * <br/>非导出字段
     */
    @ExcelIgnore
    private Long refundId;

    /**
     * 导购的用户id
     */
    @ExcelIgnore
    private Long guiderUserId;

    /**
     * 门店id
     */
    @ExcelIgnore
    private String subStoreId;

    /**
     * 订单来源
     */
    @ExcelIgnore
    private String outFrom;

    /**
     * 退款单号
     * <column width="8400"display="退款单号"name="outId"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "退款单号", order = 1)
    private String outId;

    /**
     * 订单号
     * <column width="4800"display="订单号"name="orderId"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "订单号", order = 2)
    private String orderId;

    /**
     * 申报推送单号
     * <column width="6000"display="申报推送单号"name="declaredId"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "申报推送单号", order = 3)
    private String declaredId;

    /**
     * 店铺名
     * <column width="2800"display="店铺名"name="shopName"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "店铺名", order = 4)
    private String shopName;

    /**
     * 服务商名称
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "服务商名称", order = 5)
    private String serviceProviderName;

    /**
     * 门店名称
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "门店名称", order = 6)
    private String subStoreName;

    /**
     * 导购名称
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "导购名称", order = 7)
    private String guiderName;

    /**
     * 订单状态
     * <column width="2000"display="订单状态"name="coStatus"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "订单状态", order = 8)
    private String coStatus;

    /**
     * 支付流水号
     * <column width="4000"display="支付流水号"name="paySerialNo"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "支付流水号", order = 9)
    private String paySerialNo;

    /**
     * 下单时间
     * <column width="4000"display="下单时间"name="createAt"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "下单时间", order = 10)
    private String createAt;

    /**
     * 支付时间
     * <column width="4000"display="支付时间"name="paidAt"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "支付时间", order = 11)
    private String paidAt;

    /**
     * 总价
     * <column width="1800"display="总价"name="sellFee"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "总价", order = 12)
    private String sellFee;

    /**
     * 微信openId
     * <column width="1800"display="微信openId"name="openId"/>
     */
//    @ColumnWidth(25)
//    @ExcelProperty(value = "微信openId", order = 10)
//    private String openId;

    /**
     * 收件人
     * <column width="1800"display="收件人"name="receiveUserName"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "收件人", order = 13)
    private String receiveUserName;

    /**
     * 手机号
     * <column width="3800"display="手机号"name="mobile"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "手机号", order = 14)
    private String mobile;

    /**
     * 收货地址
     * <column width="4800"display="收货地址"name="address"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "收货地址", order = 15)
    private String address;

    /**
     * 发货时间
     * <column width="4000"display="发货时间"name="shipmentTime"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "发货时间", order = 16)
    private String shipmentTime;

    /**
     * 商品名称
     * <column width="2800"display="商品名称"name="itemName"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "商品名称", order = 17)
    private String itemName;

    /**
     * 商品价格
     * <column width="1800"display="商品价格"name="originFee"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "商品价格", order = 18)
    private String originFee;

    /**
     * 退款额度
     * <column width="4000"display="退款额度"name="coFee"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "退款额度", order = 19)
    private String coFee;

    /**
     * 数量
     * <column width="1800"display="数量"name="quantity"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "数量", order = 20)
    private String quantity;

    /**
     * 税费
     * <column width="1800"display="税费"name="tax"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "税费", order = 21)
    private String tax;

    /**
     * 运费
     * <column width="1800"display="运费"name="shipFee"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "运费", order = 22)
    private String shipFee;

    /**
     * 退款交易流水号
     * <column width="8400"display="退款交易流水号"name="refundSerialNo"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "退款交易流水号", order = 23)
    private String refundSerialNo;

    /**
     * 退货地址
     * <column width="8400"display="退货地址"name="expressAddress"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "退货地址", order = 24)
    private String expressAddress;

    /**
     * 退货快递单号
     * <column width="8400"display="退货快递单号"name="expressNo"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "退货快递单号", order = 25)
    private String expressNo;

    /**
     * 退货快递公司名
     * <column width="4000"display="退货快递公司名"name="expressName"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "退货快递公司名", order = 26)
    private String expressName;

    /**
     * 退货收货人
     * <column width="4000"display="退货收货人"name="expressReceiverName"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "退货收货人", order = 27)
    private String expressReceiverName;

    /**
     * 退货收货手机号
     * <column width="8000"display="退货收货手机号"name="expressReceiverMobile"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "退货收货手机号", order = 28)
    private String expressReceiverMobile;

    /**
     * 操作时间
     * <column width="4000"display="操作时间"name="operaAt"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "操作时间", order = 29)
    private String operaAt;

    /**
     * 申请时间
     * <column width="4000"display="申请时间"name="applyAt"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "申请时间", order = 30)
    private String applyAt;

    /**
     * 退款理由
     * <column width="3500"display="退款理由"name="buyerNote"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "退款理由", order = 31)
    private String buyerNote;

    /**
     * 优惠金额
     * <column width="1800"display="优惠金额"name="discount"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "优惠金额", order = 32)
    private String discount;

    /**
     * 推送状态
     * <column width="3000"display="推送状态"name="pushStatus"/>
     */
    @ColumnWidth(25)
    @ExcelProperty(value = "推送状态", order = 33)
    private String pushStatus;
}
