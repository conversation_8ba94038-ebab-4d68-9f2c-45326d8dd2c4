package moonstone.web.front.component.item;

import com.google.common.collect.Lists;
import io.terminus.common.utils.Splitters;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.emu.ItemTypeEnum;
import moonstone.item.model.Item;
import moonstone.promotion.model.Promotion;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static moonstone.promotion.api.SkuScope.REQUIRE_INTEGRAL;

@Slf4j
@Component
public class ItemPromotionMatcher {

    private final static String TYPE = "type";
    private final static String TYPE_INCLUDE = "include";
    private final static String TYPE_EXCLUDE = "exclude";

    private final static String ITEM_IDS = "itemIds";

    private final static List<Integer> INTEGRAL_TYPE = Lists.newArrayList(ItemTypeEnum.COMBINATION.getCode(),
            ItemTypeEnum.SCAN_INTEGRAL.getCode(), ItemTypeEnum.NEW_CUSTOMER_GIFT.getCode(),
            ItemTypeEnum.ACCUMULATE_GIFT.getCode());

    /**
     * 简单的商品营销活动匹配器，用于小程序端的商品列表、商品详情里的“优惠卷”标签的显示
     *
     * @param item
     * @param sourceList
     * @return 当前商品适用的营销活动
     */
    public List<Promotion> match(Item item, List<Promotion> sourceList) {
        if (item == null || CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }

        return sourceList.stream()
                .filter(promotion -> match(item, promotion))
                .collect(Collectors.toList());
    }

    private boolean match(Item item, Promotion promotion) {
        var skuScopeParams = promotion.getSkuScopeParams();
        if (CollectionUtils.isEmpty(skuScopeParams)) {
            log.error("ItemPromotionMatcher.match, promotionId={}, skuScopeParams为空", promotion.getId());
            return false;
        }

        if (skuScopeParams.getOrDefault(REQUIRE_INTEGRAL, "false").equals("true")) {
            // 限积分商品使用
            if (!isIntegralType(item.getType())) {
                return false;
            }
        } else if (isIntegralType(item.getType())) {
            return false;
        }

        String type = skuScopeParams.get(TYPE);
        String itemIdsString = skuScopeParams.get(ITEM_IDS);
        if (StringUtils.isBlank(type) || StringUtils.isBlank(itemIdsString)) {
            log.error("ItemPromotionMatcher.match, promotionId={}, type={}, itemIdsString={}, 配置不合法",
                    promotion.getId(), type, itemIdsString);
            return false;
        }

        boolean contains = Splitters.splitToLong(itemIdsString, Splitters.COMMA).contains(item.getId());
        return switch (type) {
            case TYPE_INCLUDE -> contains;
            case TYPE_EXCLUDE -> !contains;
            default -> error(String.format("ItemPromotionMatcher.match, promotionId=%s, skuScopeParams.type=%s, 配置不合法",
                    promotion.getId(), type));
        };
    }

    private boolean isIntegralType(Integer itemType) {
        return INTEGRAL_TYPE.contains(itemType);
    }

    private boolean error(String errorMessage) {
        log.error(errorMessage);
        return false;
    }
}
