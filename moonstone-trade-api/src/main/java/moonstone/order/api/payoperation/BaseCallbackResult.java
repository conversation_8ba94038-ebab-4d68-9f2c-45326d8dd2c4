package moonstone.order.api.payoperation;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class BaseCallbackResult {

    /**
     * 原始回调报文
     */
    private String rawCallbackJson;

    /**
     * 是否支付成功
     */
    private boolean paySuccess;

    /**
     * 支付平台的交易单号
     */
    private String tradeNo;
}
