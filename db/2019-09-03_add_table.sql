
CREATE TABLE parana.parana_store_integral_record (
	id bigint(20) NULL AUTO_INCREMENT,
	integral_id bigint(20) not NULL COMMENT '积分账户id',
	face_value bigint(20) NOT NULL DEFAULT 0 COMMENT '原积分面值',
	remain_value bigint(20) NOT NULL DEFAULT 0 COMMENT '剩余可用积分',
	status tinyint(1) NULL COMMENT '积分账户状态',
	extra_json varchar(255) NULL COMMENT '拓展信息',
	valid_at DATETIME NULL COMMENT '有效期时间',
	created_at DATETIME NULL COMMENT '创建时间',
	updated_at DATETIME NULL COMMENT '修改时间',
	CONSTRAINT parana_store_integral_record_PK PRIMARY KEY (id)
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8 COMMENT='积分商城积分扫码有效期'
COLLATE=utf8_general_ci;
CREATE INDEX parana_store_integral_integral_id_IDX USING BTREE ON parana_store_integral_record (integral_id);
