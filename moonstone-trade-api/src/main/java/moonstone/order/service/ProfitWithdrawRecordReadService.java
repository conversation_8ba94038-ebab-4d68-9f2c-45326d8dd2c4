package moonstone.order.service;

import io.terminus.common.model.Response;
import moonstone.order.model.ProfitWithdrawRecord;
import moonstone.order.model.result.ProfitWithdrawRecordInfoDO;

import java.util.Date;
import java.util.List;

public interface ProfitWithdrawRecordReadService {

    Response<List<ProfitWithdrawRecord>> findByProfitIdsAndStatus(List<Long> profitIds, List<Integer> statusList);

    Response<List<ProfitWithdrawRecord>> findByProfitIds(List<Long> profitIds);

    Response<List<ProfitWithdrawRecordInfoDO>> findProfitByStatus(Long shopId, Long userId, Integer status,
                                                                  Date createdStartAt, Date createdEndAt);
}
