import moonstone.common.utils.EncryptHelper;
import moonstone.user.dto.EncryptImage;
import org.junit.Test;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.FileInputStream;
import java.net.URL;
import java.security.KeyStore;

public class ImageTest {
    @Test
    public void imageTest() throws Exception {
        String url = "http://dante-img.oss-cn-hangzhou.aliyuncs.com/test/8d601783a1aecd970943928f4c8a9bd5.jpg";
        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        keyStore.load(new FileInputStream("/home/<USER>/.keystore"), "devdev".toCharArray());
        EncryptHelper.instance.keyStore(keyStore);
        EncryptHelper.instance.secret("devdev");
        EncryptImage image = new EncryptImage(url);
        image.fetchData(Runnable::run);
        BufferedImage assertImg = image.image();
        BufferedImage matchImg = ImageIO.read(new URL(url));
        for (int i = 0; i < assertImg.getHeight(); i++) {
            for (int j = 0; j < assertImg.getWidth(); j++) {
                assert assertImg.getRGB(i, j) == matchImg.getRGB(i, j);
            }
        }
    }
}
