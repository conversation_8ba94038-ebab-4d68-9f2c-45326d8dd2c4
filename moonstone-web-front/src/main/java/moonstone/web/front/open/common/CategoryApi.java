package moonstone.web.front.open.common;

import com.google.common.base.MoreObjects;
import com.google.common.base.Throwables;
import io.terminus.pampas.openplatform.annotations.OpenBean;
import io.terminus.pampas.openplatform.annotations.OpenMethod;
import io.terminus.pampas.openplatform.exceptions.OPServerException;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.BackCategoryCacher;
import moonstone.cache.FrontCategoryCacher;
import moonstone.cache.ShopCategoryCacher;
import moonstone.category.dto.FrontCategoryTree;
import moonstone.category.dto.ShopCategoryWithChildren;
import moonstone.category.model.BackCategory;
import moonstone.category.model.FrontCategory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

/**
 * Author:cp
 * Created on 8/12/16.
 */
@OpenBean
@Slf4j
public class CategoryApi {

    @Autowired
    private FrontCategoryCacher frontCategoryCacher;

    @Autowired
    private BackCategoryCacher backCategoryCacher;

    @Autowired
    private ShopCategoryCacher shopCategoryCacher;

    @OpenMethod(key = "front.category.tree.find.by.ids", paramNames = {"ids"}, httpMethods = RequestMethod.GET)
    public List<FrontCategoryTree> findFrontCategoryTreeByIds(String ids) {
        try {
            return frontCategoryCacher.findByIds(ids);
        } catch (Exception e) {
            log.error("failed to find front category tree by ids={}, cause:{}",
                    ids, Throwables.getStackTraceAsString(e));
            throw new OPServerException("front.category.tree.find.fail");
        }
    }

    @OpenMethod(key = "back.category.children.find.by.pid", paramNames = {"pid"}, httpMethods = RequestMethod.GET)
    public List<BackCategory> findChildrenByPid(Long pid) {
        try {
            return backCategoryCacher.findChildrenOf(MoreObjects.firstNonNull(pid, 0L));
        } catch (Exception e) {
            log.error("failed to find back category children by pid={}, cause:{}",
                    pid, Throwables.getStackTraceAsString(e));
            throw new OPServerException("category.children.find.fail");
        }
    }

    @OpenMethod(key = "front.category.children.find.by.pid", paramNames = {"pid"}, httpMethods = RequestMethod.GET)
    public List<FrontCategory> findFrontChildrenByPid(Long pid) {
        try {
            return frontCategoryCacher.findChildrenOf(MoreObjects.firstNonNull(pid, 0L));
        } catch (Exception e) {
            log.error("failed to find front category children by pid={}, cause:{}",
                    pid, Throwables.getStackTraceAsString(e));
            throw new OPServerException("category.children.find.fail");
        }
    }


    @OpenMethod(key = "shop.category.tree.find.by.shop.id", paramNames = {"shopId"}, httpMethods = RequestMethod.GET)
    public List<ShopCategoryWithChildren> findShopCategoryTree(Long shopId) {
        try {
            return shopCategoryCacher.findEntireTreeOf(shopId);
        } catch (Exception e) {
            log.error("fail to find shop category tree by shopId={},cause:{}",
                    shopId, Throwables.getStackTraceAsString(e));
            throw new OPServerException("shop.category.find.fail");
        }
    }

}
