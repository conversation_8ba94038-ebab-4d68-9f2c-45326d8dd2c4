package moonstone.web.front.distribution.web.drop;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.RespResult;
import moonstone.user.model.Distribution;
import moonstone.user.service.DistributionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 功能描述:  认证环节分销商控制层
 * 创建时间:  2020/7/1 3:25 下午
 *
 * <AUTHOR>
 */
@SuppressWarnings("UnstableApiUsage")
@RestController
@Slf4j
@RequestMapping("/api/gx/distribution")
public class DistributionController {

    @Autowired
    private DistributionService distributionService;

    /**
     * 功能描述:  根据用户id查询UC系统那边是否存在分销商信息的信息。  供返回使用
     * 创建时间:  2020/7/1 3:30 下午
     *
     * @param userId: 用户id
     * @return io.terminus.common.model.Response<moonstone.user.model.Certification>
     * <AUTHOR>
     */
    @GetMapping("/getDistributionInfo")
    public RespResult<Distribution> getDistributionInfo(Long userId) {
        try {
            if (userId == null) {
                return RespResult.fail("用户ID不能为空！");
            }
            //返回查询到的认证记录，如果没查询到，则初始化认证记录。
            //supplierService.findByUserId(userid)
            return RespResult.data(distributionService.findByUserId(userId));
        } catch (Exception e) {
            return RespResult.fail("业务异常");
        }
    }


    /**
     * 功能描述:  用户填写的分销商信息存储，拿存储的数据去审核认证
     * 创建时间:  2020/7/2 1:56 下午
     *
     * @param distribution: 分销商实体类
     * @return moonstone.common.utils.RespResult<java.util.Map < java.lang.String, java.lang.Object>>
     * <AUTHOR>
     */
    @PostMapping("/saveOrUpdate")
    public RespResult<Map<String, Object>> saveOrUpdate(@RequestBody Distribution distribution) {
        try {
            if (distribution.getUserId() == null) {
                return RespResult.fail("用户ID不能为空！");
            }
            if (distribution.getStatus() == null) {
                return RespResult.fail("status状态字段必须要有默认值！");
            }
            Map<String, Object> map = new HashMap<>();
            //重新认证，重置状态码之后，返回重置的状态码。
            RespResult<Integer> respResult = distributionService.saveOrUpdate(distribution);
            if (respResult.isSuccess()) {
                map.put("status", respResult.getData());
                return RespResult.data(map);
            }
            return RespResult.fail(respResult.getMsg());
        } catch (Exception e) {
            return RespResult.fail("业务异常");
        }
    }

}
