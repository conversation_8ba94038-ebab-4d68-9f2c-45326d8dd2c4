package moonstone.user.impl.dao;

import com.google.common.collect.ImmutableBiMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import lombok.extern.slf4j.Slf4j;
import moonstone.user.criteria.UserExtraInformationCriteria;
import moonstone.user.model.UserExtraInformation;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class UserExtraInformationDao extends MyBatisDao<UserExtraInformation> {
    public List<UserExtraInformation> findByUserIdAndShopId(Long userId, Long shopId) {
        return getSqlSession().selectList(sqlId("findByUserIdAndShopId"), ImmutableBiMap.of("userId", userId, "shopId", shopId));
    }

    public List<UserExtraInformation> findByRelatedId(Long relatedId, Long shopId) {
        return getSqlSession().selectList(sqlId("findByRelatedId"), ImmutableBiMap.of("relatedId", relatedId, "shopId", shopId));
    }

    public List<UserExtraInformation> findByHashDataAndShopId(String hashData, Long shopId, Integer type) {
        return getSqlSession().selectList(sqlId("findByHashDataAndShopId"), ImmutableBiMap.of("hashData", hashData, "shopId", shopId, "type", type));
    }

    public Long count(UserExtraInformationCriteria criteria) {
        return getSqlSession().selectOne(sqlId("count"), criteria.toMap());
    }
}
