package moonstone.web.front.decoration;

import com.alibaba.fastjson.JSON;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.ImmutableMap;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.BaseUser;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.open.bean.auth.WxOpenAuthorizerInfo;
import me.chanjar.weixin.open.bean.ma.WxOpenMaSubmitAudit;
import me.chanjar.weixin.open.bean.message.WxOpenMaSubmitAuditMessage;
import me.chanjar.weixin.open.bean.result.*;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.*;
import moonstone.shopWxa.enums.ShopWxaProjectStatus;
import moonstone.shopWxa.enums.ShopWxaStatus;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import moonstone.shopWxa.service.ShopWxaProjectWriteService;
import moonstone.shopWxa.service.ShopWxaReadService;
import moonstone.shopWxa.service.ShopWxaWriteService;
import moonstone.web.core.events.shop.ShopWxaProjectUpdateEvent;
import moonstone.web.core.shop.events.WechatAuditApplyEvent;
import moonstone.web.front.component.decoration.ShopWxaProjectWriteLogic;
import moonstone.web.front.decoration.component.WxaCategoryMaker;
import moonstone.wxOpen.dto.WxaFirstCategory;
import moonstone.wxOpen.service.WxOpenParanaComponentService;
import moonstone.wxOpen.service.WxOpenParanaMaService;
import moonstone.wxa.model.WxaTemplate;
import moonstone.wxa.service.WxaTemplateReadService;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * Created by CaiZhy on 2018/10/31.
 */
@Slf4j
@RestController
@RequestMapping("/api/wechat")
public class WechatApi {
    @Autowired
    private ShopWxaProjectWriteLogic shopWxaProjectWriteLogic;

    @Autowired
    private WxaCategoryMaker wxaCategoryMaker;

    @Autowired
    private WxOpenParanaComponentService wxOpenParanaComponentService;

    @Autowired
    private WxOpenParanaMaService wxOpenParanaMaService;

    @Autowired
    private ShopWxaReadService shopWxaReadService;

    @Autowired
    private ShopWxaWriteService shopWxaWriteService;

    @Autowired
    private ShopWxaProjectReadService shopWxaProjectReadService;

    @Autowired
    private ShopWxaProjectWriteService shopWxaProjectWriteService;

    @Autowired
    private WxaTemplateReadService wxaTemplateReadService;

    @Value("${wechat.authorize.redirectUrl}")
    private String redirectUrl;

    @Value("${wechat.authorize.resultUrl}")
    private String resultUrl;

    @Resource
    private RedissonClient redissonClient;

    private static final String SUCCESS = "0";

    @RequestMapping(value = "/auth/gotoAuthUrl", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public void gotoPreAuthUrl(HttpServletRequest request, HttpServletResponse response) {
        String url = redirectUrl;
        log.debug("[op:gotoPreAuthUrl] redirectUrl={}", url);
        try {
            url = wxOpenParanaComponentService.getPreAuthUrl(url);
            response.sendRedirect(url);
        } catch (Exception e) {
            log.error("[op:gotoPreAuthUrl] fail to goto preAuthUrl, error:", e);
            throw new JsonResponseException(e);
        }
    }

    @RequestMapping(value = "/auth/jump", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void jump(@RequestParam(value = "authCode", required = false) String authorizationCode
            , @RequestParam(value = "auth_code", required = false) String authCode
            , HttpServletResponse response) {
        try {
            BaseUser user = UserUtil.getCurrentUser();
            log.debug("{} auth callback for user[{}]", LogUtil.getClassMethodName(), Json.toJson(user));
            authorizationCode = Optional.ofNullable(authorizationCode).orElse(authCode);
            WxOpenQueryAuthResult queryAuthResult = wxOpenParanaComponentService.getQueryAuth(authorizationCode);
            Lock lock = redissonClient.getLock(WechatApi.class.getName() + "#jump");
            try {
                if (!lock.tryLock(30, TimeUnit.SECONDS)) {
                    throw Translate.exceptionOf("竞争失败");
                }
            } catch (Exception ex) {
                throw new RuntimeException(ex.getMessage(), ex.getCause());
            }
            try {
                //更新信息
                String appId = queryAuthResult.getAuthorizationInfo().getAuthorizerAppid();
                WxOpenAuthorizerInfoResult authorizerInfoResult = wxOpenParanaComponentService.getAuthorizerInfo(appId);
                WxOpenAuthorizerInfo authorizerInfo = authorizerInfoResult.getAuthorizerInfo();
                ShopWxa shopWxa = new ShopWxa();
                shopWxa.setAppId(appId);
                shopWxa.setWxaUserName(authorizerInfo.getUserName());
                shopWxa.setWxaNickName(authorizerInfo.getNickName());
                shopWxa.setWxaHeadImg(authorizerInfo.getHeadImg());
                shopWxa.setAuthorizerRefreshToken(authorizerInfoResult.getAuthorizationInfo().getAuthorizerRefreshToken());
                shopWxa.setAuthorizerAccessToken(authorizerInfoResult.getAuthorizationInfo().getAuthorizerAccessToken());
                // related the shopWxa for shop
                if (user instanceof CommonUser) {
                    ShopWxa exists = shopWxaReadService.findByAppId(shopWxa.getAppId()).getResult();
                    boolean allIsOpenProject = exists == null || shopWxaProjectReadService.findByShopWxaId(exists.getId()).getResult()
                            .stream().noneMatch(project -> project.getStatus() != -1 && Optional.ofNullable(project.getExtra()).filter(extra -> !"open".equals(extra.get("method"))).isPresent());
                    if (exists == null || allIsOpenProject && exists.getShopId() == null) {
                        Optional.ofNullable(((CommonUser) user).getShopId())
                                .ifPresent(shopWxa::setShopId);
                    }
                }
                shopWxa.setStatus(1);
                if (shopWxaReadService.findByAppId(appId).getResult() == null) {
                    shopWxaWriteService.create(shopWxa);
                }
                shopWxaWriteService.updateByAppId(shopWxa);

                log.info("[op:jump] getQueryAuth({})", Json.toJson(queryAuthResult));
                try {
                    wxOpenParanaComponentService.modifyDomain(appId);
                } catch (Exception e) {
                    log.error("{} fail to modify domain [AppId => {}]", LogUtil.getClassMethodName(), appId, e);
                    throw new RuntimeException(Translate.of("请检测是否已关联其他第三方平台, 请取消全部关联的第三方平台再试"));
                }
                createShopWxaProjectIfNotExists(shopWxa.getAppId(), authorizerInfo.getNickName());
                response.sendRedirect(this.resultUrl);
            } finally {
                lock.unlock();
            }
        } catch (Exception e) {
            log.error("[op:jump] redirect failed, error: ", e);
            throw new JsonResponseException(e);
        }
    }

    private void createShopWxaProjectIfNotExists(String appId, String name) {
        try {
            ShopWxa shopWxa = shopWxaReadService.findByAppId(appId).getResult();
            Long shopWxaId = shopWxa.getId();
            if (!shopWxaProjectReadService.findByShopWxaId(shopWxaId).getResult().isEmpty()) {
                return;
            }
            ShopWxaProject shopWxaProject = new ShopWxaProject();
            shopWxaProject.setExtra(ImmutableMap.of("method", "open"));
            shopWxaProject.setShopWxaId(shopWxaId);
            shopWxaProject.setName(Optional.ofNullable(name).orElse(Translate.of("新小程序[%s]", shopWxaId)));
            shopWxaProject.setStatus(1);
            shopWxaProjectWriteService.create(shopWxaProject);
            EventSender.sendApplicationEvent(new ShopWxaProjectUpdateEvent(shopWxaProject.getId()));
        } catch (Exception e) {
            log.error("{} fail to createShopWxaProjectIfNotExists [AppId => {}]", LogUtil.getClassMethodName(), appId, e);
        }
    }

    @RequestMapping(value = "/getAuthorizerInfo", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public WxOpenAuthorizerInfoResult getAuthorizerInfo(@RequestParam String appId) {
        try {
            return wxOpenParanaComponentService.getAuthorizerInfo(appId);
        } catch (Exception e) {
            log.error("[op:getAuthorizerInfo] fail to getAuthorizerInfo by appId={}, error: ", appId, e);
            throw new JsonResponseException(e);
        }
    }

    @RequestMapping(value = "/modifyDomain", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public String modifyDomain(@RequestParam String appId) {
        try {
            return wxOpenParanaComponentService.modifyDomain(appId);
        } catch (Exception e) {
            log.error("[op:modifyDomain] fail to modifyDomain by appId={}, error: {}", appId, e);
            throw new JsonResponseException(e);
        }
    }

    @RequestMapping(value = "/codeCommit/{shopWxaProjectId}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean codeCommit(@PathVariable Long shopWxaProjectId,
                              @RequestParam String version,
                              @RequestParam String description) {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            Response<ShopWxaProject> rShopWxaProject = shopWxaProjectReadService.findById(shopWxaProjectId);
            if (!rShopWxaProject.isSuccess()) {
                log.error("failed to find shopWxaProject by id={}, error code: {}", shopWxaProjectId, rShopWxaProject.getError());
                throw new JsonResponseException(rShopWxaProject.getError());
            }
            ShopWxaProject shopWxaProject = rShopWxaProject.getResult();

            Response<ShopWxa> rShopWxa = shopWxaReadService.findById(shopWxaProject.getShopWxaId());
            if (!rShopWxa.isSuccess()) {
                log.error("failed to find shopWxa by id={}, error code: {}", shopWxaProject.getShopWxaId(), rShopWxa.getError());
                throw new JsonResponseException(rShopWxa.getError());
            }
            ShopWxa shopWxa = rShopWxa.getResult();

            if (!Objects.equals(shopWxa.getShopId(), commonUser.getShopId())) {
                log.error("shopWxaProject(id={}) is not belong to user(Id={})", shopWxaProjectId, commonUser.getId());
                throw new JsonResponseException("shopWxaProject.not.belong.to.user");
            }

            Response<WxaTemplate> rWxaTemplate = wxaTemplateReadService.findById(shopWxaProject.getTemplateId());
            if (!rWxaTemplate.isSuccess()) {
                log.error("failed to find wxaTemplate by id={}, error code: {}", shopWxaProject.getTemplateId(), rWxaTemplate.getError());
                throw new JsonResponseException(rWxaTemplate.getError());
            }
            WxaTemplate wxaTemplate = rWxaTemplate.getResult();
            Map<String, Object> extJson = JSON.parseObject(wxaTemplate.getExtJson());
            Map<String, Object> ext = JSON.parseObject(extJson.get("ext").toString());
            ext.put("shopWxaProjectId", shopWxaProjectId);
            ext.put("shopId", shopWxa.getShopId());
            extJson.put("ext", ext);
            extJson.put("extAppid", shopWxa.getAppId());

            String response = wxOpenParanaMaService.codeCommit(wxaTemplate.getTemplateId(), version, description, JSON.toJSONString(extJson), shopWxa.getAppId());
            EventSender.sendApplicationEvent(new WechatAuditApplyEvent(shopWxaProjectId, shopWxa.getId(), wxaTemplate.getTemplateId()));
            Map<String, Object> map = JSON.parseObject(response);
            if (map.get("errcode") != null && (int) map.get("errcode") != 0) {
                log.error("failed to commit code by shopWxaProjectId={}, error code:{}", shopWxaProjectId, map.get("errmsg").toString());
                throw new JsonResponseException(map.get("errmsg").toString());
            }

            //把该shopWxa下的其它项目原本是已上传的项目变为待上传的状态，当前项目变为已上传
            shopWxaProjectWriteLogic.updateStatusByShopWxaProjectAndStatus(shopWxaProject, ShopWxaProjectStatus.UPLOADED);

            //把当前shopWxa的状态改为待提交审核
            Response<Boolean> rUpdateStatus = shopWxaWriteService.updateStatus(shopWxaProject.getShopWxaId(), ShopWxaStatus.WAITING_SUBMIT.getValue());
            if (!rUpdateStatus.isSuccess()) {
                log.error("failed to update shopWxa status by id={}, status={}, error code: {}",
                        shopWxaProject.getShopWxaId(), ShopWxaStatus.WAITING_SUBMIT.getValue(), rUpdateStatus.getError());
                throw new JsonResponseException(rUpdateStatus.getError());
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[op:codeCommit] fail to commit code by shopWxaProjectId={}, error: ", shopWxaProjectId, e);
            throw new JsonResponseException(e);
        }
    }

    @RequestMapping(value = "/getQrcode/{shopWxaId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public void getQrCode(HttpServletResponse response,
                          @PathVariable Long shopWxaId,
                          @RequestParam(required = false) String path) {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();

            Response<ShopWxa> shopWxaResponse = shopWxaReadService.findById(shopWxaId);
            if (!shopWxaResponse.isSuccess()) {
                log.error("failed to find shopWxa by id={}, error code: {}", shopWxaId, shopWxaResponse.getError());
                throw new JsonResponseException(shopWxaResponse.getError());
            }
            ShopWxa shopWxa = shopWxaResponse.getResult();

            if (!Objects.equals(commonUser.getShopId(), shopWxa.getShopId())) {
                log.error("ShopWxa( id={}) not belong to user", shopWxa.getId());
                throw new JsonResponseException("shopWxa.not.belong.to.user");
            }

            String accessToken = wxOpenParanaComponentService.getAuthorizerAccessToken(shopWxa.getAppId(), false);
            String uri = "https://api.weixin.qq.com/wxa/get_qrcode?access_token=" + accessToken;
            if (!ObjectUtils.isEmpty(path)) {
                uri += "&path=" + URLEncoder.encode(path, StandardCharsets.UTF_8);
            }
            HttpRequest httpRequest = HttpRequest.get(uri);
            if (httpRequest.ok()) {
                BufferedInputStream bufferedInputStream = httpRequest.buffer();
                ServletOutputStream out = response.getOutputStream();
                byte[] buffer = new byte[1024];
                while (bufferedInputStream.read(buffer) != -1) {
                    out.write(buffer);
                }
                out.flush();
            } else {
                log.error("[op:getQrCode] HttpRequest failed, uri: {}", uri);
                throw new JsonResponseException(500, "http.request.fail");
            }
        } catch (Exception e) {
            log.error("[op:getQrCode] fail to get QR code by shopWxaId={}, path={}, error: ", shopWxaId, path, e);
            throw new JsonResponseException(e);
        }
    }

    @RequestMapping(value = "/getPage/{shopWxaProjectId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<String> getPage(@PathVariable Long shopWxaProjectId) {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            Response<ShopWxaProject> rShopWxaProject = shopWxaProjectReadService.findById(shopWxaProjectId);
            if (!rShopWxaProject.isSuccess()) {
                log.error("failed to find shopWxaProject by id={}, error code: {}", shopWxaProjectId, rShopWxaProject.getError());
                throw new JsonResponseException(rShopWxaProject.getError());
            }

            Response<ShopWxa> rShopWxa = shopWxaReadService.findById(rShopWxaProject.getResult().getShopWxaId());
            if (!rShopWxa.isSuccess()) {
                log.error("failed to find shopWxa by id={}, error code: {}", rShopWxaProject.getResult().getShopWxaId(), rShopWxa.getError());
                throw new JsonResponseException(rShopWxa.getError());
            }
            ShopWxa shopWxa = rShopWxa.getResult();

            if (!Objects.equals(shopWxa.getShopId(), commonUser.getShopId())) {
                log.error("shopWxaProject(id={}) is not belong to user(Id={})", shopWxaProjectId, commonUser.getId());
                throw new JsonResponseException("shopWxaProject.not.belong.to.user");
            }

            WxOpenMaPageListResult wxOpenMaPageListResult = wxOpenParanaMaService.getPageList(shopWxa.getAppId());
            if (!Objects.equals(wxOpenMaPageListResult.getErrcode(), SUCCESS)) {
                log.error("failed to get page list by shopWxaProjectId={}, error code: {}", shopWxaProjectId, wxOpenMaPageListResult.getErrmsg());
                throw new JsonResponseException(wxOpenMaPageListResult.getErrmsg());
            }
            return wxOpenMaPageListResult.getPageList();
        } catch (Exception e) {
            log.error("[op:getPage] fail to get page list by shopWxaProjectId={}, error: ", shopWxaProjectId, e);
            throw new JsonResponseException(e);
        }
    }

    @RequestMapping(value = "/getCategory/{shopWxaProjectId}", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<WxaFirstCategory> getCategory(@PathVariable Long shopWxaProjectId) {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            Response<ShopWxaProject> rShopWxaProject = shopWxaProjectReadService.findById(shopWxaProjectId);
            if (!rShopWxaProject.isSuccess()) {
                log.error("failed to find shopWxaProject by id={}, error code: {}", shopWxaProjectId, rShopWxaProject.getError());
                throw new JsonResponseException(rShopWxaProject.getError());
            }

            Response<ShopWxa> rShopWxa = shopWxaReadService.findById(rShopWxaProject.getResult().getShopWxaId());
            if (!rShopWxa.isSuccess()) {
                log.error("failed to find shopWxa by id={}, error code: {}", rShopWxaProject.getResult().getShopWxaId(), rShopWxa.getError());
                throw new JsonResponseException(rShopWxa.getError());
            }
            ShopWxa shopWxa = rShopWxa.getResult();

            if (!Objects.equals(shopWxa.getShopId(), commonUser.getShopId())) {
                log.error("shopWxaProject(id={}) is not belong to user(Id={})", shopWxaProjectId, commonUser.getId());
                throw new JsonResponseException("shopWxaProject.not.belong.to.user");
            }

            WxOpenMaCategoryListResult wxOpenMaCategoryListResult = wxOpenParanaMaService.getCategoryList(shopWxa.getAppId());
            if (!Objects.equals(wxOpenMaCategoryListResult.getErrcode(), SUCCESS)) {
                log.error("failed to get category list by shopWxaProjectId={}, error code: {}", shopWxaProjectId, wxOpenMaCategoryListResult.getErrmsg());
                throw new JsonResponseException(wxOpenMaCategoryListResult.getErrmsg());
            }

            return wxaCategoryMaker.make(wxOpenMaCategoryListResult);
        } catch (Exception e) {
            log.error("[op:getPage] fail to get category list by shopWxaProjectId={}, error: ", shopWxaProjectId, e);
            throw new JsonResponseException(e);
        }
    }

    @RequestMapping(value = "/submitAudit/{shopWxaProjectId}", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Boolean submitAudit(@PathVariable Long shopWxaProjectId,
                               @RequestBody List<WxOpenMaSubmitAudit> itemList) {
        try {
            CommonUser commonUser = UserUtil.getCurrentUser();
            Response<ShopWxaProject> rShopWxaProject = shopWxaProjectReadService.findById(shopWxaProjectId);
            if (!rShopWxaProject.isSuccess()) {
                log.error("failed to find shopWxaProject by id={}, error code: {}", shopWxaProjectId, rShopWxaProject.getError());
                throw new JsonResponseException(rShopWxaProject.getError());
            }
            ShopWxaProject shopWxaProject = rShopWxaProject.getResult();

            Response<ShopWxa> rShopWxa = shopWxaReadService.findById(shopWxaProject.getShopWxaId());
            if (!rShopWxa.isSuccess()) {
                log.error("failed to find shopWxa by id={}, error code: {}", shopWxaProject.getShopWxaId(), rShopWxa.getError());
                throw new JsonResponseException(rShopWxa.getError());
            }
            ShopWxa shopWxa = rShopWxa.getResult();

            if (!Objects.equals(shopWxa.getShopId(), commonUser.getShopId())) {
                log.error("shopWxaProject(id={}) is not belong to user(Id={})", shopWxaProjectId, commonUser.getId());
                throw new JsonResponseException("shopWxaProject.not.belong.to.user");
            }

            WxOpenMaSubmitAuditMessage wxOpenMaSubmitAuditMessage = new WxOpenMaSubmitAuditMessage();
            wxOpenMaSubmitAuditMessage.setItemList(itemList);

            WxOpenMaSubmitAuditResult wxOpenMaSubmitAuditResult = wxOpenParanaMaService.submitAudit(wxOpenMaSubmitAuditMessage, shopWxa.getAppId());
            if (!Objects.equals(wxOpenMaSubmitAuditResult.getErrcode(), SUCCESS)) {
                log.error("failed to submit code to audit by shopWxaProjectId={}, itemList({}), error code: {}",
                        shopWxaProjectId, itemList, wxOpenMaSubmitAuditResult.getErrmsg());
                throw new JsonResponseException(wxOpenMaSubmitAuditResult.getErrmsg());
            }

            Map<String, String> extra = shopWxaProject.getExtra();
            extra.put("auditId", wxOpenMaSubmitAuditResult.getAuditId().toString());
            ShopWxaProject updateProject = new ShopWxaProject();
            updateProject.setId(shopWxaProject.getId());
            updateProject.setExtra(extra);

            Response<Boolean> response = shopWxaProjectWriteService.update(updateProject);
            if (!response.isSuccess() || !response.getResult()) {
                log.error("failed to update shopWxaProject({}), error code: {}", updateProject, response.getError());
                throw new JsonResponseException(response.getError());
            }

            //把该shopWxa的状态变为待微信审核
            Response<Boolean> rUpdateStatus = shopWxaWriteService.updateStatus(shopWxaProject.getShopWxaId(), ShopWxaStatus.WAITING_AUDIT.getValue());
            if (!rUpdateStatus.isSuccess()) {
                log.error("failed to update shopWxa status by id={}, status={}, error code: {}",
                        shopWxaProject.getShopWxaId(), ShopWxaStatus.WAITING_AUDIT.getValue(), rUpdateStatus.getError());
                throw new JsonResponseException(rUpdateStatus.getError());
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("[op:submitAudit] fail to submit code to audit by shopWxaProjectId={}, itemList({}), error: ", shopWxaProjectId, itemList, e);
            throw new JsonResponseException(e);
        }
    }
}
