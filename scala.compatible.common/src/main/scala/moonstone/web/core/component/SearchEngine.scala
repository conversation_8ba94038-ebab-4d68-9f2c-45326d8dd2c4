package moonstone.web.core.component

import java.util
import java.util.function.Consumer

import io.terminus.search.api.model.IndexAction
import io.terminus.search.api.query.{CriteriasBuilder, Keyword}
import io.terminus.search.api.{IndexTaskBuilder, Searcher}

import scala.reflect.ClassTag


class SearchEngine(executor: Consumer[_ >: Runnable], indexTaskBuilder: IndexTaskBuilder, searcher: Searcher) {
  //  依赖搜索模块使用的模板(模板使用mustache)
  val searchTemplate: String = "search.mustache"

  def toLowerByLine(str: String): String = {
    val builder = new StringBuilder
    str.foreach({
      case lower if lower.isLower => builder.append(lower)
      case bigger => builder.append("_" + bigger.toLower)
    })
    builder.toString() match {
      case notFit if notFit.startsWith("_") => notFit.substring(1)
      case fit => fit
    }
  }

  /**
   * 以某信息为标准查询的Id信息
   *
   * @param bucketName :String包
   * @param indexName  :String 组
   * @param query      查询条件
   * @return
   */
  def queryBy[T](bucketName: String, indexName: String, query: String, expectType: Class[T])(implicit c: ClassTag[T]): Array[T] = {

    //  使用系统依赖的搜索条件构建器
    //  其使用到一个模板构建器
    val criteriasBuilder: CriteriasBuilder = new CriteriasBuilder
    //  10000是ES限制 其无法支撑这么大的数据量
    criteriasBuilder.withPageInfo(0, 10000)
    criteriasBuilder.withKeyword(new Keyword(util.Arrays.asList("name"), query))
    searcher.searchWithAggs(toLowerByLine(bucketName), toLowerByLine(indexName), searchTemplate, criteriasBuilder.build(), expectType).getData.toArray().asInstanceOf[Array[T]]
  }

  /**
   * 获取搜索引擎内的数据,抛出SearchException 如果没有初始化过数据
   *
   * @param bucketName  :String 包
   * @param indexName   :String  组
   * @param id          id主键
   * @param targetClass 包裹类型
   */
  def getById[T](bucketName: String, indexName: String, id: Any, targetClass: Class[T])(implicit c: ClassTag[T]): Option[T] =
    searcher.searchWithAggs(toLowerByLine(bucketName), toLowerByLine(indexName), searchTemplate, new CriteriasBuilder().withKeyword(new Keyword(util.Arrays.asList("id"), id)).build(), targetClass).getData.stream().findFirst().map(Option.apply[Any]).orElse(Option.empty[Any]).asInstanceOf[Option[T]]


  /**
   * 将某个东西自动
   *
   * @param bucketName :String包
   * @param indexName  :String 组
   * @param id         Id
   * @param content    内容
   */
  def index(bucketName: String, indexName: String, id: Any, content: Any): Unit
  = executor.accept(indexTaskBuilder.indexName(toLowerByLine(bucketName))
    .indexType(toLowerByLine(indexName))
    .indexAction(IndexAction.INDEX)
    .build(id, content))
}
