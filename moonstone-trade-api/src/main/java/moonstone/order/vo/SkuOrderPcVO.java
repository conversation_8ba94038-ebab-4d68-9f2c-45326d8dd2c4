package moonstone.order.vo;


import lombok.Getter;
import lombok.Setter;


/**
 * Desc: sku维度子订单
 * Mail: <EMAIL>
 * Data: 16/2/26
 * Author: yang<PERSON>eng
 */
public class SkuOrderPcVO {

    /**
     * 销售属性id
     */
    @Getter
    @Setter
    private Long skuId;

    /**
     * 对应的店铺订单id
     */
    @Getter
    @Setter
    private Long orderId;


    /**
     * 购买数量
     */
    @Getter
    @Setter
    private Integer quantity;

    /**
     * 商品id
     */
    @Getter
    @Setter
    private Long itemId;

    /**
     * 商品名称
     */
    @Getter
    @Setter
    private String itemName;

    /**
     * 对应的主图
     */
    @Getter
    @Setter
    private String skuImage;

    /**
     * 是否保税（1：保税，0完税，2：跨境直邮（保税））
     */
    @Getter
    @Setter
    private Integer isBonded;

    /**
     * 税费
     */
    @Getter
    @Setter
    private Long tax;


    /**
     * 原价
     */
    @Getter
    @Setter
    private Long originFee;


}
