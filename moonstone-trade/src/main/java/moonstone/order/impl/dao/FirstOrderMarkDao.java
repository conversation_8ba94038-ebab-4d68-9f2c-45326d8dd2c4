package moonstone.order.impl.dao;

import com.google.common.collect.ImmutableMap;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.FirstOrderMark;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class FirstOrderMarkDao extends MyBatisDao<FirstOrderMark> {

    public List<FirstOrderMark> findByOrderIds(List<Long> orderIdList) {
        return getSqlSession().selectList(sqlId("findByOrderIds"), ImmutableMap.of("orderIdList", orderIdList));
    }
}
