package moonstone.web.core.component

import moonstone.common.factory.SpringAutoInjectAware
import moonstone.web.core.component.api.Y800Rpc
import org.springframework.beans.factory.FactoryBean
import org.springframework.context.ApplicationContext


/**
 * 洋800 的接口工厂
 *
 * 生产继承了Y800API接口的代理配置 与提供设置
 */
class Y800APIInsertFactory(key: String, secret: String, apiSupportUrl: String, context: ApplicationContext) extends SpringAutoInjectAware(applicationContext = context) with FactoryBean[Y800Rpc] {
  override def getObject: Y800Rpc = new Y800Rpc {
    override def getApiUrl: String = apiSupportUrl

    override def getKey: String = key

    override def getSecret: String = secret
  }

  override def getObjectType: Class[_] = classOf[Y800Rpc]

  override def isSingleton: Boolean = true
}
