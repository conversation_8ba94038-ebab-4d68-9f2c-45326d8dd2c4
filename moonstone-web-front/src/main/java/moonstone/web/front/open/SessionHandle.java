package moonstone.web.front.open;

import io.terminus.pampas.openplatform.annotations.OpenParamHandler;
import io.terminus.pampas.openplatform.core.ParamPreHandle;
import io.terminus.pampas.openplatform.exceptions.OPClientException;
import io.terminus.pampas.openplatform.exceptions.OPServerException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserOpenIdUtil;
import moonstone.common.utils.UserUtil;
import moonstone.user.cache.UserCacheHolder;
import moonstone.user.model.User;
import moonstone.web.core.session.HeraldSession;
import moonstone.web.core.session.SessionConfig;
import moonstone.web.core.util.ParanaUserMaker;
import moonstone.web.front.constants.Sessions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import redis.clients.jedis.JedisPool;

import java.util.Map;
import java.util.Optional;

import static io.terminus.common.utils.Arguments.isEmpty;

/**
 * 注意:server.time,get.session.id,get.user.captcher是为了兼容老版本
 * Mail: <EMAIL> <br>
 * Date: 2016-03-23 10:42 AM  <br>
 * Author: xiao
 */
@OpenParamHandler(patterns = "*", exclusions = {
        "user.register",
        "user.login",
        "server.time.get",
        "mobile.code.send",
        "get.mobile.code",
        "session.id.get",
        "user.captcha.get",
        "server.time",
        "get.session.id",
        "get.user.captcher",
        "user.password.forget",
        "user.forget.password",
        "user.auto.login"
})
@Slf4j
public class SessionHandle implements ParamPreHandle {

    @Autowired
    private UserCacheHolder userCacheHolder;
    @Autowired
    private JedisPool jedisPool;
    @Autowired
    private SessionConfig sessionConfig;

    /**
     * check params before calling
     *
     * @param params params from http request
     * @throws OPClientException fail to inject session
     */
    @Override
    public void handle(Map<String, Object> params) throws OPClientException {
        if (params.get("sid") == null || isEmpty((String) params.get("sid"))) {
            return;
        }
        String sessionId = params.get("sid").toString();
        HeraldSession session = new HeraldSession(sessionConfig.getRedisPrefix(), sessionId, jedisPool, sessionConfig.getCookieMaxAge(), null);
        if (session.getAttribute(Sessions.USER_ID) == null) {
            throw new OPClientException(400, "session.id.expired");
        }
        // refresh
        Long uid = Long.parseLong(session.getAttribute(Sessions.USER_ID).toString());
        Optional<User> findUser = userCacheHolder.findByUserId(uid);
        if (!findUser.isPresent()) {
            throw new OPServerException("user.not.found");
        }

        CommonUser commonUser = ParanaUserMaker.from(findUser.get());
        UserUtil.putCurrentUser(commonUser);
        String openId = session.getAttribute(Sessions.OPEN_ID).toString();
        if (!ObjectUtils.isEmpty(openId)) {
            UserOpenIdUtil.putOpenId(openId);
        }
    }
}
