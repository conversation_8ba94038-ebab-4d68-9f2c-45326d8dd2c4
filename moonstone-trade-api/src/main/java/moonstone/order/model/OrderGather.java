package moonstone.order.model;

import io.terminus.common.model.BaseUser;
import moonstone.common.enums.OrderOutFrom;
import moonstone.item.model.Sku;
import moonstone.order.api.OrderGatherFactory;
import moonstone.order.dto.RichGatherOrder;
import moonstone.order.enu.GatherType;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

public abstract class OrderGather {

    protected OrderGatherFactory factory;
    List<ShopOrder> gatheredOrderList = new ArrayList<>();

    public List<ShopOrder> getGatheredOrderList() {
        return gatheredOrderList;
    }

    /**
     * @param shopOrder 将要被聚合的定单
     * @return 无法聚合的订单
     */
    public Optional<ShopOrder> gather(ShopOrder shopOrder) {
        return factory.gather(this, shopOrder);
    }

    public OrderOutFrom outFrom() {
        return factory.outFrom();
    }

    /**
     * 打包出一个采购聚合单
     *
     * @param buyer          买家
     * @param shopId         店铺Id
     * @param skuOrderReader 子订单读取器
     * @param skuReader      单品读取器
     * @return 聚合单
     */
    public RichGatherOrder react(BaseUser buyer, Long shopId, Function<Long, List<SkuOrder>> skuOrderReader, Function<Long, Sku> skuReader) {
        RichGatherOrder richGatherOrder = new RichGatherOrder();
        richGatherOrder.setGatherOrder(new GatherOrder());
        richGatherOrder.setSkuOrderMapByOrderId(new HashMap<>());
        richGatherOrder.setSkuMapById(new HashMap<>());
        richGatherOrder.setShopOrderList(getGatheredOrderList());
        for (ShopOrder shopOrder : getGatheredOrderList()) {
            List<SkuOrder> skuOrderList = skuOrderReader.apply(shopOrder.getId());
            richGatherOrder.getSkuOrderMapByOrderId().put(shopOrder.getId(), skuOrderList);
            for (SkuOrder skuOrder : skuOrderList) {
                Sku sku = skuReader.apply(skuOrder.getSkuId());
                richGatherOrder.getSkuMapById().put(sku.getId(), sku);
            }
        }
        GatherOrder gatherOrder = richGatherOrder.getGatherOrder();
        gatherOrder.setBuyerId(buyer.getId());
        gatherOrder.setShopId(shopId);
        switch (outFrom()) {
            case LEVEL_Distribution:
                gatherOrder.setType(GatherType.Proxy.getType());
                break;
            case WE_SHOP:
                gatherOrder.setType(GatherType.WeShop.getType());
                break;
            default:
                gatherOrder.setType(GatherType.Common.getType());
        }
        gatherOrder.setStatus(0);
        gatherOrder.setOutFrom(outFrom().Code());
        // init fee
        gatherOrder.setFee(0L);
        gatherOrder.setTax(0L);
        gatherOrder.setOriginFee(0L);
        // 最后装饰一下
        decorate(gatherOrder);
        return richGatherOrder;
    }

    protected void decorate(GatherOrder gatherOrder) {
        // redefine it if you need
    }
}
