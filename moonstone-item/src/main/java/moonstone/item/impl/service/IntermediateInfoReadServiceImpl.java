package moonstone.item.impl.service;

import com.alibaba.fastjson.JSON;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.ThirdIntermediateType;
import moonstone.common.model.Either;
import moonstone.common.utils.LogUtil;
import moonstone.item.impl.dao.IntermediateInfoDao;
import moonstone.item.model.IntermediateInfo;
import moonstone.item.service.IntermediateInfoReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/22 18:20
 */
@Service
@RpcProvider
@Slf4j
public class IntermediateInfoReadServiceImpl implements IntermediateInfoReadService {

    @Autowired
    IntermediateInfoDao intermediateInfoDao;

    @Override
    public Either<List<IntermediateInfo>> findByThirdAndType(Long thirdId, Integer type) {
        try {
            return Either.ok(intermediateInfoDao.findByThirdAndType(thirdId, type));
        } catch (Exception ex) {
            log.error("{} thirdId:{} type:{}", LogUtil.getClassMethodName(), thirdId, type, ex);
            return Either.error(ex, "fail");
        }
    }

    @Override
    public Either<List<IntermediateInfo>> findByThirdAndType(Long thirdId, Integer type, Integer matchingType) {
        try {
            return Either.ok(intermediateInfoDao.findByThirdAndType(thirdId, type, matchingType));
        } catch (Exception ex) {
            log.error("IntermediateInfoReadServiceImpl.findByThirdAndType error, thirdId={}, type={}, matchingType={}",
                    thirdId, type, matchingType, ex);
            return Either.error(ex, "fail");
        }
    }

    @Override
    public Either<List<IntermediateInfo>> findAllByThirdAndType(Long thirdId, Integer type) {
        try {
            return Either.ok(intermediateInfoDao.findAllByThirdAndType(thirdId, type));
        } catch (Exception ex) {
            log.error("IntermediateInfoReadServiceImpl.findAllByThirdAndType error, thirdId={}, type={}, matchingType={}",
                    thirdId, type, ex);
            return Either.error(ex, "fail");
        }
    }

    @Override
    public Either<Boolean> exists(Long thirdId, int type) {
        try {
            return Either.ok(intermediateInfoDao.exists(thirdId, type));
        } catch (Exception ex) {
            log.error("{} thirdId:{} type:{}", LogUtil.getClassMethodName(), thirdId, type, ex);
            return Either.error(ex);
        }
    }

    @Override
    public Either<IntermediateInfo> findWithActivityByThirdAndType(Long thirdId, ThirdIntermediateType type) {
        try {
            if (thirdId == null || type == null) {
                return Either.error("入参皆不能为空");
            }

            return Either.ok(intermediateInfoDao.findWithActivityByThirdAndType(thirdId, type.getValue()));
        } catch (Exception ex) {
            log.error("IntermediateInfoReadServiceImpl.findWithActivityByThirdAndType error, thirdId={}, type={}",
                    thirdId, type, ex);
            return Either.error(ex.getMessage());
        }
    }

    @Override
    public Either<List<IntermediateInfo>> findByThirdIdsAndType(List<Long> thirdIdList, ThirdIntermediateType type) {
        try{
            if(CollectionUtils.isEmpty(thirdIdList) || type==null){
                return Either.error("入参皆不能为空");
            }

            return Either.ok(intermediateInfoDao.findByThirdIdsAndType(thirdIdList, type.getValue()));
        }catch (Exception ex){
            log.error("IntermediateInfoReadServiceImpl.findByThirdIdsAndType error, thirdIdList={}, type={}",
                    JSON.toJSONString(thirdIdList), type, ex);
            return Either.error(ex.getMessage());
        }
    }
}
