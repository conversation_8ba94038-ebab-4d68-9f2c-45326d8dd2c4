package moonstone.web.front.profit.domain.withdraw.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.umf.api.service.UmfService;
import com.umf.api.service.UmfServiceImpl;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.order.api.BankAccountJudge;
import moonstone.order.enu.PaymentChannelEnum;
import moonstone.order.enu.WithdrawPaymentStatusEnum;
import moonstone.order.model.WithdrawPayment;
import moonstone.web.core.constants.EnvironmentConfig;
import moonstone.web.front.profit.application.bo.WithdrawPaymentBO;
import moonstone.web.front.profit.application.bo.WithdrawPaymentCallbackBO;
import moonstone.web.front.profit.application.bo.WithdrawPaymentCallbackResultBO;
import moonstone.web.front.profit.domain.withdraw.WithdrawPaymentPay;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 联动支付 - umf
 * <p>
 * <br/>umf接口文档: https://xy.umfintech.com/umf_api/payment.html#
 */
@Slf4j
@Service
public class WithdrawPaymentUMFPay implements WithdrawPaymentPay {

    @Resource
    private BankAccountJudge bankAccountJudge;

    @Autowired
    private EnvironmentConfig environmentConfig;

    @Value("${withdraw.apply.onlinePay.callbackUrl}")
    private String callBackUrl;

    private static final List<String> SUCCESS_CODES = Lists.newArrayList("0000", "********", "********");

    private static final String KEY_RET_MSG = "ret_msg";
    private static final String KEY_RET_CODE = "ret_code";
    private static final String KEY_TRADE_NO = "trade_no";
    private static final String KEY_TRADE_STATE = "trade_state";
    private static final String VALUE_TRADE_STATE_FAILURE = "3";
    private static final String VALUE_TRADE_STATE_SUCCESS = "4";

    @Override
    public PaymentChannelEnum getSupportChannel() {
        return PaymentChannelEnum.UMF;
    }

    @Override
    public void pay(WithdrawPaymentBO paymentBO) {
        // 构造入参
        Map requestMap = buildRequestMap(paymentBO);

        // 初始化sdk服务
        var shopPayInfo = paymentBO.getShopWithdrawPayInfo();
        UmfService instance = new UmfServiceImpl(shopPayInfo.getMerchantId(), shopPayInfo.getCertFilePath());

        // 发起调用
        log.info("WithdrawPaymentUMFPay.pay, withdrawPaymentId={}发起支付请求, requestMap={}",
                paymentBO.getWithdrawPayment().getId(), JSON.toJSONString(requestMap));
        Map responseMap = paymentOrderMap(instance, requestMap);
        log.info("WithdrawPaymentUMFPay.pay, withdrawPaymentId={}发起支付请求的响应, responseMap={}",
                paymentBO.getWithdrawPayment().getId(), JSON.toJSONString(responseMap));

        // 更新 payment
        updateWithdrawPayment(paymentBO, requestMap, responseMap);
    }

    @Override
    public WithdrawPaymentCallbackResultBO parsePayResult(WithdrawPaymentCallbackBO callbackBO) throws Exception {
        String requestString = callbackBO.getHttpServletRequest().getQueryString();

        // 初始化sdk服务
        var shopWithdrawPayInfo = callbackBO.getPaymentBO().getShopWithdrawPayInfo();
        UmfService service = new UmfServiceImpl(shopWithdrawPayInfo.getMerchantId(), shopWithdrawPayInfo.getCertFilePath());

        // 解析回调结果
        Map resultMap = notifyDataParserMap(service, requestString);

        // 返回联动字符串参数
        String responseMetaString = responseUMFMap(service, resultMap);

        // 转换模型
        log.info("WithdrawPaymentUMFPay.parsePayResult, withdrawPaymentId={}解析回调结果, resultMap={}, responseMetaString={}",
                callbackBO.getPaymentBO().getWithdrawPayment().getId(), JSON.toJSONString(requestString), responseMetaString);
        return convert(callbackBO.getPaymentBO().getWithdrawPayment(), resultMap, responseMetaString);
    }

    @Override
    public void answerCaller(HttpServletResponse response, WithdrawPaymentCallbackResultBO result) throws IOException {
        response.setContentType("text/html;charset=utf-8");

        var out = response.getWriter();
        out.println("<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.01 Transitional//EN\">");
        out.println("<HTML>");
        out.println("<HEAD><META NAME=\"MobilePayPlatform\" CONTENT=\"" + result.getSendBackToCaller() + "\" /></HEAD>");
        out.println("<BODY>");
        out.println("</BODY>");
        out.println("</HTML>");
        out.flush();
        out.close();
    }

    @Override
    public WithdrawPaymentCallbackResultBO queryPayResult(WithdrawPayment withdrawPayment) {
        throw new RuntimeException("not supported");
    }

    private Map notifyDataParserMap(UmfService service, String requestString) throws Exception {
        if (!environmentConfig.isOnline()) {
            // 非线上环境，umf也没有测试环境可用，随便mock一下
            return mockCallbackMap(requestString);
        }

        return service.notifyDataParserMap(requestString);
    }

    private String responseUMFMap(UmfService service, Map resultMap) {
        if (!environmentConfig.isOnline()) {
            // 非线上环境，umf也没有测试环境可用，随便mock一下
            return "Mock-ResponseMetaString";
        }

        return service.responseUMFMap(resultMap);
    }

    private Map<String, String> mockCallbackMap(String requestString) {
        String[] strArray = requestString.split("&");
        var ht = new HashMap<String, String>();

        for (int i = 0; i < strArray.length; ++i) {
            try {
                String[] sliArray = strArray[i].split("=", 2);
                ht.put(sliArray[0], URLDecoder.decode(sliArray[1], "UTF-8"));
            } catch (UnsupportedEncodingException ex) {
                log.error("WithdrawPaymentUMFPay.mockCallbackMap error ", ex);
            }
        }

        return ht;
    }

    private Map paymentOrderMap(UmfService instance, Map requestMap) {
        if (!environmentConfig.isOnline()) {
            // 非线上环境，umf也没有测试环境可用，随便mock一下
            return mockResponseMap(requestMap);
        }

        return instance.paymentOrderMap(requestMap);
    }

    private Map mockResponseMap(Map requestMap) {
        Map<String, String> map = new HashMap<>();

        map.put(KEY_TRADE_NO, "Mock" + System.currentTimeMillis());
        if (Long.parseLong(getByKey(requestMap, "amount")) % 2 != 0) {
            map.put(KEY_RET_CODE, "0000");
        } else {
            map.put(KEY_RET_CODE, "123456789");
            map.put(KEY_RET_MSG, "因为你的支付金额是偶数，所以失败了");
        }

        return map;
    }

    /**
     * 转换模型
     *
     * @param withdrawPayment
     * @param resultMap
     * @param responseMetaString
     * @return
     */
    private WithdrawPaymentCallbackResultBO convert(WithdrawPayment withdrawPayment, Map resultMap, String responseMetaString) {
        var resultBO = new WithdrawPaymentCallbackResultBO();

        resultBO.setPaySuccess(VALUE_TRADE_STATE_SUCCESS.equals(getByKey(resultMap, KEY_TRADE_STATE)));
        resultBO.setParsedCallBackString(JSON.toJSONString(resultMap));
        resultBO.setSendBackToCaller(responseMetaString);
        resultBO.setPayChannel(withdrawPayment.getPayChannel());

        // 错误信息
        if (!resultBO.getPaySuccess()) {
            String sb = getByKey(resultMap, KEY_RET_CODE) +
                    "," +
                    getByKey(resultMap, KEY_RET_MSG);
            resultBO.setErrorMessage(sb);
        }

        return resultBO;
    }

    private void updateWithdrawPayment(WithdrawPaymentBO paymentBO, Map requestMap, Map responseMap) {
        var withdrawPayment = paymentBO.getWithdrawPayment();

        withdrawPayment.setPayRequest(JSON.toJSONString(requestMap));
        withdrawPayment.setPayResponse(JSON.toJSONString(responseMap));
        withdrawPayment.setTradeNo(getByKey(responseMap, KEY_TRADE_NO));
        if (isRequestSuccess(responseMap)) {
            withdrawPayment.setStatus(WithdrawPaymentStatusEnum.PAY_IN_PROGRESS.getCode());
        } else {
            withdrawPayment.setStatus(WithdrawPaymentStatusEnum.FAILURE.getCode());
            paymentBO.setPayErrorMessage(getByKey(responseMap, KEY_RET_MSG));
        }
    }

    private String getByKey(Map responseMap, String key) {
        if (responseMap == null) {
            return null;
        }

        var value = responseMap.get(key);
        if (value == null) {
            return null;
        }

        return value.toString();
    }

    private boolean isRequestSuccess(Map responseMap) {
        var returnCode = responseMap.get(KEY_RET_CODE);
        if (returnCode == null) {
            throw new RuntimeException("支付平台返回的结果码为空");
        }

        return SUCCESS_CODES.contains(returnCode.toString());
    }

    private Map buildRequestMap(WithdrawPaymentBO paymentBO) {
        Map reqMap = new HashMap();

        var shopPayInfo = paymentBO.getShopWithdrawPayInfo();
        var payment = paymentBO.getWithdrawPayment();
        var receiverAccount = paymentBO.getReceiver();
        var bankAccountProperty = BankAccountProperty.getCodeByUserRole(paymentBO.getUserRole());

        // 商户编号
        reqMap.put("mer_id", shopPayInfo.getMerchantId());
        // 服务器异步通知页面路径
        reqMap.put("notify_url", callBackUrl + "/" + payment.getWithdrawApplyId() + "/payCallback/" + payment.getId());
        // 商户订单号
        reqMap.put("order_id", payment.getOrderNo());
        // 原商户订单日期
        reqMap.put("mer_date", payment.getOrderDate());
        // 付款金额
        reqMap.put("amount", payment.getAmount().toString());

        // 收款方账户类型 00:银行卡，02：U付账号 （当前只会是银行卡）
        reqMap.put("recv_account_type", "00");
        // 收款方账户属性 银行账户属性 0：对私，1：对公
        reqMap.put("recv_bank_acc_pro", bankAccountProperty.getCode());
        // 收款方账号,
        reqMap.put("recv_account", receiverAccount.getAccountNo());
        // 收款方户名,
        reqMap.put("recv_user_name", receiverAccount.getAccountUserName());

        if (BankAccountProperty.OF_PUBLIC.equals(bankAccountProperty)) {
            // 收款方账户的发卡行, 对公必填
            var bankType = bankAccountJudge.judge(receiverAccount.getAccountNo());
            reqMap.put("recv_gate_id", bankType != null ? bankType.getCode() : StringUtils.EMPTY);

            // 开户行支行全称, 对公必填
            reqMap.put("bank_brhname", receiverAccount.getIssuingBank());
        }

        // 摘要, 不能超过超过10个汉字，多余将拒绝
        reqMap.put("purpose", "提现申请单线上支付");
        // 付款实时和非实时标识, 0-实时，1-非实时
        reqMap.put("checkFlag", "0");

        return reqMap;
    }

    /**
     * 银行账户属性
     */
    private enum BankAccountProperty {
        OF_PRIVATE("0", "对私"),
        OF_PUBLIC("1", "对公"),
        ;

        private String code;
        private String description;

        BankAccountProperty(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static BankAccountProperty getCodeByUserRole(SubStoreUserIdentityEnum userIdentity) {
            return switch (userIdentity) {
                case STORE_GUIDER, SUB_STORE -> OF_PRIVATE;
                case SERVICE_PROVIDER -> OF_PUBLIC;
                default -> null;
            };
        }
    }
}
