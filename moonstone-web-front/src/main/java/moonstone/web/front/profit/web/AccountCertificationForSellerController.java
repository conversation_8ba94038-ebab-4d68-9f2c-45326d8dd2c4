package moonstone.web.front.profit.web;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.api.Result;
import moonstone.common.model.CommonUser;
import moonstone.common.model.image.slice.ImageViewBackend;
import moonstone.common.utils.*;
import moonstone.user.application.UserCertificationForWithdrawSellerApp;
import moonstone.user.criteria.AccountCertificationCriteria;
import moonstone.user.dto.AccountCertificationForSellerVO;
import moonstone.user.ext.UserTypeBean;
import moonstone.web.front.profit.event.UserCertificationAuthedEvent;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/api/profit/certification")
public class AccountCertificationForSellerController {
    private final UserTypeBean userTypeBean;
    private final UserCertificationForWithdrawSellerApp userCertificationForWithdrawSellerApp;

    @GetMapping("/paging")
    public APIResp<List<AccountCertificationForSellerVO>> paging(HttpServletRequest request, AccountCertificationCriteria criteria) {
        String host = request.getHeader("Host");
        String protocol = request.isSecure() ? "https://" : "http://";
        // ImageViewBackend.App.putLocalBackend(protocol + host);
        log.debug("{} get host [{}] protocol [{}] reward [{}]", LogUtil.getClassMethodName(), host, protocol, request.getHeader("X-Forwarded-For"));
        try {
            CommonUser user = UserUtil.getCurrentUser();
            if (Objects.isNull(user)) {
                return APIResp.notLogin();
            }
            if (!userTypeBean.isSeller(user)) {
                return APIResp.error(Translate.of("权限不足"));
            }
            criteria.setShopId(user.getShopId());
            return APIRespWrapper.wrapPaging(userCertificationForWithdrawSellerApp.paging(criteria, user.getId()), criteria.getPageNo(), criteria.getPageSize());
        } catch (Exception exception) {
            log.error("{} fail to paging by [{}]", LogUtil.getClassMethodName(), criteria.toString());
            return APIResp.error(Translate.of("查询失败"));
        } finally {
            ImageViewBackend.App.clearLocal();
        }
    }

    /**
     * llyj
     *
     * @param criteria
     * @return
     */
    @GetMapping("/paging/new")
    public Result<DataPage<AccountCertificationForSellerVO>> pagingNew(AccountCertificationCriteria criteria) {
        try {
            CommonUser user = UserUtil.getCurrentUser();
            if (Objects.isNull(user)) {
                return Result.fail(-1001, "未登录");
            }
            if (!userTypeBean.isSeller(user)) {
                return Result.fail(Translate.of("权限不足"));
            }
            criteria.setShopId(user.getShopId());
            return Result.data(DataPage.build(TotalCalculation.build(criteria.getPageSize(), criteria.getPageNo(), userCertificationForWithdrawSellerApp.paging(criteria, user.getId()).getResult().getTotal()),
                    userCertificationForWithdrawSellerApp.paging(criteria, user.getId()).getResult().getData()));
        } catch (Exception exception) {
            log.error("{} fail to paging by [{}]", LogUtil.getClassMethodName(), criteria.toString());
            return Result.fail(Translate.of("查询失败"));
        }
    }

    @RequestMapping(method = {RequestMethod.POST, RequestMethod.PUT}, value = "/{id}/auth")
    public APIResp<Boolean> auth(@PathVariable("id") Long id, Boolean accept, String reason) {
        try {
            CommonUser user = UserUtil.getCurrentUser();
            if (Objects.isNull(user)) {
                return APIResp.notLogin();
            }
            if (!userTypeBean.isSeller(user)) {
                return APIResp.error(Translate.of("权限不足"));
            }
            return userCertificationForWithdrawSellerApp.auth(id, accept, reason)
                    .ifSuccess(res -> EventSender.sendApplicationEvent(new UserCertificationAuthedEvent(id, accept, reason)))
                    .map(APIResp::ok).orElseGet(() -> APIResp.error(Translate.of("审核失败")));
        } catch (Exception exception) {
            log.error("{} fail to auth [Id=>{}] accept[{}] reason[{}]", LogUtil.getClassMethodName(), id, accept, reason, exception);
            return APIResp.error(Translate.of("审核失败"));
        }
    }
}
