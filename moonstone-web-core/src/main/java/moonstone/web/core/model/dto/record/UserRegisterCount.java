package moonstone.web.core.model.dto.record;

import lombok.AllArgsConstructor;
import lombok.Getter;
import moonstone.common.api.ARecordInsert;
import moonstone.common.api.IRecordInsert;
import moonstone.common.enums.OrderOutFrom;

@Getter
@AllArgsConstructor
public abstract class UserRegisterCount extends ARecordInsert {
    private final String key;

    public static IRecordInsert build(Long shopId, OrderOutFrom orderOutFrom) {
        return new UserRegisterCount(String.format("Member_Reg_%s_%s", shopId, orderOutFrom.Code())) {
        };
    }
}
