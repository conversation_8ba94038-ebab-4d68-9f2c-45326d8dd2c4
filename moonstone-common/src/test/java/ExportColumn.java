import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

/**
 * DATE: 16/11/21 下午3:46 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Data
@XmlRootElement(name = "column")
@XmlAccessorType(value = XmlAccessType.FIELD)
public class ExportColumn implements Serializable {

    @XmlAttribute
    private String name;
    @XmlAttribute
    private String display;
    @XmlAttribute
    private Integer width;
    @XmlAttribute
    private String format;
    @XmlAttribute
    private Boolean img;
}
