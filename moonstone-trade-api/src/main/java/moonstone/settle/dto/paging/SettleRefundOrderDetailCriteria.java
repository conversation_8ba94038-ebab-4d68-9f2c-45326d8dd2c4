package moonstone.settle.dto.paging;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;
import org.joda.time.DateTime;

import java.io.Serializable;
import java.util.Date;

/**
 * 退款单明细 查询条件
 * Created with IntelliJ IDEA
 * Author: songrenfei
 * Date: 7/21/16
 * Time: 3:14 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SettleRefundOrderDetailCriteria extends PagingCriteria implements Serializable {


    private static final long serialVersionUID = -994630722221966544L;
    /**
     * 退款起始时间
     */
    private Date refundStartAt;

    /**
     * 退款截止时间
     */
    private Date refundEndAt;

    /**
     * 退款起始时间戳
     */
    private Long refundStartAtTimeStamp;
    /**
     * 退款截止时间戳
     */
    private Long refundEndAtTimestamp;

    /**
     * 对账状态 {@link moonstone.settle.enums.CheckStatus}
     */
    private Integer checkStatus;

    /**
     * 对账起始时间
     */
    private Date checkAtStart;

    /**
     * 对账截止时间
     */
    private Date checkAtEnd;

    /**
     * 对账起始时间戳
     */
    private Long checkAtStartTimeStamp;
    /**
     * 对账截止时间戳
     */
    private Long checkAtEndTimeStamp;

    /**
     * 汇总开始时间
     */
    private Date sumAtStart;

    /**
     * 汇总截止时间
     */
    private Date sumAtEnd;

    /**
     * 汇总起始时间戳
     */
    private Long sumAtStartTimeStamp;

    /**
     * 汇总截止时间戳
     */
    private Long sumAtEndTimeStamp;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 商家ID
     */
    private Long sellerId;

    /**
     * 退款单明细类型
     */
    private Integer refundDetailType;

    /**
     * 如果Start的时间和End的时间一致, 则End+1day
     */
    @Override
    public void formatDate(){
        if(refundStartAt != null && refundEndAt != null){
            if(refundStartAt.equals(refundEndAt)){
                refundEndAt=new DateTime(refundEndAt.getTime()).plusDays(1).minusSeconds(1).toDate();
            }
        }

        if(checkAtStart != null && checkAtEnd != null){
            if(checkAtStart.equals(checkAtEnd)){
                checkAtEnd=new DateTime(checkAtEnd.getTime()).plusDays(1).minusSeconds(1).toDate();
            }
        }

        if(sumAtStart != null && sumAtEnd != null){
            if(sumAtStart.equals(sumAtEnd)){
                sumAtEnd=new DateTime(sumAtEnd.getTime()).plusDays(1).minusSeconds(1).toDate();
            }
        }
    }


}
