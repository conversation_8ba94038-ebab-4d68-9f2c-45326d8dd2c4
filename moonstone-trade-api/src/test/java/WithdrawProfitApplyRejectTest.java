import lombok.extern.slf4j.Slf4j;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.domain.AbstractWithdrawApplyDomain;
import org.junit.Test;

@Slf4j
public class WithdrawProfitApplyRejectTest {
    @Test
    public void errorReject(){
        WithDrawProfitApply apply = new WithDrawProfitApply();
        apply.setStatus(WithDrawProfitApply.WithdrawExtraStatus.ERROR.getMaskBit());
        apply.initAuth();
        assert apply.reject();
        log.debug("{}", apply.getStatus());
    }

    @Test
    public void domainErrorReject() {
        WithDrawProfitApply apply = new WithDrawProfitApply();
        AbstractWithdrawApplyDomain domain = AbstractWithdrawApplyDomain.build(apply);
        apply.setStatus(WithDrawProfitApply.WithdrawExtraStatus.ERROR.getMaskBit());
        domain.initAuth();
        domain.reject();
        log.debug("{}", apply.getStatus());
    }
}
