package moonstone.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RefundProcessRecordOperatorTypeEnum {

    /**
     * 系统
     */
    SYSTEM(10, "系统"),

    /**
     * 商家
     */
    SELLER(20, "商家"),

    /**
     * 买家
     */
    BUYER(30, "买家");
    private final Integer code;
    private final String desc;


	public static String getDesc(Integer operatorType) {
		for (RefundProcessRecordOperatorTypeEnum refundProcessRecordType : RefundProcessRecordOperatorTypeEnum.values()) {
			if (refundProcessRecordType.getCode().equals(operatorType)) {
				return refundProcessRecordType.getDesc();
			}
		}
		return "";
	}
}
