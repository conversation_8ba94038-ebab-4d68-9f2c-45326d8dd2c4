package moonstone.web.core.refund.application;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.Either;
import moonstone.item.emu.SkuTagIndex;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.SkuOrderReadService;
import moonstone.web.core.mirror.app.RemoteApiOfGongXiao;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;

@Slf4j
@AllArgsConstructor
@Component
public class GongXiaoOrderRefundApp {
    private final RemoteApiOfGongXiao remoteApiOfGongXiao;
    private final SkuOrderReadService skuOrderReadService;

    /**
     * 向供销平台发起退款, 仅退款 不代表订单状态->可能是有关 详细设计在供销平台
     * 如果退款失败 则等到供销平台的余额足够时退款
     */
    public Either<Boolean> refundOrder(ShopOrder shopOrder) {
        for (SkuOrder skuOrder : skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult()) {
            boolean found = false;
            String[] pushSystems = Optional.ofNullable(skuOrder.getTags()).orElseGet(Collections::emptyMap).getOrDefault(SkuTagIndex.pushSystem.name(), "")
                    .split(SkuTagIndex.pushSystem.getSplitter());
            for (String pushSystem : pushSystems) {
                if (ThirdPartySystem.GongXiao.Id().toString().equals(pushSystem)) {
                    found = true;
                    break;
                }
            }
            if (!found) {
                continue;
            }
            // 发起退款申请
            return remoteApiOfGongXiao.refundOrder(shopOrder.getDeclaredId());
        }
        return Either.ok(true);
    }
}
