package moonstone.web.core.mirror.model.domain;

import lombok.Getter;
import lombok.Setter;
import moonstone.common.enums.UserRole;
import moonstone.common.model.Either;
import moonstone.common.utils.Translate;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.web.core.mirror.action.DefaultDomainAction;
import moonstone.web.core.mirror.app.RemoteApiOfGongXiao;
import moonstone.web.core.mirror.model.UserDetail;
import moonstone.web.core.mirror.model.api.*;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

/**
 * @startuml 用户注册
 * App -> UserMirrorDomain : 注册用户(手机号)
 * UserMirrorDomain -> OMSUserCenter : 注册用户(手机号)
 * OMSUserCenter -> UserMirrorDomain: 返回用户Id
 * UserMirrorDomain -> UserMirrorDomain : 创建用户, 关联OMS用户Id和微信信息
 * UserMirrorDomain <-> OMSUserCenter : 根据用户Id查询用户信息
 * UserMirrorDomain -> App : 注册完毕, 返回用户信息
 * <p>
 * <p>
 * App -> UserMirrorDomain : 登录/获取用户信息
 * UserMirrorDomain -> UserMirrorDomain : 获取目前登录的微信用户信息对应的外部用户Id
 * UserMirrorDomain <-> OMSUserCenter: 查询用户信息(用户Id)
 * UserMirrorDomain -> UserMirrorDomain : 更新用户状态
 * UserMirrorDomain -> App : 返回用户信息
 * <p>
 * 供销系统 --> App : 更新用户数据
 * App -> UserMirrorDomain : 更新用户数据
 * UserMirrorDomain -> App : 同步完毕
 * App -> 供销系统: 同步完毕
 * @enduml
 */
@DomainLifeCycle(autoDiscovery = true)
public class UserMirrorDomain implements MirrorDomain {
    @Setter
    User user = new User();
    /**
     * identify for the oms user center
     */
    @Setter
    String userId;
    @Setter
    String source;

    @Getter
    UserStatus status;

    MirrorUserPersistService mirrorUserPersistService;
    UserCenterManager userCenterManager;
    RemoteApiOfGongXiao remoteApiOfGongXiao;

    @Override
    public String getSource() {
        return source;
    }

    @Override
    public Function<String, DomainAction> parseAction() {
        return DefaultDomainAction::valueOf;
    }

    /**
     * sync the user information from userId
     *
     * @return synced user
     */
    public Either<User> sync(String userId) {
        // invoke sync to api manager
        // userCenterManager.sync()
        if (user.getId() == null) {
            user = mirrorUserPersistService.findUserByUserId(userId, source).orElse(user);
        }
        return userCenterManager.findUserByUserId(userId)
                // update the user
                .flatMap(this::update)
                // reload user
                .map(update -> user)
                .ifSuccess(syncUser -> mirrorUserPersistService.syncShop(userId, syncUser.getId(), source));
    }

    public Either<Boolean> update(UserDetail userDetail) {
        if (user.getId() == null) {
            return create(userDetail)
                    .map(id -> id > 0);
        }
        // update the user information
        User update = new User();
        update.setId(user.getId());
        update.setRoles(Optional.ofNullable(user.getRoles()).orElseGet(() -> Collections.singletonList(UserRole.BUYER.name())));
        if (Objects.equals(userDetail.getSeller(), true)) {
            update.getRoles().add(UserRole.SELLER.name());
        }
        if (Objects.equals(userDetail.getWeShopSeller(), true)) {
            update.getRoles().add(UserRole.WE_DISTRIBUTOR.name());
        }
        UserProfile profileUpdate = new UserProfile();
        profileUpdate.setUserId(user.getId());
        update.setMobile(userDetail.getMobile());
        profileUpdate.setRealName(userDetail.getName());
        return mirrorUserPersistService.updateUser(update, profileUpdate, userDetail.getUserId(), source)
                .ifSuccess(result -> setUserId(userDetail.getUserId()))
                // find the update result
                .ifSuccess(success -> mirrorUserPersistService.findUserByUserId(userId, source)
                        // sync the user
                        .ifSuccess(newUser -> BeanUtils.copyProperties(newUser, user)));
    }

    public Either<Long> create(UserDetail userDetail) {
        // create user, that's a hard part.
        // we should to check if user has already exists
        // link user with outSide user if exists
        if (user.getId() != null) {
            return mirrorUserPersistService.linkUser(user.getId(), userDetail.getUserId(), source)
                    .ifSuccess(result -> setUserId(userDetail.getUserId()))
                    .map(res -> user.getId());
        }
        // match user from outer side user's mobile
        user = mirrorUserPersistService.findUserByMobile(userDetail.getMobile(), source)
                .orElseGet(() -> mirrorUserPersistService.createUser(userDetail.getName(), userDetail.getMobile(), source).take());
        return mirrorUserPersistService.linkUser(user.getId(), userDetail.getUserId(), source)
                .ifSuccess(result -> setUserId(userDetail.getUserId()))
                .map(res -> user.getId());
    }

    public Either<String> register() {
        return userCenterManager.findUserByMobile(user.getMobile())
                .map(UserDetail::getUserId)
                .ifFail(() -> userCenterManager.register(user.getMobile())
                        .elseThrow(() -> Translate.exceptionOf("用户中心注册失败")))
                .ifSuccess(userId -> mirrorUserPersistService.linkUser(user.getId(), userId, source).take())
                .ifSuccess(userId -> mirrorUserPersistService.syncShop(userId, user.getId(), source))
                .ifSuccess(userId -> remoteApiOfGongXiao.register(userId, user.getMobile()).take());
    }

    public enum UserStatus implements DomainStatus {
        ALIVE, DIE, UNBORN
    }
}
