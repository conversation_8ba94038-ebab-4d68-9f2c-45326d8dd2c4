package moonstone.user.criteria;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import moonstone.common.model.PagingCriteria;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AccountCertificationCriteria extends PagingCriteria {
    Long userId;
    Long shopId;
    String mobile;
    String shopName;
    Integer authStatus;
}
