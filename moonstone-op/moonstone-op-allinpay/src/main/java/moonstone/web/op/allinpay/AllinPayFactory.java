package moonstone.web.op.allinpay;

import com.allinpay.sdk.OpenClient;
import com.allinpay.sdk.bean.BizParameter;
import com.allinpay.sdk.bean.OpenConfig;
import moonstone.common.api.Result;

public interface AllinPayFactory {
	Result<String> execute(OpenClient openClient, String methodName, String methodDesc, BizParameter param);

	Result<String> execute(Long shopId, String methodName, String methodDesc, BizParameter param);

	OpenClient get(Long shopId);

	OpenConfig getConfig(Long shopId);
}
