package moonstone.order.impl.service;

import io.vertx.core.json.JsonObject;
import moonstone.order.service.PayerInfoService;
import moonstone.user.model.PayerInfo;
import moonstone.user.model.PayerInfoRecord;
import org.apache.ibatis.session.SqlSession;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public record PayerInfoServiceImpl(SqlSession sqlSession) implements PayerInfoService {
  @Override
  public PayerInfo findByOrderId(Long orderId) {
    return sqlSession.selectOne("PayerInfo.findByOrderId2",
      Map.of("orderId", orderId));
  }

  @Override
  public PayerInfoRecord findRecordByOrderId(Long orderId) {
    return sqlSession.selectOne("PayerInfo.findByOrderId",
      Map.of("orderId", orderId));
  }

  @Override
  public void save(PayerInfo payerInfo) {
    sqlSession.insert("PayerInfo.create", JsonObject.mapFrom(payerInfo).getMap());
  }
}
