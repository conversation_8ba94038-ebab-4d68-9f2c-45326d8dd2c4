package moonstone.web.front.promotions;

import lombok.extern.slf4j.Slf4j;
import moonstone.cache.PromotionCacher;
import moonstone.promotion.model.Promotion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Author:cp
 * Created on 03/11/2016.
 */
@RestController
@RequestMapping("/api")
@Slf4j
public class ShopPromotions {

    private final PromotionCacher promotionCacher;

    @Autowired
    public ShopPromotions(PromotionCacher promotionCacher) {
        this.promotionCacher = promotionCacher;
    }

    @RequestMapping(value = "/shop/{id}/promotion", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public List<Promotion> findAvailablePromotionByShopId(@PathVariable("id") Long shopId) {
        return promotionCacher.findOngoingPromotionOf(shopId);
    }

}
