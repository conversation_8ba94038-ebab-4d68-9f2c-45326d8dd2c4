package moonstone.web.front.promotions.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by cp on 5/2/17.
 */
@Data
public class ShipmentPromotionRequest implements Serializable {

    private static final long serialVersionUID = -305231706096763883L;

    private Long shopId;

    private Integer channel;

    private Long receiverInfoId;

    private Long fee;

    private Integer originShipFee;

    private List<SkuOrderInfo> skuOrderInfos;

    private Long promotionId;
}
