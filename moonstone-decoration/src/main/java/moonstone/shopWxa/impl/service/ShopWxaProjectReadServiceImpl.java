package moonstone.shopWxa.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.AppTypeEnum;
import moonstone.shopWxa.impl.dao.ShopWxaProjectDao;
import moonstone.shopWxa.model.ShopWxaProject;
import moonstone.shopWxa.service.ShopWxaProjectReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/11/12.
 */
@Slf4j
@Service
@RpcProvider
public class ShopWxaProjectReadServiceImpl implements ShopWxaProjectReadService {
    @Autowired
    private ShopWxaProjectDao shopWxaProjectDao;

    @Override
    public Response<ShopWxaProject> findById(Long id) {
        try {
            ShopWxaProject shopWxaProject = shopWxaProjectDao.findById(id);
            return Response.ok(shopWxaProject);
        } catch (Exception e) {
            log.error("fail to find shopWxaProject by id={}, cause: {}", id, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("shopWxaProject.find.fail");
        }
    }

    @Override
    public Response<List<ShopWxaProject>> findByShopWxaId(Long shopWxaId) {
        try {
            List<ShopWxaProject> shopWxaProjects = shopWxaProjectDao.findByShopWxaId(shopWxaId);
            return Response.ok(shopWxaProjects);
        } catch (Exception e) {
            log.error("fail to find shopWxaProject by shopWxaId={}, cause: {}", shopWxaId, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("shopWxaProject.find.fail");
        }
    }

    @Override
    public AppTypeEnum findAppType(Long projectId) {
        try {
            return AppTypeEnum.from(shopWxaProjectDao.findAppType(projectId));
        } catch (Exception ex) {
            log.error("ShopWxaProjectReadServiceImpl.findAppType error, projectId={}", projectId, ex);
            return null;
        }
    }

    @Override
    public ShopWxaProject getOne(Map<String, Object> query) {
        return shopWxaProjectDao.selectOne(query);
    }
}
