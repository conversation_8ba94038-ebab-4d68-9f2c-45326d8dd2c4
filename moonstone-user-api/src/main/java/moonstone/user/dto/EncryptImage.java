package moonstone.user.dto;

import com.github.kevinsawicki.http.HttpRequest;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EncryptHelper;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UUID;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.BiFunction;

/**
 * <AUTHOR>
 */
@Slf4j
@Data
@NoArgsConstructor
public class EncryptImage {
    String imageUrl;
    String dataUrl;
    BufferedImage image = null;
    CompletableFuture<ByteArrayOutputStream> buff = new CompletableFuture<>();
    Map<String, String> header = new HashMap<>();

    /**
     * 生成加密校验的名字
     *
     * @param verifyKey 校验用数据
     * @return 校验名
     */
    public static String generateEncryptName(BiFunction<String, String, String> map, String verifyKey) {
        String uuidName = UUID.randomUUID().toString();
        String head = uuidName.split("-")[0];
        return map.apply(head, verifyKey);
    }


    public EncryptImage(String dataUrl) {
        this.dataUrl = dataUrl;
    }

    /**
     * 获取数据
     *
     * @param executor 执行器
     */
    public void fetchData(Executor executor) {
        image = null;
        executor.execute(() -> {
            try {
                HttpRequest httpRequest = HttpRequest.get(dataUrl);
                httpRequest.headers(header);
                log.debug("{} fetch img from[{}] with head[{}]", LogUtil.getClassMethodName(), dataUrl, header);
                if (!httpRequest.ok()) {
                    buff.completeExceptionally(new RuntimeException(Translate.of("HTTP请求失败")));
                    return;
                }
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                httpRequest.receive(byteArrayOutputStream);
                buff.complete(byteArrayOutputStream);
            } catch (Exception e) {
                buff.completeExceptionally(e);
            }
        });
    }

    /**
     * 塞入HTTP请求头
     * @param headerName      请求头Head
     * @param value     值
     */
    public synchronized void header(String headerName,String value){
        header.put(headerName, value);
    }


    /**
     * 解码数据
     */
    private void decodeData() {
        image = null;
        try {
            image = ImageIO.read(new ByteArrayInputStream(EncryptHelper.instance.decrypt(EncryptHelper.KeyEnu.CommonKey, buff.get().toByteArray()).take().toByteArray()));
        } catch (Exception exception) {
            log.error("{} fail to decode data[{}]", LogUtil.getClassMethodName(), dataUrl, exception);
        }
    }

    /**
     * 获取图片数据
     *
     * @return 图片数据
     */
    public BufferedImage image() {
        if (Objects.nonNull(image)) {
            return image;
        }
        try {
            image = ImageIO.read(new ByteArrayInputStream(buff.get().toByteArray()));
            if (Objects.nonNull(image)) {
                return image;
            }
        } catch (Exception ignore) {
        }
        decodeData();
        return image;
    }
}
