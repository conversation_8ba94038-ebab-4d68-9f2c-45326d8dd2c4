/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.search.item.impl;

import com.google.common.base.Function;
import com.google.common.base.Throwables;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import io.terminus.search.api.IndexExecutor;
import io.terminus.search.api.model.IndexTask;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.impl.dao.ItemAttributeDao;
import moonstone.item.impl.dao.ItemDao;
import moonstone.item.model.Item;
import moonstone.item.model.ItemAttribute;
import moonstone.search.dto.IndexedItem;
import moonstone.search.item.IndexedItemFactory;
import moonstone.search.item.ItemSearchWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-02-09
 */
@Service
@Slf4j
@RpcProvider
public class ItemSearchWriteServiceImpl implements ItemSearchWriteService {

    private final IndexExecutor indexExecutor;

    private final IndexedItemFactory indexedItemFactory;

    private final ItemDao itemDao;

    private final ItemAttributeDao itemAttributeDao;

    private final IndexedItemIndexAction indexedItemIndexAction;

    private final IndexedItemGuarder indexedItemGuarder;

    @Autowired
    public ItemSearchWriteServiceImpl(IndexExecutor indexExecutor,
                                      IndexedItemFactory indexedItemFactory,
                                      ItemDao itemDao,
                                      ItemAttributeDao itemAttributeDao,
                                      IndexedItemIndexAction indexedItemIndexAction,
                                      IndexedItemGuarder indexedItemGuarder) {
        this.indexExecutor = indexExecutor;
        this.indexedItemFactory = indexedItemFactory;
        this.itemDao = itemDao;
        this.itemAttributeDao = itemAttributeDao;
        this.indexedItemIndexAction = indexedItemIndexAction;
        this.indexedItemGuarder = indexedItemGuarder;
    }

    /**
     * 索引商品
     *
     * @param itemId 商品id
     * @return 是否调用成功
     */
    @Override
    public Response<Boolean> index(Long itemId) {
        try {
            Item item = itemDao.findById(itemId);
            ItemAttribute itemAttribute = itemAttributeDao.findByItemId(itemId);
            IndexedItem indexedItem = indexedItemFactory.create(item, itemAttribute);
            indexExecutor.submit(indexedItemIndexAction.indexTask(indexedItem));
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to index item(id={}), cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.index.fail");
        }
    }

    /**
     * 删除商品
     *
     * @param itemId 商品id
     * @return 是否调用成功
     */
    @Override
    public Response<Boolean> delete(Long itemId) {
        try {
            indexExecutor.submit(indexedItemIndexAction.deleteTask(itemId));
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to delete item(id={}) from index, cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.index.fail");
        }
    }

    /**
     * 索引或者删除商品
     *
     * @param itemId 商品id
     * @return 是否调用成功
     */
    @Override
    public Response<Boolean> update(Long itemId) {
        try {
            Item item = itemDao.findById(itemId);
            if(indexedItemGuarder.indexable(item)){

                ItemAttribute itemAttribute = itemAttributeDao.findByItemId(itemId);
                IndexedItem indexedItem = indexedItemFactory.create(item, itemAttribute);
                indexExecutor.submit(indexedItemIndexAction.indexTask(indexedItem));
                return Response.ok(Boolean.TRUE);
            }else{
                indexExecutor.submit(indexedItemIndexAction.deleteTask(itemId));
                return Response.ok(Boolean.TRUE);
            }
        } catch (Exception e) {
            log.error("failed to update item(id={}) from index, cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.index.fail");
        }
    }

    @Override
    public Response<Boolean> updateByShopId(Long shopId) {
        try {
            Long lastId = itemDao.maxIdByShopId(shopId)+1;
            while (true) {
                List<Item> items = itemDao.listByShopId(shopId, lastId, 200);
                if (items.isEmpty()) {
                    break;
                }

                doIndex(items);
                lastId = Iterables.getLast(items).getId();
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to update item by shopId={},cause:{}",
                    shopId, Throwables.getStackTraceAsString(e));
            return Response.fail("item.index.fail");
        }
    }

    private void doIndex(List<Item> items) {
        List<Long> itemIds = Lists.transform(items, new Function<Item, Long>() {
            @Override
            public Long apply(Item item) {
                return item.getId();
            }
        });

        List<ItemAttribute> itemAttributes = itemAttributeDao.findByItemIds(itemIds);

        Map<Long, ItemAttribute> ItemAttributebyItemIdIndex = Maps.uniqueIndex(itemAttributes, new Function<ItemAttribute, Long>() {
            @Override
            public Long apply(ItemAttribute itemId) {
                return itemId.getItemId();
            }
        });


        for (Item item : items) {
            try {
                if (indexedItemGuarder.indexable(item)) {
                    IndexedItem indexedItem = indexedItemFactory.create(item, ItemAttributebyItemIdIndex.get(item.getId()));
                    IndexTask indexTask = indexedItemIndexAction.indexTask(indexedItem);
                    indexExecutor.submit(indexTask);
                } else {
                    IndexTask indexTask = indexedItemIndexAction.deleteTask(item.getId());
                    indexExecutor.submit(indexTask);
                }
            } catch (Exception e) {
                log.error("failed to index item(id={}),cause:{}", item.getId(), Throwables.getStackTraceAsString(e));
            }
        }
    }
}
