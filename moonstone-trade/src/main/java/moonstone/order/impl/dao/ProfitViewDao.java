package moonstone.order.impl.dao;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import io.terminus.common.mysql.dao.MyBatisDao;
import moonstone.order.model.ProfitView;
import org.springframework.stereotype.Repository;

import java.util.Map;
import java.util.function.Function;

@Repository
public class ProfitViewDao extends MyBatisDao<ProfitView> implements Function<Long, ProfitView> {
    LoadingCache<Long, ProfitView> cache = Caffeine.newBuilder().maximumSize(1000)
            .build(this::findById);

    @Override
    public ProfitView apply(Long profitId) {
        return cacheId(profitId);
    }

    public ProfitView cacheId(Long profitId){
        if (profitId == null) return null;
        return cache.get(profitId);
    }

    public ProfitView createOrFind(ProfitView extraInfo) {
        var hash = extraInfo.generateHash();
        ProfitView exists = getSqlSession().selectOne(sqlId("findByHash"), Map.of("hash", hash));
        if (exists != null) {
            return exists;
        }
        if (extraInfo.hash() == null) {
            exists = new ProfitView(null, extraInfo.profitA(), extraInfo.profitB(),
                    extraInfo.profitC(), extraInfo.profitD(), extraInfo.profitE(), hash);
        }
        getSqlSession().insert(sqlId("create"), exists);
        return getSqlSession().selectOne(sqlId("findByHash"), Map.of("hash", hash));

    }
}
