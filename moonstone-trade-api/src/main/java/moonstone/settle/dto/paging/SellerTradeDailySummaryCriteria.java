package moonstone.settle.dto.paging;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;
import org.joda.time.DateTime;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * Created with IntelliJ IDEA
 * Author: songrenfei
 * Date: 7/21/16
 * Time: 3:43 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SellerTradeDailySummaryCriteria extends PagingCriteria implements Serializable {

    private static final long serialVersionUID = 4151907929343279717L;


    /**
     * 汇总起始时间
     */
    private Date sumStartAt;

    /**
     * 汇总截止时间
     */
    private Date sumEndAt;

    /**
     * 汇总起始时间戳
     */
    private Long sumStartAtTimestamp;
    /**
     * 汇总截止时间戳
     */
    private Long sumEndAtTimestamp;

    /**
     * 商家id
     */
    private Long sellerId;

    /**
     * 商家名称
     */
    private String sellerName;

    /**
     * 汇总类型
     * @see moonstone.settle.enums.SummaryType
     */
    private Integer summaryType;

    /**
     * 如果Start的时间和End的时间一致, 则End+1day
     */
    @Override
    public void formatDate(){
        if(sumStartAt != null && sumEndAt != null){
            if(sumStartAt.equals(sumEndAt)){
                sumEndAt=new DateTime(sumEndAt.getTime()).plusDays(1).minusSeconds(1).toDate();
            }
        }
        if (ObjectUtils.isEmpty(sellerName)){
            sellerName = null;
        }
    }

}
