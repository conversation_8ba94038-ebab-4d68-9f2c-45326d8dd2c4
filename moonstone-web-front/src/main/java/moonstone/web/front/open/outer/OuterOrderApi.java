package moonstone.web.front.open.outer;

import com.google.common.base.MoreObjects;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.pampas.openplatform.annotations.OpenBean;
import io.terminus.pampas.openplatform.annotations.OpenMethod;
import io.terminus.pampas.openplatform.exceptions.OPClientException;
import io.terminus.pampas.openplatform.exceptions.OPServerException;
import io.terminus.pampas.openplatform.utils.ClientUtil;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.Json;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.OrderDetail;
import moonstone.order.dto.OrderGroup;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.web.core.order.OrderReadLogic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Author:cp
 * Created on 9/14/16.
 */
@OpenBean
@Slf4j
public class OuterOrderApi {

    @RpcConsumer
    private ShopOrderReadService shopOrderReadService;

    @Autowired
    private OrderReadLogic orderReadLogic;

    @OpenMethod(key = "order.query", paramNames = {"statuses", "startAt", "endAt", "pageNo", "pageSize"}, httpMethods = RequestMethod.GET)
    public Paging<OrderGroup> findOrders(String statuses, String startAt, String endAt, Integer pageNo, Integer pageSize) {
        final Long shopId = ClientUtil.get().getClientId();

        Map<String, String> criteria = new HashMap<>();
        criteria.put("shopId", shopId.toString());
        if (StringUtils.hasText(statuses)) {
            criteria.put("statusStr", statuses);
        }
        if (StringUtils.hasText(startAt)) {
            criteria.put("startAt", startAt);
        }
        if (StringUtils.hasText(endAt)) {
            criteria.put("endAt", endAt);
        }
        criteria.put("pageNo", MoreObjects.firstNonNull(pageNo, 1).toString());
        criteria.put("size", MoreObjects.firstNonNull(pageSize, 20).toString());
        var param = Json.parseObject(Json.toJson(criteria), OrderCriteria.class);
        Response<Paging<OrderGroup>> findResp = orderReadLogic.pagingOrder(param);
        if (!findResp.isSuccess()) {
            log.error("fail to find orders for seller(shop id={}) with criteria={},cause:{}",
                    shopId, criteria, findResp.getError());
            throw new OPServerException(findResp.getError());
        }
        return findResp.getResult();
    }

    @OpenMethod(key = "order.detail.query.by.id", paramNames = {"id"}, httpMethods = RequestMethod.GET)
    public OrderDetail findOrderDetail(Long shopOrderId) {
        Response<ShopOrder> findShopOrder = shopOrderReadService.findById(shopOrderId);
        if (!findShopOrder.isSuccess()) {
            log.error("fail to find shop order by id={},cause:{}",
                    shopOrderId, findShopOrder.getError());
            throw new OPServerException(findShopOrder.getError());
        }
        ShopOrder shopOrder = findShopOrder.getResult();

        final Long shopId = ClientUtil.get().getClientId();
        if (!Objects.equals(shopOrder.getShopId(), shopId)) {
            throw new OPClientException(401, "order.not.owner");
        }

        Response<OrderDetail> findDetail = orderReadLogic.orderDetail(shopOrderId);
        if (!findDetail.isSuccess()) {
            log.error("fail to find order detail by shopOrderId={},cause:{}",
                    shopOrderId, findDetail.getError());
            throw new OPServerException(findDetail.getError());
        }
        return findDetail.getResult();
    }

}
