package moonstone.order.dto;

import lombok.Data;
import moonstone.common.utils.Translate;
import moonstone.order.dto.fsm.SkuOrderPushStatus;
import moonstone.order.model.SkuOrder;
import org.springframework.beans.BeanUtils;

import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Data
public class SkuOrderForExportView {
    private Long id;
    //sku的id
    private Long skuId;
    //商品名称
    private String itemName;
    /**
     * 货码
     */
    private String itemCode;
    //商品价格
    private String originFee;
    //数量
    private Integer quantity;
    //税费
    private String tax;
    //运费
    private String shipFee;
    //优惠金额
    private String discount;
    // 快递公司
    private String shipmentName;
    // 快递单号
    private String shipmentId;
    private String shipmentTime = "";
    //推送状态
    private String pushStatus;
    //推送后的洋800订单ID
    private String y800PushOrderId;
    //外部推送的系统名
    private String outPushSystemName;
    //推送后的外部订单ID
    private String outPushOrderId;
    private String outerSkuId;
    private String barCode;

    public static SkuOrderForExportView from(SkuOrder skuOrder) {
        SkuOrderForExportView skuOrderForExportView = new SkuOrderForExportView();
        BeanUtils.copyProperties(skuOrder, skuOrderForExportView);
        skuOrderForExportView.setTax(skuOrder.getTax() == null ? "" : "" + skuOrder.getTax() / 100.0);
        skuOrderForExportView.setOriginFee(skuOrder.getOriginFee() == null ? "" : "" + skuOrder.getOriginFee() / 100.0);
        skuOrderForExportView.setShipFee(skuOrder.getShipFee() == null ? "" : "" + skuOrder.getShipFee() / 100.0);
        skuOrderForExportView.setDiscount(skuOrder.getDiscount() == null ? "" : "" + skuOrder.getDiscount() / 100.0);
        skuOrderForExportView.outerSkuId = skuOrder.getOuterSkuId();
        switch (shapeMap(skuOrder.getTags()).getOrDefault("pushSystem", "")) {
            case "1", "2" -> {
                if (!shapeMap(skuOrder.getExtra()).getOrDefault("packageNo", "").isEmpty()) {
                    skuOrderForExportView.y800PushOrderId = shapeMap(skuOrder.getExtra()).get("packageNo");
                }
            }
            default -> {

            }
        }
        if (skuOrder.getPushStatus() == null || skuOrder.getPushStatus() == -1) {
            skuOrderForExportView.setPushStatus(new Translate("t.push.error") + " " + skuOrder.getPushErrorMsg());
        } else {
            skuOrderForExportView.setPushStatus(skuOrderForExportView.judgeStatus(skuOrder.getPushStatus()));
        }
        return skuOrderForExportView;
    }

    private static Map<String, String> shapeMap(Map<String, String> map) {
        return map == null ? new TreeMap<>() : map;
    }

    private String judgeStatus(Integer status) {
        if (status == null) {
            return "";
        }
        String unTranslateStr = Stream.of(SkuOrderPushStatus.values()).filter(_pushStatus -> Objects.equals(_pushStatus.value(), status))
                .findFirst()
                .map(Enum::name)
                .orElse("UNKNOW");
        return new Translate("t.push." + unTranslateStr.toLowerCase()).toString();
    }
}
