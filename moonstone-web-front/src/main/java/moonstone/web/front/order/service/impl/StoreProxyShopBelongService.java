package moonstone.web.front.order.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.OrderOutFrom;
import moonstone.order.api.OrderReadLogic;
import moonstone.order.model.OrderBase;
import moonstone.web.core.component.StoreProxyRegisterComponent;
import moonstone.web.front.order.domain.StoreProxyOrderBelongDomain;
import moonstone.web.front.order.domain.api.BelongShop;
import moonstone.web.front.order.service.api.ShopBelongFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * build the order belong domain for storeProxy
 */
@Component
@Slf4j
@AllArgsConstructor
public class StoreProxyShopBelongService implements ShopBelongFactory {
    private final OrderReadLogic orderReadLogic;
    private final StoreProxyRegisterComponent storeProxyRegisterComponent;

    @Override
    public BelongShop build(OrderBase order) {
        return new StoreProxyOrderBelongDomain() {
            @Override
            protected void init() {
                orderBase = order;
                orderReadLogic = StoreProxyShopBelongService.this.orderReadLogic;
                storeProxyRegisterComponent = StoreProxyShopBelongService.this.storeProxyRegisterComponent;
            }
        };
    }

    @Override
    public OrderOutFrom getOrderOutFrom() {
        return OrderOutFrom.LEVEL_Distribution;
    }
}
