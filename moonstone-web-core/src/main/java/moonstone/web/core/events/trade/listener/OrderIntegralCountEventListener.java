package moonstone.web.core.events.trade.listener;

import com.google.common.base.Objects;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.event.OrderIntegralCountEvent;
import moonstone.item.model.Item;
import moonstone.item.service.ItemReadService;
import moonstone.order.model.*;
import moonstone.order.service.*;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.user.service.StoreIntegralReadService;
import moonstone.user.service.StoreIntegralWriteService;
import moonstone.web.core.config.FunctionSwitch;
import moonstone.web.core.integral.IntegralAccount;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/7/15 15:40
 */
@SuppressWarnings("UnstableApiUsage")
@Slf4j
public class OrderIntegralCountEventListener {

    @RpcConsumer
    private PaymentReadService paymentReadService;

    @RpcConsumer
    private ShopReadService shopReadService;

    @RpcConsumer
    private PaymentWriteService paymentWriteService;

    @Autowired
    private OrderStatusUpdater orderStatusUpdater;

    @RpcConsumer
    StoreIntegralWriteService storeIntegralWriteService;

    @RpcConsumer
    StoreIntegralReadService storeIntegralReadService;

    @RpcConsumer
    private SkuOrderReadService skuOrderReadService;

    @RpcConsumer
    private ShopOrderReadService shopOrderReadService;

    @RpcConsumer
    private RefundReadService refundReadService;

    @RpcConsumer
    private OrderWriteService orderWriteService;

    @Autowired
    private FunctionSwitch functionSwitch;

    @RpcConsumer
    private ItemReadService itemReadService;

    @Autowired
    IntegralAccount integralAccount;


    /**
     * 目前只针对订单是同一个用户同一个店铺的订单
     *
     * @param orderPaymentEvent 订单支付事件
     */
    @EventListener(OrderIntegralCountEvent.class)
    public void onPayment(OrderIntegralCountEvent orderPaymentEvent) {
        Long paymentId = orderPaymentEvent.getPaymentId();
        Long refundId = orderPaymentEvent.getRefundId();

        log.info("[OrderIntegralCountEventListener] paymentId:{} refundId:{}", paymentId, refundId);
        Map<String, Long> map = new HashMap<>();//记录shopId userId  quantity
        StringBuilder integralCountIds = new StringBuilder();//记录parana_sku_orders 的 id 和 quantity ---累计礼 规则skuid+"_"+quantity+"#"
        try {
            if (paymentId > 0L) {
                //支付单
                Response<Payment> rPayment = paymentReadService.findById(paymentId);
                if (!rPayment.isSuccess()) {
                    log.error("failed to find Payment(id={}), error code:{}", paymentId, rPayment.getError());
                    return;
                }
                final Payment payment = rPayment.getResult();

                Response<List<OrderPayment>> rOrderPayments = paymentReadService.findOrderIdsByPaymentId(paymentId);
                if (!rOrderPayments.isSuccess()) {
                    log.error("failed to find orderIds for payment(id={}), error code:{}", paymentId, rOrderPayments.getError());
                    return;
                }
                List<OrderPayment> orderPayments = rOrderPayments.getResult();

                for (OrderPayment orderPayment : orderPayments) {
                    if (orderPayment.getOrderType() == 1) {
                        // 店铺级别
                        ShopOrder shopOrder = shopOrderReadService.findById(orderPayment.getOrderId()).getResult();
                        //判断积分订单不予加核减数
                        if (shopOrder == null || shopOrder.getType().equals(3)) {
                            log.info("failed to find shopOrder null or Type=3 for payment(id={}), error code:{}", paymentId, rOrderPayments.getError());
                            return;
                        }
                        log.info("shopOrder.getId：{}", shopOrder.getId());
                        List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
                        //过滤一段奶粉不参加
                        log.info("shopOrder.ShopId：{} skuOrderList.size:{}", skuOrderList.get(0).getShopId(), skuOrderList.size());
                        Shop rShop = Optional.ofNullable(shopReadService.findById(skuOrderList.get(0).getShopId()).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));
                        Map<String, String> extras = rShop.getExtra();

                        for (SkuOrder skuOrder : skuOrderList) {

                            Response<Item> itemResponse = itemReadService.findById(skuOrder.getItemId());
                            if (!itemResponse.isSuccess() || itemResponse.getResult() == null) {
                                log.error("[itemResponse  ] itemId:{}", skuOrder.getItemId());
                                return;
                            }
                            if ((extras != null
                                    && !extras.isEmpty()
                                    && extras.containsKey("startIntegral")
                                    && extras.get("startIntegral").equals("3")
                                    && Objects.equal(itemResponse.getResult().getTips(), 1)
                            )) {
                                integralCountIds.append(skuOrder.getId()).append("_").append(skuOrder.getQuantity()).append("#");
                                if (map.containsKey("quantity")) {
                                    map.put("quantity", map.get("quantity") + skuOrder.getQuantity());
                                    map.put("userId", skuOrder.getBuyerId());
                                    map.put("shopId", skuOrder.getShopId());
                                } else {
                                    map.put("quantity", Long.valueOf(skuOrder.getQuantity()));
                                    map.put("shopId", skuOrder.getShopId());
                                    map.put("userId", skuOrder.getBuyerId());
                                }
                            } else if ((extras != null
                                    && !extras.isEmpty()
                                    && extras.containsKey("startIntegral")
                                    && extras.get("startIntegral").equals("2")
                            )) {
                                if (map.containsKey("quantity")) {
                                    map.put("quantity", map.get("quantity") + skuOrder.getQuantity());
                                    map.put("userId", skuOrder.getBuyerId());
                                    map.put("shopId", skuOrder.getShopId());
                                } else {
                                    map.put("quantity", Long.valueOf(skuOrder.getQuantity()));
                                    map.put("shopId", skuOrder.getShopId());
                                    map.put("userId", skuOrder.getBuyerId());
                                }
                            }
                        }

                    } else if (orderPayment.getOrderType() == 2) {
                        // sku级别
                        SkuOrder skuOrder = skuOrderReadService.findById(orderPayment.getOrderId()).getResult();
                        if (skuOrder == null) {
                            log.info("failed to find shopOrder null or Type=3 for payment(id={}), error code:{}", paymentId, rOrderPayments.getError());
                            return;
                        }
                        ShopOrder shopOrder = shopOrderReadService.findById(skuOrder.getOrderId()).getResult();
                        //判断积分订单不予加核减数
                        if (shopOrder == null || shopOrder.getType().equals(3)) {
                            log.info("failed to find shopOrder null or Type=3 for payment(id={}), error code:{}", paymentId, rOrderPayments.getError());
                            return;
                        }
                        //过滤一段奶粉不参加
                        Shop rShop = Optional.ofNullable(shopReadService.findById(skuOrder.getShopId()).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));
                        Map<String, String> extras = rShop.getExtra();
                        Response<Item> itemResponse = itemReadService.findById(skuOrder.getItemId());
                        if (!itemResponse.isSuccess() || itemResponse.getResult() == null) {
                            log.error("[itemResponse  ] itemId:{}", skuOrder.getItemId());
                            return;
                        }
                        if ((extras != null
                                && !extras.isEmpty()
                                && extras.containsKey("startIntegral")
                                && extras.get("startIntegral").equals("3")
                                && Objects.equal(itemResponse.getResult().getTips(), 1)
                        )) {
                            integralCountIds.append(skuOrder.getId()).append("_").append(skuOrder.getQuantity()).append("#");
                            map.put("quantity", Long.valueOf(skuOrder.getQuantity()));
                            map.put("shopId", skuOrder.getShopId());
                            map.put("userId", skuOrder.getBuyerId());
                        } else if ((extras != null
                                && !extras.isEmpty()
                                && extras.containsKey("startIntegral")
                                && extras.get("startIntegral").equals("2")
                        )) {
                            map.put("quantity", Long.valueOf(skuOrder.getQuantity()));
                            map.put("shopId", skuOrder.getShopId());
                            map.put("userId", skuOrder.getBuyerId());
                        }
                    }
                }
                log.info("Optionalcount map:{}", map);
                if (map.isEmpty() || !map.containsKey("shopId")) {
                    return;
                }
                Shop rShop = Optional.ofNullable(shopReadService.findById(map.get("shopId")).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));
                Map<String, String> extra = rShop.getExtra();
                //积分商品核减数
//                if (extra != null && !extra.isEmpty()
//                        && extra.containsKey("startIntegral")
//                        && extra.get("startIntegral").equals("2")
//                ) {
//                    log.info("[OrderIntegralCountEventListener3] paymentId:{} refundId:{} startIntegral:{}", paymentId, refundId, extra.get("startIntegral"));
//                    Map<String, String> maps = new HashMap<>();
//                    maps.put("userId", String.valueOf(map.get("userId")));
//                    maps.put("shopId", String.valueOf(map.get("shopId")));
//                    maps.put("integralCount", String.valueOf(map.get("quantity")));
//                    integralAccount.initializeIntegralAccount(maps, 3);
//                }
                //积分累计商品合计数
                if (extra != null && !extra.isEmpty()
                        && extra.containsKey("startIntegral")
                        && extra.get("startIntegral").equals("3")
                ) {
                    log.info("[OrderIntegralCountEventListener5] paymentId:{} refundId:{} startIntegral:{}", paymentId, refundId, extra.get("startIntegral"));
                    Map<String, String> maps = new HashMap<>();
                    maps.put("userId", String.valueOf(map.get("userId")));
                    maps.put("shopId", String.valueOf(map.get("shopId")));
                    maps.put("integralCountIds", integralCountIds.toString());
                    integralAccount.initializeIntegralAccount(maps, 5);
                }

            } else {
                //退款单
                Response<List<OrderRefund>> rOrderRefunds = refundReadService.findOrderIdsByRefundId(refundId);
                for (OrderRefund orderRefund : rOrderRefunds.getResult()) {
                    if (orderRefund.getOrderType() == 1) {
                        // 店铺级别
                        ShopOrder shopOrder = shopOrderReadService.findById(orderRefund.getOrderId()).getResult();
                        List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderId(shopOrder.getId()).getResult();
                        if (ObjectUtils.isEmpty(skuOrderList.get(0).getShopId())) {
                            log.error("shopId IS ERROR SHOPOrderId:{}", shopOrder.getId());
                            return;
                        }
                        //过滤一段奶粉不参加
                        Shop rShop = Optional.ofNullable(shopReadService.findById(skuOrderList.get(0).getShopId()).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));
                        Map<String, String> extras = rShop.getExtra();

                        for (SkuOrder skuOrder : skuOrderList) {
                            Response<Item> itemResponse = itemReadService.findById(skuOrder.getItemId());
                            if (!itemResponse.isSuccess() || itemResponse.getResult() == null) {
                                log.error("[itemResponse  ] itemId:{}", skuOrder.getItemId());
                                return;
                            }
                            if ((extras != null
                                    && !extras.isEmpty()
                                    && extras.containsKey("startIntegral")
                                    && extras.get("startIntegral").equals("3")
                                    && Objects.equal(itemResponse.getResult().getTips(), 1)
                            )) {
                                integralCountIds.append(skuOrder.getId()).append("_").append(skuOrder.getQuantity()).append("#");
                                if (map.containsKey("quantity")) {
                                    map.put("quantity", map.get("quantity") + skuOrder.getQuantity());
                                    map.put("userId", skuOrder.getBuyerId());
                                    map.put("shopId", skuOrder.getShopId());
                                } else {
                                    map.put("quantity", Long.valueOf(skuOrder.getQuantity()));
                                    map.put("userId", skuOrder.getBuyerId());
                                    map.put("shopId", skuOrder.getShopId());
                                }
                            } else if (extras != null
                                    && !extras.isEmpty()
                                    && extras.containsKey("startIntegral")
                                    && extras.get("startIntegral").equals("2")
                            ) {
                                if (map.containsKey("quantity")) {
                                    map.put("quantity", map.get("quantity") + skuOrder.getQuantity());
                                    map.put("userId", skuOrder.getBuyerId());
                                    map.put("shopId", skuOrder.getShopId());
                                } else {
                                    map.put("quantity", Long.valueOf(skuOrder.getQuantity()));
                                    map.put("userId", skuOrder.getBuyerId());
                                    map.put("shopId", skuOrder.getShopId());
                                }
                            }
                        }
                    } else if (orderRefund.getOrderType() == 2) {
                        // sku级别
                        SkuOrder skuOrder = skuOrderReadService.findById(orderRefund.getOrderId()).getResult();
                        //过滤一段奶粉不参加
                        Shop rShop = Optional.ofNullable(shopReadService.findById(skuOrder.getShopId()).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));
                        Map<String, String> extras = rShop.getExtra();
                        Response<Item> itemResponse = itemReadService.findById(skuOrder.getItemId());
                        if (!itemResponse.isSuccess() || itemResponse.getResult() == null) {
                            log.error("[itemResponse  ] itemId:{}", skuOrder.getItemId());
                            return;
                        }
                        if ((extras != null
                                && !extras.isEmpty()
                                && extras.containsKey("startIntegral")
                                && extras.get("startIntegral").equals("3")
                                && Objects.equal(itemResponse.getResult().getTips(), 1)
                        )) {
                            integralCountIds.append(skuOrder.getId()).append("_").append(skuOrder.getQuantity()).append("#");
                            map.put("quantity", Long.valueOf(skuOrder.getQuantity()));
                            map.put("userId", skuOrder.getBuyerId());
                            map.put("shopId", skuOrder.getShopId());
                        } else if ((extras != null
                                && !extras.isEmpty()
                                && extras.containsKey("startIntegral")
                                && extras.get("startIntegral").equals("2")
                        )) {
                            map.put("quantity", Long.valueOf(skuOrder.getQuantity()));
                            map.put("userId", skuOrder.getBuyerId());
                            map.put("shopId", skuOrder.getShopId());
                        }
                    }
                }

                log.info("Optionalcount-REFUND map:{}", map);
                if (map.isEmpty() || !map.containsKey("shopId")) {
                    return;
                }

                Shop rShop = Optional.ofNullable(shopReadService.findById(map.get("shopId")).getResult()).orElseThrow(() -> new JsonResponseException("shop.not.find"));


                Map<String, String> extra = rShop.getExtra();
                //积分商品核减数
//                if (extra != null && !extra.isEmpty()
//                        && extra.containsKey("startIntegral")
//                        && extra.get("startIntegral").equals("2")
//                ) {
//                    log.info("[OrderIntegralCountEventListener] paymentId:{} refundId:{} startIntegral:{}", paymentId, refundId, map.get("startIntegral"));
//                    Map<String, String> maps = new HashMap<>();
//                    maps.put("userId", String.valueOf(map.get("userId")));
//                    maps.put("shopId", String.valueOf(map.get("shopId")));
//                    maps.put("integralCount", String.valueOf(map.get("quantity")));
//                    integralAccount.initializeIntegralAccount(maps, 4);
//                }
                //积分累计商品合计数
                if (extra != null && !extra.isEmpty()
                        && extra.containsKey("startIntegral")
                        && extra.get("startIntegral").equals("3")
                ) {
                    log.info("[OrderIntegralCountEventListener] paymentId:{} refundId:{} startIntegral:{}", paymentId, refundId, map.get("startIntegral"));
                    Map<String, String> maps = new HashMap<>();
                    maps.put("userId", String.valueOf(map.get("userId")));
                    maps.put("shopId", String.valueOf(map.get("shopId")));
                    maps.put("integralCountIds", integralCountIds.toString());
                    integralAccount.initializeIntegralAccount(maps, 6);
                }
            }
        } catch (Exception e) {
            log.error("failed find to  cause:{}", Throwables.getStackTraceAsString(e));
        }
    }

}


