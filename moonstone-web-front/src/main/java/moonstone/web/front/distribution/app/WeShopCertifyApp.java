package moonstone.web.front.distribution.app;

import lombok.AllArgsConstructor;
import moonstone.cache.ShopCacheHolder;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.model.AuthAble;
import moonstone.common.utils.Translate;
import moonstone.shop.slice.ShopFunctionSlice;
import moonstone.user.application.UserCertificationForWithdrawApp;
import moonstone.user.dto.UserAccountCertificationVO;
import moonstone.weShop.model.WeShop;
import moonstone.weShop.model.WeShopShopAccount;
import moonstone.weShop.service.WeShopShopAccountReadService;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class WeShopCertifyApp {
    WeShopShopAccountReadService weShopShopAccountReadService;
    UserCertificationForWithdrawApp userCertificationForWithdrawApp;
    WeShopCacheHolder weShopCacheHolder;
    ShopCacheHolder shopCacheHolder;

    /**
     * 微店店铺是否需要实名审核, 这个实名审核和提现审核为一体化开关
     * @param weShopId  微店
     * @return 需要审核
     */
    public boolean requireCertification(Long weShopId) {
        Long shopId = weShopShopAccountReadService.findByWeShopId(weShopId).getResult().stream().filter(ac -> ac.getStatus() > 0).findFirst().map(WeShopShopAccount::getShopId).orElse(null);
        return ShopFunctionSlice.build(shopCacheHolder.findShopById(shopId)).isWithdrawRequireCertification();
    }

    /**
     * 判断微店店铺是否通过实名审核
     *
     * @param weShopId 微店店铺Id
     * @return 通过审核
     */
    public boolean passCertification(Long weShopId) {
        WeShop weShop = weShopCacheHolder.findByWeShopId(weShopId)
                .orElseThrow(() -> Translate.exceptionOf("微店店铺不存在"));
        if (!requireCertification(weShopId)) {
            return true;
        }
        Long shopId = weShopShopAccountReadService.findByWeShopId(weShopId).getResult().stream().filter(ac -> ac.getStatus() > 0).findFirst().map(WeShopShopAccount::getShopId).orElse(null);
        return userCertificationForWithdrawApp.previewUserCertification(weShop.getUserId(), shopId)
                .map(UserAccountCertificationVO::getAuthAble)
                .map(AuthAble::isAuthed).orElse(false);
    }
}
