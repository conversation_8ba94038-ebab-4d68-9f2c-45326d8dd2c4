/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Lists;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.service.PromotionReadService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 店铺设置的营销活动参数缓存
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-06-06
 */
@Component
@Slf4j
public class PromotionCacher {

    private LoadingCache<Long, Promotion> promotionCache;

    private LoadingCache<Long, List<Promotion>> shopOngoingPromotionCache;

    @Resource
    private PromotionReadService promotionReadService;

    @Value("${cache.duration.in.minutes: 60}")
    private Integer duration;

    @PostConstruct
    public void init() {
        this.promotionCache = Caffeine.newBuilder()
                .expireAfterWrite(duration, TimeUnit.MINUTES)
                .maximumSize(10000)
                .build(promotionId -> {
                    Response<Promotion> rPromotion = promotionReadService.findById(promotionId);
                    if (!rPromotion.isSuccess()) {
                        log.error("failed to find promotion(id={}), error code:{}", promotionId, rPromotion.getError());
                        throw new ServiceException(rPromotion.getError());
                    }
                    return rPromotion.getResult();
                });

        this.shopOngoingPromotionCache = Caffeine.newBuilder()
                .expireAfterWrite(3, TimeUnit.SECONDS)
                .maximumSize(10000)
                .build(shopId -> {
                    final Response<List<Promotion>> rOngoingPromotions = promotionReadService.findOngoingByShopId(shopId);
                    if (!rOngoingPromotions.isSuccess()) {
                        log.error("failed to find ongoing promotions for shop(id={}), error code:{}",
                                shopId, rOngoingPromotions.getError());
                        throw new ServiceException(rOngoingPromotions.getError());
                    }
                    return rOngoingPromotions.getResult();
                });
    }

    /**
     * 根据营销id查找对应的店铺营销参数
     *
     * @param promotionId 营销活动id
     * @return 店铺营销参数
     */
    public Promotion findByPromotionId(Long promotionId) {
        return this.promotionCache.get(promotionId);
    }


    /**
     * 查找店铺的有效营销活动列表
     *
     * @param shopId 店铺id, 如果是0, 表示全局营销
     * @return 有效营销活动列表
     */
    public List<Promotion> findOngoingPromotionOf(Long shopId) {
        List<Promotion> promotions = this.shopOngoingPromotionCache.get(shopId);
        //因为营销是被缓存的, 所以从缓存中拿出来的营销也可能过期了
        List<Promotion> result = Lists.newArrayListWithCapacity(Objects.requireNonNull(promotions).size());
        for (Promotion promotion : promotions) {
            if (promotion.inProcess()) {
                result.add(promotion);
            }
        }
        return result;
    }

    /**
     * 失效指定id的营销活动缓存
     *
     * @param promotionId 要失效的营销活动id
     */
    public void invalidateByPromotionId(Long promotionId) {
        this.promotionCache.invalidate(promotionId);
    }

    /**
     * 失效指定店铺进行中的营销活动缓存
     *
     * @param shopId 店铺id
     */
    public void invalidateOngoingPromotion(Long shopId) {
        this.shopOngoingPromotionCache.invalidate(shopId);
    }
}
