package moonstone.membership.dto.paging;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;

import java.io.Serializable;
import java.util.List;

/**
 * Author:  CaiZhy
 * Date:    2019/1/7
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MembershipPriceCriteria extends PagingCriteria implements Serializable {
    private static final long serialVersionUID = -8021318966446683305L;
    private List<Long> ids;
    private Long shopId;
    private String skuName;
    private String userName;
}
