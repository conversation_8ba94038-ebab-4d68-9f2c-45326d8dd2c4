package moonstone.web.front.component.order;

import moonstone.common.model.IsPresent;
import moonstone.order.bo.OrderReceiverInfoBO;
import moonstone.order.bo.ShopOrderProfitBO;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.OrderRoleSnapshotOrderTypeEnum;
import moonstone.order.enu.ProfitType;
import moonstone.order.enu.ProfitWithdrawRecordStatusEnum;
import moonstone.order.model.*;
import moonstone.order.model.related.OrderRelated;
import moonstone.order.model.result.OrderPaymentInfoDO;
import moonstone.order.model.result.OrderShipmentInfoDO;
import moonstone.order.model.result.ShopOrderWithdrawStatusDO;
import moonstone.order.service.*;
import moonstone.user.bo.PayerInfoBO;
import moonstone.user.enums.DataExportTaskTypeEnum;
import moonstone.user.model.PayerInfoRecord;
import moonstone.user.model.User;
import moonstone.user.service.PayerInfoReadService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.util.BaseExporter;
import moonstone.web.front.component.order.convert.OrderExportConvertor;
import moonstone.web.front.component.order.view.OrderExcelExportView;
import org.apache.commons.lang3.tuple.Pair;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toSet;

@Component
public class OrderExporter extends BaseExporter<OrderExcelExportView, OrderExporter.OrderExportParameter> {

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Resource
    private FirstOrderMarkReadService firstOrderMarkReadService;

    @Resource
    private PayerInfoReadService payerInfoReadService;

    @Resource
    private ReceiverInfoReadService receiverInfoReadService;

    @Resource
    private ShipmentReadService shipmentReadService;

    @Resource
    private PaymentReadService paymentReadService;

    @Resource
    private BalanceDetailReadService balanceDetailReadService;

    @Resource
    private ProfitWithdrawRecordReadService profitWithdrawRecordReadService;

    @Resource
    private OrderExportConvertor orderExportConvertor;

    @Resource
    private RefundReadService refundReadService;

    @Resource
    private AccountStatementDetailReadService accountStatementDetailReadService;

    @Resource
    private WithDrawProfitApplyReadService withDrawProfitApplyReadService;

    @Resource
    private OrderRoleSnapshotReadService orderRoleSnapshotReadService;

    @Resource
    private UserReadService<User> userReadService;

    @Override
    protected DataExportTaskTypeEnum getCurrentTaskType() {
        return DataExportTaskTypeEnum.ORDER_MANAGEMENT;
    }

    @Override
    protected List<OrderExcelExportView> findPageDataList(OrderExportParameter queryParameter, int pageNo, int pageSize) {
        if (queryParameter == null || CollectionUtils.isEmpty(queryParameter.shopOrderIdList())) {
            return Collections.emptyList();
        }

        int start = (pageNo - 1) * pageSize;
        int end = Math.min(start + pageSize, queryParameter.shopOrderIdList().size());
        if (start >= queryParameter.shopOrderIdList().size()) {
            return Collections.emptyList();
        }

        return batchFindExportData(queryParameter.shopOrderIdList().subList(start, end));
    }

    private List<OrderExcelExportView> batchFindExportData(List<Long> orderIdList) {
        //查询主订单
        Map<Long, ShopOrder> shopOrderMap = shopOrderReadService.getShopOrderMap(orderIdList);
        if (CollectionUtils.isEmpty(shopOrderMap)) {
            return Collections.emptyList();
        }

        //由子订单初步构造导出对象
        List<OrderExcelExportView> resultList = buildFromSkuOrder(orderIdList);

        //填充主订单信息
        appendShopOrderInfo(resultList, shopOrderMap);

        //填充商品信息
        appendItemInfo(resultList);

        //填充首单标识
        appendFirstOrderMark(resultList);

        //填充付款人信息
        appendPayerInfo(resultList, shopOrderMap);

        //填充收件人信息
        appendReceiverInfo(resultList, shopOrderMap);

        //填充发货信息
        appendShipmentInfo(resultList);

        //填充支付单信息
        appendPaymentInfo(resultList);

        //填充利润信息
        appendProfitInfo(resultList, shopOrderMap);

        //填充”是否坏账“信息
        appendBadDebtsInfo(resultList, shopOrderMap);

        return resultList;
    }

    /**
     * 填充”是否坏账“信息
     * <br/> 确认收货后并且已提现的订单发生退款成功的 标记为坏账订单
     *
     * @param resultList
     * @param shopOrderMap
     */
    private void appendBadDebtsInfo(List<OrderExcelExportView> resultList, Map<Long, ShopOrder> shopOrderMap) {
        if (CollectionUtils.isEmpty(shopOrderMap)) {
            return;
        }
        var shopOrderIds = shopOrderMap.keySet().stream().toList();
        var shopId = resultList.get(0).getShopId();

        //查询其中已确认收货的订单
        var confirmedSet = findAlreadyConfirmed(shopId, shopOrderIds);

        //查询其中已完成退款的订单
        var refundedSet = findAlreadyRefunded(shopId, shopOrderIds);
        var refundedSet2 = shopOrderMap.values().stream()
                .filter(shopOrder -> OrderStatus.REFUND.getValue() == shopOrder.getStatus() || OrderStatus.PAYMENT_CLOSED_COMPLETE_REFUND.getValue() == shopOrder.getStatus() )
                .map(ShopOrder::getId)
                .collect(Collectors.toSet());

        //发起了提现的订单
        var withdrawSet = findAlreadyWithdrawSet(shopId, shopOrderIds);

        resultList.forEach(entity -> {
            if (withdrawSet.contains(entity.getOrderId())
                    && (refundedSet.contains(entity.getOrderId()) || refundedSet2.contains(entity.getOrderId()))
                    && confirmedSet.contains(entity.getOrderId())) {
                entity.setBadDebts(Boolean.TRUE.toString());
            } else {
                entity.setBadDebts(Boolean.FALSE.toString());
            }
        });
    }

    /**
     * 查询发起了提现的订单
     *
     * @param shopId
     * @param shopOrderIds
     * @return
     */
    private Set<Long> findAlreadyWithdrawSet(Long shopId, List<Long> shopOrderIds) {
        var statusList = balanceDetailReadService.findShopOrderWithdrawStatus(shopId, shopOrderIds).getResult();
        if (CollectionUtils.isEmpty(statusList)) {
            return Collections.emptySet();
        }

        return statusList.stream()
                .filter(e -> e.getWithdrawStatus() != null)
                .map(ShopOrderWithdrawStatusDO::getShopOrderId)
                .collect(Collectors.toSet());
    }

    /**
     * 查询其中已完成退款的订单 （理论上，退款单不会关子订单了）
     *
     * @param shopId
     * @param shopOrderIds
     * @return
     */
    private Set<Long> findAlreadyRefunded(Long shopId, List<Long> shopOrderIds) {
        var orderRefunds = refundReadService.findOrderRefund(OrderLevel.SHOP, shopOrderIds).getResult();
        if (CollectionUtils.isEmpty(orderRefunds)) {
            return Collections.emptySet();
        }
        var orderRefundMap = orderRefunds.stream().collect(Collectors.groupingBy(
                OrderRefund::getRefundId, mapping(OrderRelation::getOrderId, toSet())));

        //退款单关联的利润
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setPageNo(1);
        criteria.setPageSize(Integer.MAX_VALUE);
        criteria.setSourceId(shopId);
        criteria.setRelatedIds(orderRefunds.stream().map(OrderRefund::getRefundId).collect(Collectors.toList()));
        criteria.setType(ProfitType.OutCome.getValue());
        criteria.setStatusBitMarks(Lists.newArrayList(BalanceDetail.maskBit.RefundRelated.getValue()));

        var balanceDetails = balanceDetailReadService.pageList(criteria).getResult();
        if (CollectionUtils.isEmpty(balanceDetails)) {
            return Collections.emptySet();
        }

        return balanceDetails.stream()
                .map(balanceDetail -> orderRefundMap.get(balanceDetail.getRelatedId()))
                .filter(orderIds -> !CollectionUtils.isEmpty(orderIds))
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
    }

    /**
     * 查询其中已确认收货的订单 (理论上，利润流水不再关联子订单了)
     *
     * @param shopId
     * @param shopOrderIds
     * @return
     */
    private Set<Long> findAlreadyConfirmed(Long shopId, List<Long> shopOrderIds) {
        //主订单关联的“已确认收货”的利润
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setPageNo(1);
        criteria.setPageSize(Integer.MAX_VALUE);
        criteria.setSourceId(shopId);
        criteria.setRelatedIds(shopOrderIds);
        criteria.setType(ProfitType.InCome.getValue());
        criteria.setStatusBitMarks(Lists.newArrayList(BalanceDetail.maskBit.OrderRelated.getValue(),
                OrderRelated.orderRelatedMask.ShopOrder.getValue(), IsPresent.presentMaskBit.Present.getValue()));

        var list = balanceDetailReadService.pageList(criteria).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptySet();
        }

        return list.stream().map(BalanceDetail::getRelatedId).collect(Collectors.toSet());
    }

    /**
     * 由子订单初步构造导出对象
     *
     * @param shopOrderIds
     * @return
     */
    private List<OrderExcelExportView> buildFromSkuOrder(List<Long> shopOrderIds) {
        if (CollectionUtils.isEmpty(shopOrderIds)) {
            return Collections.emptyList();
        }

        //查询子订单
        List<SkuOrder> skuOrderList = skuOrderReadService.findByShopOrderIds(shopOrderIds).getResult();
        if (CollectionUtils.isEmpty(skuOrderList)) {
            return Collections.emptyList();
        }

        return skuOrderList.stream()
                .map(skuOrder -> orderExportConvertor.convert(skuOrder))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 填充商品信息
     *
     * @param resultList
     */
    private void appendItemInfo(List<OrderExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        resultList.forEach(target -> orderExportConvertor.appendItemInfo(target));
    }

    /**
     * 填充利润信息
     *
     * @param resultList
     * @param shopOrderMap
     */
    private void appendProfitInfo(List<OrderExcelExportView> resultList, Map<Long, ShopOrder> shopOrderMap) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        //利润信息 (key = shop_order_id)
        Map<Long, ShopOrderProfitBO> profitMap = getProfitMap(shopOrderMap);

        resultList.forEach(target -> orderExportConvertor.appendProfitInfo(target, profitMap.get(target.getOrderId())));
    }

    /**
     * 填充支付单信息
     *
     * @param resultList
     */
    private void appendPaymentInfo(List<OrderExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        //支付信息 (key = shop_order_id)
        Map<Long, OrderPaymentInfoDO> paymentMap = getPaymentMap(
                resultList.stream().map(OrderExcelExportView::getOrderId).collect(Collectors.toList()));

        resultList.forEach(target -> orderExportConvertor.appendPaymentInfo(target, paymentMap.get(target.getOrderId())));
    }

    /**
     * 填充发货信息
     *
     * @param resultList
     */
    private void appendShipmentInfo(List<OrderExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        //发货信息 (key = shop_order_id)
        Map<Long, OrderShipmentInfoDO> shipmentMapByShopOrder = shipmentReadService.getShipmentMap(
                resultList.stream().map(OrderExcelExportView::getOrderId).collect(Collectors.toList()), OrderLevel.SHOP);

        //发货信息( key = sku_order_id)
        Map<Long, OrderShipmentInfoDO> shipmentMapBySkuOrder = shipmentReadService.getShipmentMap(
                resultList.stream().map(OrderExcelExportView::getSkuOrderId).collect(Collectors.toList()), OrderLevel.SKU);

        resultList.forEach(target -> orderExportConvertor.appendShipmentInfo(target, shipmentMapByShopOrder.get(target.getOrderId()),
                shipmentMapBySkuOrder.get(target.getSkuOrderId())));
    }

    /**
     * 填充收件人信息
     *
     * @param resultList
     * @param shopOrderMap
     */
    private void appendReceiverInfo(List<OrderExcelExportView> resultList, Map<Long, ShopOrder> shopOrderMap) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        // key = shop_order_id
        Map<Long, OrderReceiverInfoBO> receiverInfoMap = getReceiverInfoMap(shopOrderMap);

        resultList.forEach(target -> orderExportConvertor.appendReceiverInfo(target, receiverInfoMap.get(target.getOrderId())));
    }

    /**
     * 填充付款人信息
     *
     * @param resultList
     * @param shopOrderMap
     */
    private void appendPayerInfo(List<OrderExcelExportView> resultList, Map<Long, ShopOrder> shopOrderMap) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        //付款人信息 (key = shop_order_id)
        Map<Long, PayerInfoBO> payInfoMap = getPayerInfoMap(shopOrderMap);

        //付款人基础用户信息（key = user_id）
        Map<Long, User> userMap = getUserMap(shopOrderMap);

        resultList.forEach(target -> {
            //付款人名称与身份证
            orderExportConvertor.appendPayerInfo(target, payInfoMap.get(target.getOrderId()));

            //付款人的微信账号信息
            orderExportConvertor.appendUserWxInfo(target, shopOrderMap.get(target.getOrderId()));

            //付款人的微信账号信息
            orderExportConvertor.appendUserInfo(target, userMap.get(target.getBuyerId()));
        });
    }

    /**
     * 订单购买人信息
     *
     * @param shopOrderMap
     * @return
     */
    private Map<Long, User> getUserMap(Map<Long, ShopOrder> shopOrderMap) {
        List<Long> userIdList = shopOrderMap.values().stream().map(ShopOrder::getBuyerId).collect(Collectors.toList());

        var userList = userReadService.findByIds(userIdList).getResult();
        if (CollectionUtils.isEmpty(userList)) {
            return Collections.emptyMap();
        }

        return userList.stream().collect(Collectors.toMap(User::getId, o -> o, (k1, k2) -> k1));
    }

    /**
     * 填充首单标识
     *
     * @param resultList
     */
    private void appendFirstOrderMark(List<OrderExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        //首单标识 (value = shop_order_id)
        Set<Long> firstOrderMarkSet = getFirstOrderMarkSet(
                resultList.stream().map(OrderExcelExportView::getOrderId).collect(Collectors.toList()));

        resultList.forEach(target -> target.setFirstOrder(firstOrderMarkSet.contains(target.getOrderId())));
    }

    /**
     * 填充主订单信息
     *
     * @param resultList
     * @param shopOrderMap
     */
    private void appendShopOrderInfo(List<OrderExcelExportView> resultList, Map<Long, ShopOrder> shopOrderMap) {
        if (CollectionUtils.isEmpty(resultList) || CollectionUtils.isEmpty(shopOrderMap)) {
            return;
        }

        resultList.forEach(target -> orderExportConvertor.appendShopOrderInfo(target, shopOrderMap.get(target.getOrderId())));
    }

    /**
     * 利润信息
     *
     * @param shopOrderMap
     * @return
     */
    private Map<Long, ShopOrderProfitBO> getProfitMap(Map<Long, ShopOrder> shopOrderMap) {
        //订单id
        List<Long> orderIds = shopOrderMap.keySet().stream().toList();

        //平台id
        Long shopId = shopOrderMap.values().stream().toList().get(0).getShopId();

        //key = shop_order_id , value = BalanceDetail
        Map<Long, List<BalanceDetail>> balanceDetailMap = balanceDetailReadService.getBalanceDetailMap(orderIds, shopId);

        //key = BalanceDetail.id , value = AccountStatementDetail
        Map<Long, AccountStatementDetail> accountStatementDetailMap = getAccountStatementDetailMap(balanceDetailMap);

        //key = Balance_Detail_id
        Map<Long, List<ProfitWithdrawRecord>> profitWithdrawRecordMap = getProfitWithdrawRecordMap(balanceDetailMap);

        //key = WithDrawProfitApply.id
        Map<Long, WithDrawProfitApply> withdrawApplyMap = getWithdrawApplyMap(profitWithdrawRecordMap);

        //key = shop_order_id
        Map<Long, List<OrderRoleSnapshot>> roleSnapshotMap = getOrderRoleSnapshotMap(orderIds);

        //模型转换并返回
        return balanceDetailMap.entrySet().stream()
                .map(sourceEntry -> orderExportConvertor.convert(shopOrderMap.get(sourceEntry.getKey()),
                        sourceEntry.getValue(), profitWithdrawRecordMap, accountStatementDetailMap, withdrawApplyMap, roleSnapshotMap))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Pair::getLeft, Pair::getRight, (o1, o2) -> (o1)));
    }

    /**
     * 查询对应的提现申请单信息
     *
     * @param profitWithdrawRecordMap
     * @return
     */
    private Map<Long, WithDrawProfitApply> getWithdrawApplyMap(Map<Long, List<ProfitWithdrawRecord>> profitWithdrawRecordMap) {
        if (CollectionUtils.isEmpty(profitWithdrawRecordMap)) {
            return Collections.emptyMap();
        }

        //提现单id
        var ids = profitWithdrawRecordMap.values().stream()
                .filter(list -> !CollectionUtils.isEmpty(list))
                .map(list -> list.stream().map(ProfitWithdrawRecord::withdrawId).collect(Collectors.toList()))
                .collect(ArrayList<Long>::new, ArrayList::addAll, ArrayList::addAll);

        var list = withDrawProfitApplyReadService.findByIds(ids).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(WithDrawProfitApply::getId, o -> o, (k1, k2) -> k1));
    }

    /**
     * 查询对应的账单明细
     *
     * @param source
     * @return key = BalanceDetail.id , value = AccountStatementDetail
     */
    private Map<Long, AccountStatementDetail> getAccountStatementDetailMap(Map<Long, List<BalanceDetail>> source) {
        if (CollectionUtils.isEmpty(source)) {
            return Collections.emptyMap();
        }

        var ids = source.values().stream()
                .filter(list -> !CollectionUtils.isEmpty(list))
                .map(list -> list.stream().map(BalanceDetail::getId).collect(Collectors.toList()))
                .collect(ArrayList<Long>::new, ArrayList::addAll, ArrayList::addAll);

        var list = accountStatementDetailReadService.findByBalanceDetailIds(ids).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.toMap(AccountStatementDetail::getBalanceDetailId, o -> o, (k1, k2) -> k1));
    }

    /**
     * 查询订单的角色信息快照
     *
     * @param orderIds 主订单id列表
     * @return
     */
    private Map<Long, List<OrderRoleSnapshot>> getOrderRoleSnapshotMap(List<Long> orderIds) {
        var list = orderRoleSnapshotReadService.findByShopOrderIds(
                orderIds, OrderRoleSnapshotOrderTypeEnum.SHOP_ORDER).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.groupingBy(entity -> Optional.ofNullable(entity.getShopOrderId()).orElse(-1L)));
    }

    private Map<Long, List<ProfitWithdrawRecord>> getProfitWithdrawRecordMap(Map<Long, List<BalanceDetail>> balanceDetailMap) {
        if (CollectionUtils.isEmpty(balanceDetailMap)) {
            return Collections.emptyMap();
        }

        //所有的 balance_detail 的 id
        List<Long> profitIdList = new ArrayList<>();
        balanceDetailMap.values().forEach(subList -> {
            if (!CollectionUtils.isEmpty(subList)) {
                profitIdList.addAll(subList.stream().map(BalanceDetail::getId).collect(Collectors.toList()));
            }
        });

        //目标状态列表
        List<Integer> statusList = Lists.newArrayList(ProfitWithdrawRecordStatusEnum.WAIT_FOR_AUDIT.getCode(),
                ProfitWithdrawRecordStatusEnum.AUDIT_FINISHED.getCode());

        //查询
        var list = profitWithdrawRecordReadService.findByProfitIdsAndStatus(profitIdList, statusList).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream().collect(Collectors.groupingBy(ProfitWithdrawRecord::profitId));
    }

    /**
     * 支付信息
     *
     * @param orderIdList
     * @return
     */
    private Map<Long, OrderPaymentInfoDO> getPaymentMap(List<Long> orderIdList) {
        return paymentReadService.findMapByOrderIds(orderIdList, OrderLevel.SHOP);
    }

    /**
     * 付款人信息
     *
     * @param shopOrderMap
     * @return
     */
    private Map<Long, PayerInfoBO> getPayerInfoMap(Map<Long, ShopOrder> shopOrderMap) {
        List<Long> orderIdList = shopOrderMap.keySet().stream().toList();

        //订单的付款人信息
        List<PayerInfoRecord> payerInfoRecordList = payerInfoReadService.findByOrderIds(orderIdList).getResult();
        if (CollectionUtils.isEmpty(payerInfoRecordList)) {
            return Collections.emptyMap();
        }

        //模型转换
        return payerInfoRecordList.stream()
                .map(payerInfo -> orderExportConvertor.convert(payerInfo))
                .collect(Collectors.toMap(PayerInfoBO::getOrderId, e -> e, (o1, o2) -> o1));
    }

    /**
     * 收件人信息
     *
     * @param shopOrderMap
     * @return
     */
    private Map<Long, OrderReceiverInfoBO> getReceiverInfoMap(Map<Long, ShopOrder> shopOrderMap) {
        List<Long> orderIdList = shopOrderMap.keySet().stream().toList();

        //收件人信息
        List<OrderReceiverInfo> list = getOrderReceiverInfo(orderIdList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }

        return list.stream()
                .map(orderReceiver -> orderExportConvertor.convert(orderReceiver, shopOrderMap.get(orderReceiver.getOrderId())))
                .collect(Collectors.toMap(OrderReceiverInfoBO::getOrderId, e -> e, (o1, o2) -> o1));
    }

    private List<OrderReceiverInfo> getOrderReceiverInfo(List<Long> orderIdList) {
        List<OrderReceiverInfo> receiverInfos = receiverInfoReadService.findByOrderIds(orderIdList, OrderLevel.SHOP).getResult();
        if (CollectionUtils.isEmpty(receiverInfos)) {
            return Collections.emptyList();
        }

        return receiverInfos;
    }

    /**
     * 首单标识
     *
     * @param orderIdList
     * @return
     */
    private Set<Long> getFirstOrderMarkSet(List<Long> orderIdList) {
        var firstOrderMarkList = firstOrderMarkReadService.findByOrderIds(orderIdList).getResult();
        if (CollectionUtils.isEmpty(firstOrderMarkList)) {
            return Collections.emptySet();
        }

        return firstOrderMarkList.stream().map(FirstOrderMark::orderId).collect(Collectors.toSet());
    }

    public record OrderExportParameter(List<Long> shopOrderIdList) {
    }
}
