package moonstone.web.op.allinpay.dto.signature.req;

import lombok.Data;
import moonstone.web.op.allinpay.AllinPayMethodEnum;
import moonstone.web.op.allinpay.dto.signature.BaseRequest;
import moonstone.web.op.allinpay.dto.signature.res.GetPreviewUrlResponse;

@Data
public class GetPreviewUrlDTO extends BaseRequest {

    // 统一客户号（必填）
    private String merId;

    // 商户订单号（由商户生成，不重复值，必填）
    private String custOrderId;

    // 账号（必填）
    private String account;

    // 合同编号（必填）
    private String contractId;

    @Override
    public Class responseClass() {
        return GetPreviewUrlResponse.class;
    }

    @Override
    public AllinPayMethodEnum method() {
        // "allinpay.uas.getPreviewUrl"
        return AllinPayMethodEnum.SIGNATURE_GET_PREVIEW_URL;
    }
}
