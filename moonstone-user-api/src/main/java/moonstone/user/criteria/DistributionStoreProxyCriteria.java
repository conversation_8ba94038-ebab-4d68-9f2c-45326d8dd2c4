package moonstone.user.criteria;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.BaseCriteria;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/6/17 13:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DistributionStoreProxyCriteria extends BaseCriteria {


    String nickName;//昵称->parana_user_profiles->realName

    String mobile;//手机号parana_users的mobile

    String name;//parana_users的name
}
