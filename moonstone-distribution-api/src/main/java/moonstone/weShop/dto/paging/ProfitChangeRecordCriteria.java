package moonstone.weShop.dto.paging;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.PagingCriteria;

import java.io.Serializable;
import java.util.Date;

/**
 * Author:  CaiZhy
 * Date:    2019/2/26
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProfitChangeRecordCriteria extends PagingCriteria implements Serializable {
    private static final long serialVersionUID = -6053119601351930257L;

    private Long shopId;
    private Long weShopId;
    private Integer account;
    private Integer type;
    private Date startSettleAt;
    private Date endSettleAt;
}
