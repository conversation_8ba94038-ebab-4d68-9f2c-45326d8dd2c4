package moonstone.weShop.service;

import io.terminus.common.model.Response;
import moonstone.common.model.Either;
import moonstone.weShop.model.WeShopItem;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2018/12/10
 */
public interface WeShopItemWriteService {
    Response<Long> create(WeShopItem weShopItem);

    Response<Boolean> update(WeShopItem weShopItem);

    Response<Boolean> batchUpdate(List<WeShopItem> weShopItems);

    Response<Boolean> updateStatus(Long weShopItemId, Integer status);

    Response<Boolean> updateStatusByShopId(Long shopId, Integer status);

    Response<Boolean> delete(Long weShopId);

    Response<Boolean> updateStatusByItemId(Long itemId, int status);

    @Deprecated
    Response<Boolean> increaseSellQuantity(Integer quantity, Long itemId, long parseLong);

    /**
     * 更新商品状态, 如果商品不存在则创建
     * 如果已经存在且上线 则不允许修改
     *
     * @param weShopId 微店Id
     * @param shopId   店铺Id
     * @param itemId   商品Id
     * @param skuId
     * @return 创建成功的微店商品Id
     */
    Either<Long> initStatusForCreateWeShopItem(Long weShopId, Long shopId, Long itemId, Long skuId);

    /**
     * 修改商品的销量
     * 如果商品初始销量为null则会被初始化为0 然后进行与quantity求和
     *
     * @param id       weShopItemId
     * @param quantity 这次修改的数量, 会以加法加入数据库
     * @return 操作成功, 除非出现数据库异常或者 id/quantity为null才报错
     */
    Either<Boolean> modifySellQuantityById(Long id, Long quantity);
}
