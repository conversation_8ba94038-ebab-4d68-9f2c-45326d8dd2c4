package moonstone.web.front.user;

import com.alibaba.fastjson.JSON;
import io.terminus.common.model.BaseUser;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.model.CommonUser;
import moonstone.common.model.WxCommonUser;
import moonstone.common.utils.EventSender;
import moonstone.common.utils.LogUtil;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.user.cache.UserCacheHolder;
import moonstone.web.core.AppConstants;
import moonstone.web.core.component.user.UserSubShopPackComponent;
import moonstone.web.core.events.user.WxLoginEvent;
import moonstone.web.core.util.ParanaUserMaker;
import moonstone.web.front.user.application.UserWxaPageQrCodeApp;
import moonstone.web.front.user.dto.QrCoderCreateRequest;
import moonstone.web.front.user.vo.WxaPageQrCodeVO;
import moonstone.web.core.decoration.model.WxUserDomain;
import moonstone.web.core.decoration.api.WxUserDomainFactory;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 为微信第三方平台
 *
 * <AUTHOR>
 */
@Slf4j
@RequestMapping("/api/open/v1/user")
@RestController
public class UserWxApp {
    @Resource
    private WxUserDomainFactory wxUserDomainFactory;
    @Resource
    private UserCacheHolder userCacheHolder;
    @Autowired
    private UserSubShopPackComponent userSubShopPackComponent;
    @Autowired
    private UserWxaPageQrCodeApp userWxaPageQrCodeApp;

    @PostMapping("/login")
    public APIResp<BaseUser> login(HttpServletRequest request, String code, Long projectId) {
        WxUserDomain domain = wxUserDomainFactory.create(code, projectId);
        try {
            Long userId = domain.login().take();
            // session 注入登录信息
            request.getSession().setAttribute(AppConstants.SESSION_OPEN_ID, domain.getUserWx().getOpenId());
            request.getSession().setAttribute(AppConstants.SESSION_USER_ID, userId);
            // 发送事件
            WxCommonUser wxParanaUser = new WxCommonUser();
            BeanUtils.copyProperties(ParanaUserMaker.from(userCacheHolder.findByUserId(userId).orElseThrow(() -> Translate.exceptionOf("用户信息查找失败[%s]", userId)))
                    , wxParanaUser);
            userSubShopPackComponent.wrap(wxParanaUser, domain.getShopId());
            wxParanaUser.setOpenId(domain.getUserWx().getOpenId());
            EventSender.sendApplicationEvent(new WxLoginEvent(wxParanaUser));
            return APIResp.ok(wxParanaUser);
        } catch (Exception exception) {
            log.error("{} fail to login for project[{}] with code [{}]", LogUtil.getClassMethodName(), projectId, code, exception);
            return APIResp.error(Translate.of("登录失败"));
        }
    }

    @PostMapping("/createQrCode")
    public APIResp<WxaPageQrCodeVO> createQrCode(@RequestBody QrCoderCreateRequest request) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            return APIResp.notLogin();
        }
        if (request == null || request.getShopId() == null || request.getProjectId() == null
                || StringUtils.isBlank(request.getPage())) {
            return APIResp.error("参数缺失");
        }

        try {
            return APIResp.ok(new WxaPageQrCodeVO(userWxaPageQrCodeApp.createQrCode(request.getShopId(),
                    request.getProjectId(), request.getPage(), request.getScene())));
        } catch (Exception ex) {
            log.error("UserWxApp.createQrCode error, request={}", JSON.toJSONString(request), ex);
            return APIResp.error(ex.getMessage());
        }
    }
}
