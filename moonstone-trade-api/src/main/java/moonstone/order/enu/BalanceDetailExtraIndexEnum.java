package moonstone.order.enu;

public enum BalanceDetailExtraIndexEnum {
    // 什么鬼，有什么用？
    RelatedOrder("RelatedOrder", ""),
    OrderLevel("OrderLevel", ""),
    orderId("orderId", ""),
    ;

    private String code;
    private String description;

    BalanceDetailExtraIndexEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
