
package moonstone.web.op.allinpay.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 通联电子签约
 *

 * @since 2021-02-19
 */
@Data
@ApiModel(value = "SignContractDTO", description = "通联电子签约")
public class SignContractDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "商户系统用户标识，商户系统中唯一编号", required = true)
	@NotEmpty(message = "商户系统用户标识不能为空")
	private String bizUserId;

	@ApiModelProperty(value = "签约户名，" +
			"个人会员：名称" +
			"企业会员：" +
			"法人提现，则上送“法人姓名”" +
			"对公户提现，则上送“企业名称”", required = true)
	@NotEmpty(message = "签约户名")
	private String signAcctName;

	@ApiModelProperty(value = "跳转页面类型 " +
			"1-H5页面" +
			"2-小程序页面", required = true)
	@NotEmpty(message = "跳转页面类型")
	private Integer jumpPageType;

	@ApiModelProperty(value = "跳转地址", hidden = true)
	private String jumpUrl;

	@ApiModelProperty(value = "回调地址", hidden = true)
	private String backUrl;

	@ApiModelProperty(value = "访问终端类型:1移动设备2PC")
	@NotNull(message="终端类型不能为空")
	private Long source;

	/*@ApiModelProperty(value = "是否由通商云进行认证 'true'", required = true)
	@NotNull(message = "是否认证不能为空")
	private String isAuth;

	@ApiModelProperty(value = "姓名")
	@NotNull(message = "姓名不能为空")
	private String name;

	@ApiModelProperty(value = "证件类型 1-身份证 2-护照")
	@NotNull(message = "证件类型不能为空")
	private String identityType;

	@ApiModelProperty(value = "证件号码")
	@NotNull(message = "证件号码不能为空")
	private String identityNo;*/
}
