package moonstone.web.core.user;

import io.terminus.common.exception.JsonResponseException;
import moonstone.user.enums.UserCertificationVerifyStatusEnum;
import moonstone.user.model.UserCertification;
import moonstone.user.service.UserCertificationReadService;
import moonstone.user.service.UserCertificationWriteService;
import moonstone.web.core.component.cert.RealNameCertificationComponent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class RealNameCertificationManager {

    @Resource
    private UserCertificationReadService userCertificationReadService;

    @Resource
    private UserCertificationWriteService userCertificationWriteService;

    @Autowired
    private RealNameCertificationComponent realNameCertificationComponent;

    /**
     * 保存指定用户的实名验证信息，该信息必须通过第三方接口的校验
     *
     * @param userId   用户id
     * @param realName 真实姓名
     * @param idNumber 身份证号码
     */
    public void saveUserCertification(Long userId, String realName, String idNumber) {
        //已经通过实名验证的信息
        var list = userCertificationReadService.findByUserId(userId).getResult();
        if (!CollectionUtils.isEmpty(list)) {
            list = list.stream()
                    .filter(existed -> existed.getPaperName().equals(realName) && existed.getPaperNo().equals(idNumber))
                    .collect(Collectors.toList());
            if (list.stream().anyMatch(passed -> UserCertificationVerifyStatusEnum.PASSED.getCode().equals(passed.getVerifyStatus()))) {
                return;
            }
        }
        var existedObject = getDefault(list);

        //匹配不上已有实名信息时，通过第三方接口校验
        if (!realNameCertificationComponent.realNameCertificationCheck(realName, idNumber)) {
            throw new JsonResponseException("支付人姓名与身份证号码一致性校验不通过");
        }

        //保存一下这个实名信息
        saveCertification(existedObject, userId, realName, idNumber);
    }

    private void saveCertification(UserCertification existedObject, Long userId, String realName, String idNumber) {
        UserCertification parameter = new UserCertification();
        parameter.setUserId(userId);
        parameter.setPaperName(realName);
        parameter.setPaperNo(idNumber);
        parameter.setVerifyStatus(UserCertificationVerifyStatusEnum.PASSED.getCode());

        if (existedObject == null) {
            userCertificationWriteService.create(parameter);
        } else {
            parameter.setId(existedObject.getId());
            userCertificationWriteService.update(parameter);
        }
    }

    private UserCertification getDefault(List<UserCertification> list) {
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        var defaultOne = list.stream().filter(UserCertification::getIsDefault).findAny();
        return defaultOne.orElseGet(() -> list.get(0));
    }
}
