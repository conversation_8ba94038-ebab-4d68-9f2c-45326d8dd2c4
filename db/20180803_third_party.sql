DROP TABLE IF EXISTS `parana_third_party_skus`;

CREATE TABLE `parana_third_party_skus` (
	`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
	`third_party_id` tinyint(6) NOT NULL COMMENT '第三方平台标识（1：洋800）',
	`outer_sku_id` varchar(32) NOT NULL COMMENT '第三方sku编码',
	`outer_sku_name` varchar(256) DEFAULT NULL COMMENT '第三方sku名称',
	`status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，-1禁用',
	`created_at` datetime DEFAULT NULL COMMENT '创建时间',
	`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='第三方sku表';

DROP TABLE IF EXISTS `parana_third_party_sku_shop`;

CREATE TABLE `parana_third_party_sku_shop` (
	`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL COMMENT '店铺id',
	`third_party_id` tinyint(6) NOT NULL COMMENT '第三方平台标识（1：洋800）',
	`outer_sku_id` varchar(32) NOT NULL COMMENT '第三方sku编码',
	`status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，-1禁用',
	`created_at` datetime DEFAULT NULL COMMENT '创建时间',
	`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='第三方sku绑定第三方用户名表';

DROP TABLE IF EXISTS `parana_third_party_user_shop`;

CREATE TABLE `parana_third_party_user_shop` (
	`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
	`shop_id` bigint(20) NOT NULL COMMENT '店铺id',
	`third_party_id` tinyint(6) NOT NULL COMMENT '第三方平台标识（1：洋800）',
	`third_party_key` varchar(50) DEFAULT NULL COMMENT '第三方平台用户key',
	`third_party_secret` varchar(50) NOT NULL COMMENT '第三方平台用户秘钥',
	`status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，-1禁用',
	`created_at` datetime DEFAULT NULL COMMENT '创建时间',
	`updated_at` datetime DEFAULT NULL COMMENT '更新时间',
	PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='第三方用户名绑定店铺表';

alter table parana_third_party_user_shop change column `third_party_key` `third_party_code` varchar(50) NOT NULL COMMENT '第三方平台用户标识码';
alter table parana_third_party_user_shop change column `third_party_secret` `third_party_key` varchar(50) NOT NULL COMMENT '第三方平台用户密钥';