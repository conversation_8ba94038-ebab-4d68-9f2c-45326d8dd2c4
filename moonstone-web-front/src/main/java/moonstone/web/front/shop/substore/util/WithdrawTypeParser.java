package moonstone.web.front.shop.substore.util;

import moonstone.common.model.AuthAble;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.web.core.model.dto.WithDrawProfitApplyCriteriaDTO;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
public interface WithdrawTypeParser {

    /**
     * 设置提现的搜索类型
     * 使用了bitmask进行设置类型
     *
     * @param type        类型
     * @param criteriaDTO 提现搜索请求
     */
    static void parseTheWithdrawType(int type, WithDrawProfitApplyCriteriaDTO criteriaDTO) {
        switch (type) {
            //全部
            case 0: {
                criteriaDTO.setOrderByAuth(true);
                break;
            }
            //未审核
            case 1: {
                criteriaDTO.setBitmasks(Stream.of(AuthAble.AuthStatus.AUTHED, AuthAble.AuthStatus.REJECT)
                        .map(AuthAble.AuthStatus::getValue).collect(Collectors.toList()));
                break;
            }
            //未操作
            case 2: {
                criteriaDTO.setBitmasks(Stream.of(AuthAble.AuthStatus.AUTHED)
                        .map(AuthAble.AuthStatus::getValue).collect(Collectors.toList()));
                criteriaDTO.setNotBitMasks(Stream.of(WithDrawProfitApply.WithdrawExtraStatus.WAITING, WithDrawProfitApply.WithdrawExtraStatus.PAID)
                        .map(WithDrawProfitApply.WithdrawExtraStatus::getMaskBit).collect(Collectors.toList()));
                criteriaDTO.getNotBitMasks().add(AuthAble.AuthStatus.REJECT.getValue());
                break;
            }
            //已操作
            case 3: {
                List<Integer> bitMasks = Arrays.asList(AuthAble.AuthStatus.AUTHED.getValue()
                        , WithDrawProfitApply.WithdrawExtraStatus.WAITING.getMaskBit());
                criteriaDTO.setBitmasks(bitMasks);
                criteriaDTO.setNotBitMasks(Stream.of(WithDrawProfitApply.WithdrawExtraStatus.PAID)
                        .map(WithDrawProfitApply.WithdrawExtraStatus::getMaskBit).collect(Collectors.toList()));
                break;
            }
            //已拒绝
            case 4: {
                criteriaDTO.setBitmasks(Stream.of(AuthAble.AuthStatus.REJECT)
                        .map(AuthAble.AuthStatus::getValue).collect(Collectors.toList()));
                criteriaDTO.setNotBitMasks(Stream.of(AuthAble.AuthStatus.AUTHED)
                        .map(AuthAble.AuthStatus::getValue).collect(Collectors.toList()));
                criteriaDTO.getNotBitMasks().add(WithDrawProfitApply.WithdrawExtraStatus.CLOSED.getMaskBit());
                criteriaDTO.getNotBitMasks().add(WithDrawProfitApply.WithdrawExtraStatus.ERROR.getMaskBit());
                break;
            }
            //已付款完成
            case 5: {
                criteriaDTO.setBitmasks(Arrays.asList(AuthAble.AuthStatus.AUTHED.getValue()
                        , WithDrawProfitApply.WithdrawExtraStatus.PAID.getMaskBit()));
                criteriaDTO.setNotBitMasks(Arrays.asList(AuthAble.AuthStatus.REJECT.getValue()
                        , WithDrawProfitApply.WithdrawExtraStatus.WAITING.getMaskBit()));
                break;
            }
            // 已关闭
            case 6: {
                criteriaDTO.setBitmasks(Collections.singletonList(WithDrawProfitApply.WithdrawExtraStatus.CLOSED.getMaskBit()));
                break;
            }
            // 线上支付的支付中
            case 7: {
                criteriaDTO.setBitmasks(Arrays.asList(AuthAble.AuthStatus.AUTHED.getValue()
                        , WithDrawProfitApply.WithdrawExtraStatus.PAYING.getMaskBit()));
                criteriaDTO.setNotBitMasks(Arrays.asList(AuthAble.AuthStatus.REJECT.getValue()
                        , WithDrawProfitApply.WithdrawExtraStatus.WAITING.getMaskBit(),
                        WithDrawProfitApply.WithdrawExtraStatus.PAID.getMaskBit()));
            }
            // 提现错误
            case 8: {
                criteriaDTO.setBitmasks(Collections.singletonList(WithDrawProfitApply.WithdrawExtraStatus.ERROR.getMaskBit()));
                break;
            }
            default:
                break;
        }
    }
}
