package moonstone.order.api;

import moonstone.order.model.ShopOrder;

/**
 * 用于分阶段支付的场景, 分阶段支付是店铺单维度的
 * Created by ya<PERSON><PERSON><PERSON> on 2016/11/11
 */
public interface PayStage {

    /**
     * 获取当前期数的支付百分比
     *
     * @param shopOrder 店铺单
     * @param stage     当前支付的期数
     * @return 支付百分比, 不包含百分号
     */
    String currentRate(ShopOrder shopOrder, Integer stage);

    /**
     * 获取当前期数的支付百分比
     *
     * @param shopOrderId 店铺单id
     * @param stage       当前支付的期数
     * @return 支付百分比, 不包含百分号
     */
    String currentRate(Long shopOrderId, Integer stage);

    /**
     * 判断是否是最后阶段的支付
     *
     * @param shopOrder 店铺单
     * @param stage     当前支付的期数
     * @return 是否最后一期
     */
    boolean isLastStage(ShopOrder shopOrder, Integer stage);

    /**
     * 判断是否是最后阶段的支付
     *
     * @param shopOrderId 店铺单id
     * @param stage       当前支付的期数
     * @return 是否最后一期
     */
    boolean isLastStage(Long shopOrderId, Integer stage);
}
