package moonstone.web.front.profit.web;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.utils.DateUtil;
import moonstone.common.utils.UserUtil;
import moonstone.order.service.ProfitWithdrawRecordReadService;
import moonstone.web.core.util.LockKeyUtils;
import moonstone.web.front.profit.convert.SubStoreWithdrawConvertor;
import moonstone.web.front.profit.view.SubStoreOrderProfitVO;
import moonstone.web.front.profit.view.SubStoreWithdrawRequest;
import moonstone.web.front.shop.substore.app.SubStoreWithdrawApp;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.locks.Lock;

@Slf4j
@RestController
public class SubStoreWithdrawController {

    @Resource
    private ProfitWithdrawRecordReadService profitWithdrawRecordReadService;

    @Resource
    private SubStoreWithdrawConvertor subStoreWithdrawConvertor;

    @Resource
    private SubStoreWithdrawApp subStoreWithdrawApp;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 小程序端，利润列表
     * <br/> 等量复刻自 moonstone.profit.route.clj 里的 query-by-order；前端不动，延用原来的url，出入参
     *
     * @param status
     * @param from
     * @param to
     * @return
     */
    @GetMapping("/api/subStoreWithdraw/queryByOrder")
    public APIResp<List<SubStoreOrderProfitVO>> queryByOrder(@RequestParam("status") Integer status, @RequestParam("from") String from,
                                                             @RequestParam("to") String to, @RequestParam("shopId") Long shopId) {
        var user = UserUtil.getCurrentUser();
        if (user == null) {
            return APIResp.error("用户未登录");
        }
        if (shopId == null || StringUtils.isBlank(from)) {
            return APIResp.error("缺少from或者shopId");
        }

        try {
            var list = profitWithdrawRecordReadService.findProfitByStatus(shopId, user.getId(), status,
                    DateUtil.parseDate(from), DateUtil.parseDate(to + " 23:59:59")).getResult();
            log.info("门店收益查询 {}", JSONUtil.toJsonStr(list));
            if (CollectionUtils.isEmpty(list)) {
                return APIResp.ok(Collections.emptyList());
            }

            List<SubStoreOrderProfitVO> result = subStoreWithdrawConvertor.convert(shopId, user.getId(), status, list);
            log.info("门店收益返回 {}", JSONUtil.toJsonStr(result));
            return APIResp.ok(result);
        } catch (Exception ex) {
            log.error("SubStoreWithdrawController.queryByOrder error, shopId={}, status={}, from={}, to={}",
                    shopId, status, from, to, ex);
            return APIResp.error(ex.getMessage());
        }
    }


    /**
     * 小程序端，门店发起提现申请
     * <br/> 等量复刻自 moonstone.profit.route.clj 里的 withdraw；前端不动，延用原来的url，出入参
     *
     * @param request
     * @return
     */
    @PostMapping("/api/subStoreWithdraw/withdrawByProfitId")
    public APIResp<Long> withdraw(@RequestBody SubStoreWithdrawRequest request) {
        var user = UserUtil.getCurrentUser();
        if (user == null) {
            return APIResp.error("用户未登录");
        }
        if (request == null || CollectionUtils.isEmpty(request.getList())) {
            return APIResp.error("提现金额不能为0");
        }
        if (request.getSourceId() == null) {
            return APIResp.error("商家id为空");
        }

        Lock lock = redissonClient.getLock(LockKeyUtils.withdrawProfitApplyCreate(request.getSourceId(), user.getId()));
        if (!lock.tryLock()) {
            return APIResp.error("当前正在发起提现申请，请勿重复操作");
        }

        try {
            subStoreWithdrawApp.withdrawProfit(subStoreWithdrawConvertor.convert(user, request));

            return APIResp.ok(request.getFee());
        } catch (Exception ex) {
            log.error("SubStoreWithdrawController.withdraw error, userId={}, request={}",
                    user.getId(), JSON.toJSONString(request), ex);
            return APIResp.error(ex.getMessage());
        } finally {
            lock.unlock();
        }
    }
}
