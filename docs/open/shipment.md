## 根据发货单id查询子订单信息

* 请求url
```
GET sku.orders.find.by.shipment.id
```

* 请求参数示例
shipmentId=100
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| shipmentId | 整形 | 发货单id | 是 |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": [
        {
          "id": 2509,
          "shopId": 18,
          "buyerId": 2,
          "fee": 15900,
          "status": 2,
          "promotionId": null,
          "extra": null,
          "tags": null,
          "createdAt": 1479739563000,
          "updatedAt": 1479739795000,
          "skuId": 5760,
          "quantity": 1,
          "orderId": 1926,
          "outId": null,
          "buyerName": "buyer",
          "outBuyerId": null,
          "itemId": 5001,
          "itemName": "it's skin伊思美肌红参蜗牛BB霜50ML 保湿裸妆遮瑕强韩国正品",
          "skuImage": "http://terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/14/9fe5515d-eb9f-4306-a751-f0439971026b.jpg",
          "shopName": "护肤美妆专营店",
          "outShopId": null,
          "companyId": null,
          "outSkuId": null,
          "skuAttributes": "[{\"attrKey\":\"颜色分类\",\"attrVal\":\"红色\"}]",
          "skuAttrs": [
            {
              "attrKey": "颜色分类",
              "attrVal": "红色",
              "unit": null,
              "showImage": null,
              "thumbnail": null,
              "image": null
            }
          ],
          "channel": 1,
          "payType": 1,
          "shipmentType": 1,
          "originFee": 15900,
          "discount": null,
          "shipFee": null,
          "shipFeeDiscount": null,
          "integral": null,
          "balance": null,
          "itemSnapshotId": 583,
          "hasRefund": false,
          "invoiced": false,
          "commented": null,
          "hasApplyAfterSale": null,
          "commissionRate": null,
          "diffFee": null
        }
      ]
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| result | List | 子订单列表 |  |


  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询订单失败"
     }
    ```