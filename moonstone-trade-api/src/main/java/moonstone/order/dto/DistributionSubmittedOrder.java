package moonstone.order.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Objects;

/**
 * Created by <PERSON>ai<PERSON>hy on 2018/12/17.
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DistributionSubmittedOrder extends SubmittedOrder {
    private static final long serialVersionUID = 8318812902189286201L;

    public List<? extends SubmittedSkusByShop> getDistributionSubmittedSkusByShops() {
        return Objects.nonNull(distributionSubmittedSkusByShops) ? distributionSubmittedSkusByShops : getSubmittedSkusByShops();
    }

    /**
     * 按照店铺归组的sku信息
     * 目前不再使用这个成员
     */
    private List<? extends SubmittedSkusByShop> distributionSubmittedSkusByShops;

    @Override
    public List<SubmittedSkusByShop> getSubmittedSkusByShops() {
        return Objects.nonNull(super.getSubmittedSkusByShops()) ? super.getSubmittedSkusByShops() : (List<SubmittedSkusByShop>) this.distributionSubmittedSkusByShops;
    }
}
