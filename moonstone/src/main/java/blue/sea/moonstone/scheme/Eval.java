package blue.sea.moonstone.scheme;

public class Eval implements SchemeFuc{
    @Override
    public AST execute(Executor executor, AST ast) {
        AST requireEvalCode = executor.eval(ast.lisp.get(1));
        if (!"quote".equals(requireEvalCode.lisp.get(0).name)){
            throw new RuntimeException("not quote eval able code -> " + ast
            );
        }
        return executor.eval(requireEvalCode.lisp.get(1));
    }
}
