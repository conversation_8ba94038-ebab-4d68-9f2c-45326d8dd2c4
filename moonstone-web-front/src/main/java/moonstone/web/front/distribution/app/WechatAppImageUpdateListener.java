package moonstone.web.front.distribution.app;

import lombok.AllArgsConstructor;
import moonstone.web.core.events.shop.ShopWxaProjectUpdateEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * clear the image for the wechat app
 */
@Component
@AllArgsConstructor
public class WechatAppImageUpdateListener {
    WechatAppImageRender wechatAppImageRender;


    @EventListener(ShopWxaProjectUpdateEvent.class)
    public void refreshTheImage(ShopWxaProjectUpdateEvent shopWxaProjectUpdateEvent) {
        wechatAppImageRender.clearImageForProject(shopWxaProjectUpdateEvent.getProjectId());
    }
}
