package moonstone.web.admin.promotion;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.EventSender;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.web.core.component.promotion.CouponOutlet;
import moonstone.web.core.events.promotion.PromotionTrackChangeEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

/**
 * 优惠券操作
 * Author:cp
 * Created on 7/11/16.
 */
@RestController
@RequestMapping("/api/coupon")
@Slf4j
public class Coupons {

    @RpcConsumer
    private UserReadService userReadService;
    @Autowired
    private CouponOutlet couponOutlet;

    @RequestMapping(value = "/{id}/give", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public boolean batchGive(@PathVariable("id") Long promotionId,
                             @RequestParam("userIds") Long[] userIds,
                             @RequestParam("quantity") int quantity) {
        if (userIds == null || userIds.length == 0) {
            throw new JsonResponseException("user.ids.can.not.be.empty");
        }
        if (quantity <= 0) {
            throw new JsonResponseException("quantity.must.be.positive");
        }

        List<Long> userList = new ArrayList<>(new HashSet<>(Arrays.asList(userIds)));
        Response<List<User>> findUsersResp = userReadService.findByIds(userList);
        if (!findUsersResp.isSuccess()) {
            log.error("fail to find users by ids:{},cause:{}",
                    userList, findUsersResp.getError());
            throw new JsonResponseException(findUsersResp.getError());
        }
        List<User> users = findUsersResp.getResult();

        if (users.size() != userList.size()) {
            log.error("some users not found,expect user ids:{},but actual:{}",
                    userList, users);
            throw new JsonResponseException("some.users.not.found");
        }

        couponOutlet.send(userList, promotionId, quantity);
        //更新promotionTrack的领券数量
        EventSender.sendApplicationEvent(new PromotionTrackChangeEvent(promotionId, userList.size() * quantity, PromotionTrackChangeEvent.ChangeType.RECEIVED_QUANTITY));
        return true;
    }

}
