/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.user.service;

import io.terminus.common.model.Response;

import java.util.List;
import java.util.Map;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-28
 */
public interface AdminUserService {

    /**
     * 更改用户标签
     *
     * @param userId 用户id
     * @param tags 标签
     * @return 是否成功
     */
    Response<Boolean> updateTags(Long userId, Map<String, String> tags);

    /**
     * 更新用户状态
     *
     * @param userId 用户id
     * @param status 状态
     * @return 是否更新成功
     */
    Response<Boolean> updateStatus(Long userId, Integer status);

    /**
     * 更新用户角色
     *
     * @param userId 用户ID
     * @param roles 角色列表
     * @return 是否更新成功
     */
    Response<Boolean> updateRoles(Long userId, List<String> roles);
}
