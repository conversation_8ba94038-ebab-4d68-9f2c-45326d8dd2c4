/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.category.service;

import io.terminus.common.model.Response;
import moonstone.category.model.BackCategory;

import java.util.List;

/**
 * 后台类目查询服务, 都是没有缓存的版本, 如果需要缓存版本, 请参考CategoryCacher
 *
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-14
 */
public interface BackCategoryReadService {
    /**
     * 通过 ID 查询后台类目, 没有缓存
     *
     * @param id 后台类目 ID
     * @return 后台类目
     */
    Response<BackCategory> findById(Long id);


    /**
     * 查询下级后台类目, 没有缓存
     *
     * @param pid 父级类目id
     * @return 子级类目列表
     */
    Response<List<BackCategory>> findChildrenByPid(Long pid);


    /**
     * 查询从一级类目到本类目的路径上的所有后台类目列表(包括本级类目), 没有缓存
     *
     * @param id 本级类目id
     * @return 从本级类目开始的路径上的所有祖先类目列表, 不包括虚拟根节点
     */
    Response<List<BackCategory>> findAncestorsOf(Long id);
}
