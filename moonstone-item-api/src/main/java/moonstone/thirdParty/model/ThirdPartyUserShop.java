package moonstone.thirdParty.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.common.model.EntityBase;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class ThirdPartyUserShop extends EntityBase implements Serializable {
    private static final long serialVersionUID = -579262103436487962L;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 第三方平台标识（1：洋800）
     */
    private Integer thirdPartyId;

    /**
     * 第三方平台用户标识码
     */
    private String thirdPartyCode;

    /**
     * 第三方平台用户秘钥
     */
    private String thirdPartyKey;
}
