package moonstone.web.front.shop.substore.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.order.service.WithDrawProfitApplyReadService;
import moonstone.user.enums.DataExportTaskTypeEnum;
import moonstone.web.core.model.dto.WithDrawProfitApplyCriteriaDTO;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.core.util.BaseExporter;
import moonstone.web.front.profit.application.WithdrawProfitApplyNewExporter;
import moonstone.web.front.profit.convert.WithdrawProfitApplyNewExportConvertor;
import moonstone.web.front.profit.view.WithdrawProfitApplyExcelExportView;
import moonstone.web.front.shop.substore.convert.WithdrawProfitApplyExportConvertor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 老 提现申请 的导出
 */
@Slf4j
@Component
public class WithdrawProfitApplyExporter extends BaseExporter<WithdrawProfitApplyExcelExportView, WithDrawProfitApplyCriteriaDTO> {

    @Resource
    private WithdrawProfitApplyNewExporter withdrawProfitApplyNewExporter;

    @Resource
    private WithDrawProfitApplyReadService withDrawProfitApplyReadService;

    @Resource
    private WithdrawProfitApplyExportConvertor withdrawProfitApplyExportConvertor;

    @Override
    protected DataExportTaskTypeEnum getCurrentTaskType() {
        return DataExportTaskTypeEnum.WITHDRAW_PROFIT_APPLY_OLD;
    }

    @Override
    protected List<WithdrawProfitApplyExcelExportView> findPageDataList(WithDrawProfitApplyCriteriaDTO queryParameter, int pageNo, int pageSize) {
        var resultList = findList(queryParameter, pageNo, pageSize);
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }

        //查询角色快照信息，填充对应的字段信息
        withdrawProfitApplyNewExporter.appendByRoleSnapshot(resultList);

        //填充平台信息
        withdrawProfitApplyNewExporter.appendShopInfo(resultList);

        //填充提现账户信息
        withdrawProfitApplyNewExporter.appendWithdrawAccountInfo(resultList);

        //填充对应的角色名称（导购/门店/服务商）
        appendUserRoleName(resultList);

        //填充用户的基本信息
        withdrawProfitApplyNewExporter.appendUserInfo(resultList);

        return resultList;
    }

    /**
     * 填充对应的角色名称（导购/门店/服务商）
     *
     * @param resultList
     */
    private void appendUserRoleName(List<WithdrawProfitApplyExcelExportView> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        resultList.forEach(target -> withdrawProfitApplyExportConvertor.appendUserRoleName(target));
    }

    /**
     * 基础信息查询
     *
     * @param queryParameter
     * @param pageNo
     * @param pageSize
     * @return
     */
    private List<WithdrawProfitApplyExcelExportView> findList(WithDrawProfitApplyCriteriaDTO queryParameter, int pageNo, int pageSize) {
        queryParameter.setPageNo(pageNo);
        queryParameter.setPageSize(pageSize);

        var list = withDrawProfitApplyReadService.pageList(queryParameter).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        //基本的模型转换
        return WithdrawProfitApplyNewExportConvertor.convert(list);
    }
}
