## 商品详情页上半部分

* 请求url
```
GET item.view.find.by.id
```

* 请求参数示例
```
itemId=4
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| itemId | 整形 | 商品id | 是 |  |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": {
        "item": {
          "id": 4,
          "itemCode": "************",
          "categoryId": 952,
          "spuId": null,
          "shopId": 1,
          "shopName": "浙江天猫供应链管理有限公司",
          "brandId": 28,
          "brandName": "联想（LENOVO）",
          "name": "Lenovo/联想 TAB 2 A7-30 移动-3G 16GB 少符22商品3",
          "mainImage": "http://parana.oss-cn-hangzhou.aliyuncs.com/2016/07/10/TB1VF8TJpXXXXbZXFXXXXXXXXXX_!!2-item_pic.png",
          "lowPrice": 5000,
          "highPrice": 5000,
          "stockType": 0,
          "stockQuantity": 973,
          "saleQuantity": 58,
          "status": 1,
          "onShelfAt": null,
          "advertise": null,
          "specification": "",
          "type": 1,
          "reduceStockType": 1,
          "extra": {
            "unit": "件",
            "selfPlatformLink": "",
            "unitQuantity": ""
          },
          "tags": {
            "promotionTypes": ""
          },
          "tagsJson": "{\"promotionTypes\":\"\"}",
          "itemInfoMd5": "730e470cd4d6216821e2b8064fed0602",
          "createdAt": 1458098389000,
          "updatedAt": 1470992401000
        },
        "groupedSkuAttrs": [
          {
            "attrKey": "颜色分类",
            "skuAttributes": [
              {
                "attrKey": "颜色分类",
                "attrVal": "白色",
                "unit": null,
                "showImage": true,
                "thumbnail": null,
                "image": null
              }
            ]
          }
        ],
        "skus": [
          {
            "id": 5721,
            "skuCode": null,
            "itemId": 4,
            "shopId": 1,
            "status": 1,
            "specification": null,
            "outerSkuId": "",
            "outerShopId": null,
            "image": null,
            "thumbnail": null,
            "name": null,
            "extraPriceJson": "{\"platformPrice\":5000,\"originPrice\":10000}",
            "extraPrice": {
              "platformPrice": 5000,
              "originPrice": 10000
            },
            "price": 5000,
            "attrs": [
              {
                "attrKey": "颜色分类",
                "attrVal": "白色",
                "unit": null,
                "showImage": null,
                "thumbnail": null,
                "image": null
              }
            ],
            "stockType": 0,
            "stockQuantity": 973,
            "extra": {
              "unitQuantity": "1"
            },
            "createdAt": 1466841771000,
            "updatedAt": 1470737722000
          }
        ],
        "imageInfos": [
          {
            "name": null,
            "url": "https://img.alicdn.com/i2/2619709162/TB231vxhpXXXXXkXXXXXXXXXXXX_!!2619709162.png"
          },
          {
            "name": null,
            "url": "https://img.alicdn.com/i4/2619709162/TB2c0O3hpXXXXbWXpXXXXXXXXXX_!!2619709162.png"
          },
          {
            "name": null,
            "url": "https://img.alicdn.com/i2/2619709162/TB2H5zjhpXXXXb7XXXXXXXXXXXX_!!2619709162.png"
          },
          {
            "name": null,
            "url": "https://img.alicdn.com/i1/2619709162/TB2GLnxhpXXXXXlXXXXXXXXXXXX_!!2619709162.png"
          }
        ]
      }
    }
    ```

  - 正确结果说明
    返回结果分为四部分:商品信息(item)、sku属性按照属性key值归组(groupedSkuAttrs)、商品拥有的sku列表(skus)以及辅图信息(imageInfos)。

    商品信息(item):

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| id | 整形 | 商品id |  |
| itemCode | 字符串 | 商品编码 |  |
| categoryId | 整形 | 商品所属类目id |  |
| spuId | 整形 | 商品所属spu id | 不是基于spu发的商品,此项为空 |
| shopId | 整形 | 商品所属店铺 |  |
| shopName | 整形 | 店铺名称 |  |
| brandId | 整形 | 品牌id |  |
| brandName | 字符串 | 品牌名称 |  |
| name | 字符串 | 商品名称 |  |
| mainImage | 字符串 | 商品主图url |  |
| lowPrice | 整形 | 最低价 |  |
| highPrice | 整形 | 最高价 |  |
| stockType | 整形 | 库存类型 |  |
| stockQuantity | 整形 | 库存数量 |  |
| saleQuantity | 整形 | 销售数量 |  |
| status | 整形 | 商品状态 | 1: 上架, -1:下架, -2:冻结, -3:删除 |
| onShelfAt | 日期 | 计划上架时间 |  |
| advertise | 字符串 | 广告语 |  |
| specification | 字符串 | 型号 |  |
| type | 整形 | 商品类型 | 1.普通商品, 2.组合商品 |
| reduceStockType | 整形 | 减库存方式 | 减库存方式,1为拍下减库存, 2为付款减库存  |
| extra | json | 额外信息 |  |
| tags | json | 标签 |   |
| itemInfoMd5 | 字符串 | 当前商品信息的md5值 |   |
| createdAt | 日期 | 以毫秒数表示的商品创建日期 |  |
| updatedAt | 日期 | 以毫秒数表示的商品更新日期 |  |

     sku属性:

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| attrKey | 字符串 | 属性键 |  |
| attrVal | 字符串 | 属性值 |  |
| unit | 字符串 | 计量单位 |  |
| showImage | 布尔值 | 是否显示sku图片 |  |
| thumbnail | 字符串 | 属性对应的缩略图url |  |
| image | 字符串 | 属性对应的原图url |  |


     sku信息:

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| id | 整形 | sku id |  |
| skuCode | 字符串 | sku编码 |  |
| itemId | 整形 | sku所属商品id |  |
| shopId | 整形 | sku所属店铺id |  |
| status | 整形 | sku状态 |  |
| specification | 字符串 | sku型号 |  |
| outerSkuId| 字符串 | 外部sku id |  |
| outerShopId | 字符串 | 外部店铺id |   |
| image | 字符串 | sku图片url |  |
| thumbnail | 字符串 | sku缩略图url |  |
| name | 字符串 | sku名称 |  |
| extraPrice | json | sku额外价格 |  |
| price | 整形 | sku价格|   |
| attrs | json | sku 属性列表 |  |
| stockType | 整形 | sku库存类型 |  |
| stockQuantity | 整形 | sku库存|
| extra | json | sku额外信息 |  |
| createdAt | 日期 | 以毫秒数表示的sku创建时间 |  |
| updatedAt | 日期 | 以毫秒数表示的sku更新时间 |  |

     商品辅图(imageInfos):

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| name | 字符串 | 辅图名称 |  |
| url | 字符串 | 辅图url |  |

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "商品不存在"
     }
    ```


## 商品详情页下半部分

* 请求url
```
GET item.detail.find.by.id
```

* 请求参数示例
```
itemId=4
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| itemId | 整形 | 商品id | 是 |  |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": {
        "itemDetail": {
          "itemId": 4,
          "images": [
            {
              "name": null,
              "url": "https://img.alicdn.com/i2/2619709162/TB231vxhpXXXXXkXXXXXXXXXXXX_!!2619709162.png"
            },
            {
              "name": null,
              "url": "https://img.alicdn.com/i4/2619709162/TB2c0O3hpXXXXbWXpXXXXXXXXXX_!!2619709162.png"
            },
            {
              "name": null,
              "url": "https://img.alicdn.com/i2/2619709162/TB2H5zjhpXXXXb7XXXXXXXXXXXX_!!2619709162.png"
            },
            {
              "name": null,
              "url": "https://img.alicdn.com/i1/2619709162/TB2GLnxhpXXXXXlXXXXXXXXXXXX_!!2619709162.png"
            }
          ],
          "packing": null,
          "service": null,
          "detail": "<p>这是详情...</p>",
          "createdAt": 1458098389000,
          "updatedAt": 1468724668000
        },
        "groupedOtherAttributes": [
          {
            "group": "DEFAULT",
            "otherAttributes": [
              {
                "attrKey": "CPU主频",
                "attrVal": "1.3GHz",
                "unit": null,
                "group": "DEFAULT",
                "readOnlyBySeller": null
              },
              {
                "attrKey": "WiFi功能",
                "attrVal": "none",
                "unit": null,
                "group": "DEFAULT",
                "readOnlyBySeller": null
              }
            ]
          }
        ]
      }
    }
    ```

  - 正确结果说明
    返回结果分为两部分:商品详情(itemDetail)和分组属性(groupedOtherAttributes)

    商品详情(itemDetail):

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| itemId | 整形 | 商品id |  |
| images.name | 字符串 | 辅图名称 |  |
| images.url | 字符串 | 辅图url |  |
| packing | json | 包装清单 |  |
| service | 字符串 | 售后服务 |  |
| detail | 字符串 | 富文本表现形式的商品详情 |  |
| createdAt | 日期 | 以毫秒数表示的商品详情创建日期 |  |
| updatedAt | 日期 | 以毫秒数表示的商品详情更新日期 |  |

    分组属性(groupedOtherAttributes):

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|
| attrKey | 字符串 | 属性键 |  |
| attrVal | 字符串 | 属性值 |  |
| unit | 字符串 | 计量单位 |  |
| group | 字符串 | 属性组 |  |
| readOnlyBySeller | 布尔 | 该属性是否允许商家编辑 |  |

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "商品不存在"
     }
    ```