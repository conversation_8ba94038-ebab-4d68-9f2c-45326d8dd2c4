package moonstone.web.core.front.image;

import configuration.web.FrontWebConfiguration;
import moonstone.user.model.UserProfile;
import moonstone.web.core.BaseWebTest;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.context.ContextConfiguration;

import static org.hamcrest.core.Is.is;

/**
 * Author:cp
 * Created on 9/3/16.
 */
@ContextConfiguration(classes = {FrontWebConfiguration.class})
public class ImageHandlerTest extends BaseWebTest {

    @Test
    public void testAvatar() {
        String completePath = "//terminus-designer.oss-cn-hangzhou.aliyuncs.com/2016/07/22/05f73408-82fa-4390-99ef-e81a0132b4a1.jpg";
        UserProfile userProfile = new UserProfile(0L);
        userProfile.setAvatar(completePath);
        Assert.assertThat(userProfile.getAvatar(), is("/2016/07/22/05f73408-82fa-4390-99ef-e81a0132b4a1.jpg"));
        Assert.assertThat(userProfile.getAvatar_(), is("https:" + completePath));
    }

}
