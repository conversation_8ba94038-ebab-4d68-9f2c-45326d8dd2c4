package moonstone.web.op.allinpay.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

@Slf4j
@Data
public class GetMemberInfoVO implements Serializable {


    private String bizUserId;

    /**
     * 企业会员	2	整型
     * 个人会员	3	整型
     */
    private Long memberType;

    private MemberInfo memberInfo;

    public static GetMemberInfoVO init(String data) {
        JSONObject bizContent = JSONObject.parseObject(data);
        Long memberType = bizContent.getLong("memberType");
        String bizUserId = bizContent.getString("bizUserId");


        GetMemberInfoVO vo = new GetMemberInfoVO();
        vo.setBizUserId(bizUserId);
        vo.setMemberType(memberType);

        if(Long.parseLong("3") == memberType){
            PersonMemberInfo personMemberInfo = bizContent.getObject("memberInfo", PersonMemberInfo.class);
            vo.setMemberInfo(personMemberInfo);
        }else if (Long.parseLong("2") == memberType){
            CompanyMemberInfo companyMemberInfo = bizContent.getObject("memberInfo", CompanyMemberInfo.class);
            vo.setMemberInfo(companyMemberInfo);
        }else{
            log.error("错误的企业会员类型");
        }
        return vo;
    }

    @Data
    public static abstract class MemberInfo{}
    @Data
    public static class PersonMemberInfo extends MemberInfo {

        /** 姓名 */
        public String name;

        /**
         * 用户状态
         * (详细)
         * (必填)
         */
        public Long userState;

        /** 云商通用户id (必填) */
        public String userId;

        /** 国家 */
        public String country;

        /** 省份 */
        public String province;

        /** 县市 */
        public String area;

        /** 地址 */
        public String address;

        /** 手机号码 */
        public String phone;

        /** 身份证号码 (AES加密) */
        public String identityCardNo;

        /** 证件有效开始日期 (格式：9999-12-31) */
        public String identityBeginDate;

        /** 证件有效截止日期 (格式：9999-12-31) */
        public String identityEndDate;

        /** 是否绑定手机 */
        public Boolean isPhoneChecked;

        /** 创建时间 (yyyy-MM-dd HH:mm:ss) */
        public String registerTime;

        /** 创建ip */
        public String registerIp;

        /** 支付失败次数 */
        public Long payFailAmount;

        /** 是否进行实名认证 */
        public Boolean isIdentityChecked;

        /** 实名认证时间 (yyyy-MM-dd HH:mm:ss) */
        public String realNameTime;

        /** 备注 */
        public String remark;

        /**
         * 访问终端类型
         * (详细)
         * (必填)
         */
        public Long source;

        /** 是否已设置支付密码 */
        public Boolean isSetPayPwd;

        /**
         * 开户机构类型
         * 0-通联
         */
        public Long acctOrgType;

        /** 会员开通的通联子账号 */
        public String subAcctNo;

        /** 账户提现协议签订时间 */
        public String signAcctProtocolTime;

        /** 账户提现协议编号 */
        public String acctProtocolNo;

        // 你可以添加任何其他需要的方法或逻辑在这个DTO类中
    }
    @Data
    public static class CompanyMemberInfo extends MemberInfo {

        /** 企业名称 (必填) */
        public String companyName;

        /** 企业地址 */
        public String companyAddress;

        /**
         * 企业性质
         * 1-企业
         * 2-个体户
         * 3-事业单位
         * 无值，则不返回
         */
        public Long comproperty;

        /** 认证类型（三证或一证） (必填) */
        public Long authType;

        /** 营业执照号（三证） */
        public String businessLicense;

        /** 组织机构代码（三证） */
        public String organizationCode;

        /** 统一社会信用（一证） */
        public String uniCredit;

        /** 税务登记证（三证） */
        public String taxRegister;

        /** 统一社会信用/营业执照号到期时间 (格式：yyyy-MM-dd) */
        public String expLicense;

        /** 联系电话 */
        public String telephone;

        /** 手机号码 */
        public String phone;

        /** 法人姓名 (必填) */
        public String legalName;

        /**
         * 法人证件类型
         * 详细
         * (必填)
         */
        public Integer identityType;

        /** 法人证件号码 (AES加密，详细, 必填) */
        public String legalIds;

        /** 证件有效开始日期 (格式：9999-12-31) */
        public String identityBeginDate;

        /** 证件有效截止日期 (格式：9999-12-31) */
        public String identityEndDate;

        /** 法人手机号码 (必填) */
        public String legalPhone;

        /**
         * 账户类型
         * 0-对私
         * 1- 对公
         * 无值，则不返回
         */
        public Long acctType;

        /**
         * 企业账号
         * 对私银行账户四要素认证失败，则不返回
         * 对公账户，必返
         * (AES加密，详细)
         */
        public String accountNo;

        /** 对公户名 (仅账户类型对公时有值) */
        public String accountName;

        /** 开户银行名称 (对公账户，必返) */
        public String parentBankName;

        /** 开户行地区代码 (根据中国地区代码表) */
        public String bankCityNo;

        /** 开户行支行名称 */
        public String bankName;

        /** 支付行号，12位数字 */
        public String unionBank;

        /** 开户行所在省 */
        public String province;

        /** 开户行所在市 */
        public String city;

        /** 是否已签电子协议 */
        public Boolean isSignContract;

        /** 审核状态 (详细, 必填) */
        public Long status;

        /** 审核时间 (yyyy-MM-dd HH:mm:ss) */
        public String checkTime;

        /** 备注 */
        public String remark;

        /** 审核失败原因 */
        public String failReason;

        /**
         * 开户机构类型
         * 0-通联
         */
        public Long acctOrgType;

        /** 会员开通的通联子账号 */
        public String subAcctNo;

        /** 云商通用户id (必填) */
        public String userId;

        /** 是否绑定手机 */
        public Boolean isPhoneChecked;

        /** 账户提现协议签订时间 */
        public String signAcctProtocolTime;

        /**
         * 对私银行账户认证结果
         * 2：认证成功。
         * 3：认证失败。
         */
        public Long accountSetResult;

        /** 账户提现协议编号 */
        public String acctProtocolNo;

        /**
         * OCR识别与企业工商认证信息是否一致
         * 0-否
         * 1-是
         */
        public Long ocrRegnumComparisonResult;

        /**
         * OCR识别与企业法人实名信息是否一致
         * 0-否
         * 1-是
         */
        public Long ocrIdcardComparisonResult;

        /** 比对结果说明 */
        public String resultInfo;

        /** 法人账户提现协议签订时间 */
        public String legalSignAcctProtocolTime;

        /** 法人账户提现协议编号 */
        public String legalAcctProtocolNo;

        // 你可以添加任何其他需要的方法或逻辑在这个DTO类中
    }

}
