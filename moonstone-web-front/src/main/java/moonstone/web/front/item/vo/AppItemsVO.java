package moonstone.web.front.item.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import moonstone.common.model.IdNameVO;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AppItemsVO {
    /**
     * 商品id
     */
    Long id;
    /**
     * 店铺id
     */
    Long shopId;
    /**
     * 是否保税(1:保税，0:完税)
     */
    Integer isBonded;
    /**
     * 广告语
     */
    String advertise;
    /**
     * 商品主图
     */
    String mainPic;
    /**
     * 商品类型
     */
    Integer type;
    /**
     * 辅图
     */
    List<String> subPics;
    /**
     * 商品名
     */
    String title;
    /**
     * 库存
     */
    Integer stockQuantity;
    /**
     * 销量
     */
    Integer saleQuantity;
    /**
     * 分享数
     */
    Integer shareNum;
    /**
     * 税率
     */
    BigDecimal rate;
    /**
     * 跨境仓库
     */
    String description;
    /**
     * 商品详情介绍  图片组
     */
    List<String> detail;
    /**
     * 店铺名
     */
    String shopName;
    /**
     * 店铺图标
     */
    String shopNamePic;
    /**
     * 来源国
     */
    JSONObject origin;
    /**
     * sku信息
     */
    List<AppSkuVO> skus;
    /**
     * 参数
     */
    String paramsName;
    /**
     * 拓展参数
     */
    List<IdNameVO> params;

    Boolean isFreeExp = false;

    Boolean isFreeTax = false;

    /**
     * 物流
     */
    String express;
    /**
     * 仓库
     */
    String warehouse;

    /**
     * 分享数
     */
    String share;

}
