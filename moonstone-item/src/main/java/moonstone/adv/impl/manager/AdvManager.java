package moonstone.adv.impl.manager;

import moonstone.adv.impl.dao.AdvDao;
import moonstone.adv.model.Adv;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by CaiZhy on 2018/12/4.
 */
@Component
public class AdvManager {
    @Autowired
    private AdvDao advDao;

    @Transactional
    public void batchUpdate(List<Adv> toUpdates) {
        for (Adv toUpdate : toUpdates) {
            advDao.update(toUpdate);
        }
    }
}
