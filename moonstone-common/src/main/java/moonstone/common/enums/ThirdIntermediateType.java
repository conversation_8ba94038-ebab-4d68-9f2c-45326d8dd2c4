package moonstone.common.enums;

import com.google.common.base.Objects;

/**
 * @Author: wuxian-yjp
 * @Date: 2019/8/22 18:07
 */
public enum ThirdIntermediateType {
    /**
     * sku
     */
    SKU(1),

    /**
     * item
     */
    ITEM(2),

    /**
     * shop
     */
    SHOP(3);

    private final Integer value;

    ThirdIntermediateType(int value) {
        this.value = value;
    }

    public final Integer value() {
        return value;
    }
    public int getValue() {
        return value;
    }

    public static ThirdIntermediateType fromInt(int value) {
        for (ThirdIntermediateType thirdIntermediateType : ThirdIntermediateType.values()) {
            if (Objects.equal(thirdIntermediateType.getValue(), value)) {
                return thirdIntermediateType;
            }
        }
        throw new IllegalArgumentException("unknown order level : " +value);
    }

}
