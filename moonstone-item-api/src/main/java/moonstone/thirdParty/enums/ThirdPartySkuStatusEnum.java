package moonstone.thirdParty.enums;

public enum ThirdPartySkuStatusEnum {
    valid(1, "启用"),
    invalid(-1, "禁用"),
    ;

    private final Integer code;
    private final String description;

    ThirdPartySkuStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
