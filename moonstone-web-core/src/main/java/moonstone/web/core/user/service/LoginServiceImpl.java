package moonstone.web.core.user.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import com.github.kevinsawicki.http.HttpRequest;
import io.terminus.common.exception.JsonResponseException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.enums.AppTypeEnum;
import moonstone.common.model.WxCommonUser;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UUID;
import moonstone.user.dto.WxSpBean;
import moonstone.user.model.User;
import moonstone.user.model.UserWx;
import moonstone.user.service.UserReadService;
import moonstone.user.service.UserWriteService;
import moonstone.user.service.UserWxReadService;
import moonstone.web.core.user.api.LoginService;
import moonstone.web.core.util.AlipayOpenAPIUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * Created by CaiZhy on 2018/10/10.
 */
@Slf4j
@Service
public class LoginServiceImpl implements LoginService {

    @Resource
    private UserWxReadService userWxReadService;

    @Resource
    private UserWriteService<User> userWriteService;

    @Resource
    private UserReadService<User> userReadService;

    @Resource
    private AlipayOpenAPIUtil alipayOpenAPIUtil;

    @Override
    public WxCommonUser wxaLogin(String wxCode, String appId, String appSecret) {
        if (ObjectUtils.isEmpty(wxCode)) {
            log.error("[op:wxaLogin] wxCode is empty");
            throw new JsonResponseException("login.wxa.wxCode.empty");
        }
        if (ObjectUtils.isEmpty(appId) || StringUtils.isEmpty(appSecret)) {
            log.error("[op:code2Session] appId or appSecret is empty, appId={}, appSecret={}", appId, appSecret);
            throw new JsonResponseException("appId.appSecret.empty");
        }

        WxSpBean sp = this.code2Session(wxCode, appId, appSecret);
        log.debug("[op:wxaLogin] wxCode={}, sp={}", wxCode, JSON.toJSONString(sp));
        if (sp == null || sp.getOpenid() == null) {
            throw Translate.exceptionOf("登录失败, 请检测AppId和密钥是否配置正确");
        }
        UserWx userWx = this.createSpUserWx(sp, appId, AppTypeEnum.WECHAT);
        log.debug("[op:wxaLogin] wxCode={}, userWx={}", wxCode, JSON.toJSONString(userWx));

        boolean isNewUserWx = judgeIsNewUserWx(userWx);

        // 构造 user
        var user = createUser(userWx);

        // 构造返回结果
        var wxParanaUser = convert(userWx, user, isNewUserWx);
        log.debug("[op:wxaLogin] user={}, wxCode={}", JSON.toJSONString(user), wxCode);

        return wxParanaUser;
    }

    @Override
    public WxCommonUser loginByAlipay(String authCode, String appId, String privateSecret, String publicSecret) {
        // 调用 alipay.system.oauth.token， 换取授权访问令牌
        var tokenResponse = alipayAuthTokenRequest(authCode, appId, privateSecret, publicSecret, null);

        // 构造 userWx
        var userWx = convertByAlipay(tokenResponse, appId);
        boolean isNewUserWx = judgeIsNewUserWx(userWx);

        // 构造 user
        var user = createUser(userWx);

        // 构造返回结果
        return convert(userWx, user, isNewUserWx);
    }

    private AlipaySystemOauthTokenResponse alipayAuthTokenRequest(String authCode, String appId, String privateSecret, String publicSecret,
                                                                  String appAuthToken) {
        // 基础校验
        checkForAlipay(authCode, appId, privateSecret, publicSecret);

        // 调用 alipay.system.oauth.token， 换取授权访问令牌
        var tokenResponse = alipayOpenAPIUtil.getAlipayAuthToken(authCode, null, appId, privateSecret,
                publicSecret, appAuthToken);
        if (tokenResponse == null || !tokenResponse.isSuccess()) {
            throw new RuntimeException("换取授权访问令牌失败");
        }

        return tokenResponse;
    }

    private boolean judgeIsNewUserWx(UserWx userWx) {
        List<UserWx> list = userWxReadService.queryByUnionIdAndAppType(userWx.getUnionId(), AppTypeEnum.from(userWx.getAppType()), 1);
        return CollectionUtils.isEmpty(list);
    }

    private WxCommonUser convert(UserWx userWx, User user, boolean isNewUserWx) {
        WxCommonUser wxParanaUser = new WxCommonUser();

        wxParanaUser.setIsNewUser(isNewUserWx);
        wxParanaUser.setOpenId(userWx.getOpenId());
        wxParanaUser.setId(user.getId());
        wxParanaUser.setName(Optional.ofNullable(userWx.getNickName()).orElse(user.getName()));

        wxParanaUser.setType(user.getType());
        wxParanaUser.setRoles(user.getRoles());
        wxParanaUser.setExtra(user.getExtra());
        wxParanaUser.setTags(user.getTags());

        wxParanaUser.setHasPassword(!ObjectUtils.isEmpty(user.getPassword()));
        wxParanaUser.setHasMobile(!ObjectUtils.isEmpty(user.getMobile()));

        return wxParanaUser;
    }

    private User createUser(UserWx userWx) {
        var appType = AppTypeEnum.from(userWx.getAppType());

        User user = userWxReadService.queryByUnionIdAndAppIdAndAppType(userWx.getUnionId(), userWx.getAppId(), appType);
        if (user == null) {
            user = userWxReadService.queryByOpenIdAndAppIdAndAppType(userWx.getOpenId(), userWx.getAppId(), appType);
        }

        if (user == null) {
            user = userWriteService.createByWx(userWx);
        }

        return user;
    }

    private UserWx convertByAlipay(AlipaySystemOauthTokenResponse tokenResponse, String appId) {
        UserWx userWx = new UserWx();

        String openId = StringUtils.hasText(tokenResponse.getUserId()) ? tokenResponse.getUserId() : tokenResponse.getOpenId();

        userWx.setAppId(appId);
        userWx.setAppType(AppTypeEnum.ALIPAY.getCode());
        userWx.setOpenId(openId);
        userWx.setAccessToken(tokenResponse.getAccessToken());
        userWx.setRefreshToken(tokenResponse.getRefreshToken());

        Date date = new Date();
        userWx.setAccessTime(date.getTime());
        userWx.setRefreshTime(date.getTime());

        if (!ObjectUtils.isEmpty(tokenResponse.getUnionId())) {
            userWx.setUnionId(tokenResponse.getUnionId());
        } else {
            userWx.setUnionId(openId);
        }
        userWx.setSessionKey(tokenResponse.getAccessToken());

        return userWx;
    }

    private void checkForAlipay(String authCode, String appId, String privateSecret, String publicSecret) {
        if (!StringUtils.hasText(authCode)) {
            throw new RuntimeException("授权码authCode为空");
        }
        if (!StringUtils.hasText(appId)) {
            throw new RuntimeException("appId为空");
        }
        if (!StringUtils.hasText(privateSecret)) {
            throw new RuntimeException("私钥为空");
        }
        if (!StringUtils.hasText(publicSecret)) {
            throw new RuntimeException("公钥为空");
        }
    }

    @Override
    public WxSpBean code2Session(String wxCode, String appId, String appSecret) {
        Map<String, Object> pMap = new HashMap<>();
        pMap.put("appid", appId);
        pMap.put("secret", appSecret);
        pMap.put("js_code", wxCode);
        pMap.put("grant_type", "authorization_code");
        String url = "https://api.weixin.qq.com/sns/jscode2session";
        log.info("[op:code2Session] pMap={}", JSON.toJSONString(pMap));
        HttpRequest request = HttpRequest.get(url, pMap, false);
        if (request.ok()) {
            String result = request.body();
            log.info("[op:code2Session] pMap={}, result={}", JSON.toJSONString(pMap), result);
            return JSONObject.parseObject(result, WxSpBean.class);
        } else {
            log.error("[op:code2Session] HttpRequest failed");
            throw new JsonResponseException(500, "code2Session.http.request.fail");
        }
    }

    @Override
    public WxSpBean alipayAuthToken(String authCode, String appId, String privateSecret, String publicSecret, String appAuthToken) {
        var response = alipayAuthTokenRequest(authCode, appId, privateSecret, publicSecret, appAuthToken);

        String openId = StringUtils.hasText(response.getUserId()) ? response.getUserId() : response.getOpenId();
        String unionId = StringUtils.hasText(response.getUnionId()) ? response.getUnionId() : openId;

        var bean = new WxSpBean();
        bean.setOpenid(openId);
        bean.setSession_key(response.getAccessToken());
        bean.setUnionid(unionId);
        bean.setErrcode(response.getCode());
        bean.setErrmsg(response.getMsg());

        return bean;
    }

    @Override
    public WxCommonUser wxaLoginNew(String wxCode, String appId, String appSecret, String mobile, String nickName) {
        // 调用已有老接口
        WxCommonUser wxParanaUser = wxaLogin(wxCode, appId, appSecret);
        // 同步更新新接口
        User updateUserMobileAndNickName = new User();
        User origin = userReadService.findById(wxParanaUser.getId()).getResult();
        updateUserMobileAndNickName.setId(wxParanaUser.getId());
        if (!ObjectUtils.isEmpty(mobile)) {
            updateUserMobileAndNickName.setMobile(mobile);
        }
        if (nickName != null && !nickName.equals("微信用户")) {
            // update the user wx if possible
            userWxReadService.findByAppIdAndUserId(appId, wxParanaUser.getUserId()).getResult().ifPresent(userWx -> {
                if (userWx.getNickName() == null || !userWx.getNickName().equals(nickName)) {
                    userWx.setNickName(nickName);
                    userWriteService.updateUserWx(appId, userWx.getOpenId(), userWx);
                }
            });
        }

        // old code
        if (origin.getName() == null) {
            updateUserMobileAndNickName.setName(nickName + origin.getId() + (System.currentTimeMillis() % 520));
            boolean noChangeFound = Objects.equals(origin.getName(), updateUserMobileAndNickName.getName())
                    && Objects.equals(origin.getMobile(), updateUserMobileAndNickName.getMobile());
            if (!noChangeFound) {
                try {
                    userWriteService.update(updateUserMobileAndNickName);
                } catch (Exception e) {
                    updateUserMobileAndNickName.setName(UUID.randomUUID().toString().substring(0, 12));
                    userWriteService.update(updateUserMobileAndNickName);
                }
            }
        }

        if (!ObjectUtils.isEmpty(mobile)) {
            wxParanaUser.setHasMobile(true);
        }
        return wxParanaUser;
    }

    private UserWx createSpUserWx(WxSpBean sp, String appId, AppTypeEnum appType) {
        return createSpUserWx(sp, appId, appType, null);
    }

    private UserWx createSpUserWx(WxSpBean sp, String appId, AppTypeEnum appType, String nickName) {
        UserWx userWx = new UserWx();
        userWx.setAppId(appId);
        userWx.setAppType(appType.getCode());
        userWx.setOpenId(sp.getOpenid());
        userWx.setAccessToken("");
        userWx.setRefreshToken("");
        userWx.setScope("");
        Date date = new Date();
        userWx.setAccessTime(date.getTime());
        userWx.setRefreshTime(date.getTime());

        userWx.setAvatarUrl("");
        if (!ObjectUtils.isEmpty(sp.getUnionid())) {
            userWx.setUnionId(sp.getUnionid());
        } else {
            userWx.setUnionId(sp.getOpenid());
        }
        userWx.setSessionKey(sp.getSession_key());

        userWx.setNickName(nickName);
        return userWx;
    }
}
