package moonstone.web.front.order;

import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.APIResp;
import moonstone.common.api.App;
import moonstone.common.api.remote.RemoteAPI;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.enums.ThirdPartySystem;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.common.model.IsPresent;
import moonstone.common.utils.*;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.OrderBase;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.model.Shop;
import moonstone.shop.service.ShopReadService;
import moonstone.web.core.component.api.Y800V3Api;
import moonstone.web.core.component.api.bo.y800v3.Y800OrderQuery;
import moonstone.web.core.component.cache.ThirdPartyUserShopCache;
import moonstone.web.core.util.LockKeyUtils;
import moonstone.web.front.order.component.ShipmentComponent;
import moonstone.web.front.order.service.impl.OrderAutoConfirmServiceImpl;
import moonstone.web.front.order.service.impl.ShipmentConfirmService;
import org.apache.ibatis.session.SqlSession;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import redis.clients.jedis.Jedis;
import redis.clients.util.Pool;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * Author:cp
 * Created on 5/26/16.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class Shipments {

    @RemoteAPI
    Y800V3Api y800V3Api;
    @Autowired
    SqlSession sqlSession;
    @Autowired
    ThirdPartyUserShopCache thirdPartyUserShopCache;
    @Autowired
    OrderAutoConfirmServiceImpl orderAutoConfirmServiceImpl;
    @Autowired
    ShipmentConfirmService shipmentConfirmService;
    @Resource
    ShopOrderReadService shopOrderReadService;
    @Resource
    SkuOrderReadService skuOrderReadService;
    @Autowired
    ShipmentComponent shipmentComponent;
    @Autowired
    BalanceDetailManager balanceDetailManager;
    @Resource
    BalanceDetailReadService balanceDetailReadService;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    Pool<Jedis> jedisPool;
    @Resource
    ShopReadService shopReadService;
    private String uuid = null;

    List<Long> shopList() {
        return shopReadService.allShopId().getResult();
    }

    @GetMapping("/shipment/query-status")
    public Either<moonstone.web.core.component.api.bo.y800v3.OrderStatus> queryStatus(Long orderId) throws Exception {
        ShopOrder shopOrder = sqlSession.selectOne("ShopOrder.findDeclaredIdInfo", Map.of("id", orderId));
        var thirdNo = shopOrder.getDeclaredId();
        var list = sqlSession.<SkuOrder>selectList("SkuOrder.findOuterSkuInfo", Map.of("orderId", orderId));
        var skuOrder = list.stream().filter(order -> order.getOuterSkuId() != null).findFirst().get();
        var userShop = thirdPartyUserShopCache.findBy(ThirdPartySystem.Y800_V2.Id(), shopOrder.getShopId());
        var accessCode = userShop.get().getThirdPartyCode();
        var depotCode = skuOrder.getDepotCode();
        if (depotCode == null) {
            depotCode = sqlSession.selectOne("ThirdPartySkuStock.findDepotCode", Map.of("outerSkuId", skuOrder.getOuterSkuId(),
                    "systemId", 1));
        }
        try (var api = y800V3Api) {
            api.setAppId(App.OMS_V3.appId());
            api.setSecret(App.OMS_V3.secret());
            return api.deliveryGetDetail(new Y800OrderQuery(thirdNo,
                    accessCode,
                    depotCode));
        }
    }

    /**
     * 给予一个默认导入的导入单
     *
     * @throws IOException IO错误
     */
    @GetMapping("/shipment/import-shipments.xls")
    public void giveDefaultImportExcel(HttpServletResponse httpServletResponse) throws IOException {
        HSSFWorkbook hssfWorkbook = new HSSFWorkbook();
        Sheet sheet = hssfWorkbook.createSheet();
        Row row = sheet.createRow(0);
        row.createCell(0).setCellValue(Translate.of("订单号"));
        row.createCell(1).setCellValue(Translate.of("快递单号(必填)"));
        row.createCell(2).setCellValue(Translate.of("快递公司名(顺丰)"));
        row.createCell(3).setCellValue(Translate.of("发货时间"));
        httpServletResponse.setHeader("Content-Disposition", "attachment: filename=\"导入快递单模板.xls\"");
        httpServletResponse.setContentType("application/octet-stream;charset=utf-8");
        hssfWorkbook.write(httpServletResponse.getOutputStream());
    }

    /**
     * 导入发货单
     *
     * @param multipartFile 发货单文件
     * @return 是否成功导入
     * @throws IOException IO错误
     */
    @PostMapping("/shipment/import-shipments")
    public R importShipment(@RequestParam(value = "file") MultipartFile multipartFile) throws IOException {
        CommonUser commonUser = UserUtil.getCurrentUser();
        if (java.util.Objects.isNull(commonUser)) {
            return R.error(-1001, "未登录");
        }
        Either<Long> result = shipmentComponent.importShipment(commonUser, multipartFile.getInputStream());
        if (result.isSuccess()) {
            return R.ok();
        } else {
            return R.error(result.getErrorMsg());
        }
    }

    @GetMapping("/shipment/auto-confirm-trigger")
    public void confirmShipment(Long shopId, @RequestParam(defaultValue = "14") Integer date) {

        Shop shop = shopReadService.findById(shopId).getResult();
        date = Optional.ofNullable(shop.getExtra())
                .map(e -> e.get(ShopExtra.AUTO_CONFIRM.getCode()))
                .map(Integer::parseInt)
                .orElse(date);
        OrderCriteria criteria = new OrderCriteria();
        Date beforeTheDay = Date.from(LocalDateTime.now().minusDays(date.longValue()).atZone(ZoneId.systemDefault()).toInstant());
        criteria.setShipmentEndAt(beforeTheDay);
        criteria.setShopId(shopId);
        criteria.setStatus(Collections.singletonList(OrderStatus.SHIPPED.getValue()));
        List<Long> shippedOrderIds = shopOrderReadService.listIdsBy(criteria).getResult();
        for (Long shippedOrderId : shippedOrderIds) {
            if (orderAutoConfirmServiceImpl.tryConfirm(shippedOrderId)) {
                log.info("Order[{}] CONFIRMED", shippedOrderId);
            }
        }
    }

    /**
     * 调用确定订单收货事件 通过Spring代理进行执行新的事务以保证eventBus事件正常运行
     * 收货由于没有做sql层事务保证 会出现瞬间多重收货 导致利润问题,因此添加锁
     *
     * @param orderId   订单Id
     * @param orderType 订单类型
     * @return 是否成功确定收货
     */
    private Either<Boolean> realConfirm(Long orderId, Integer orderType) {
        // hazelcast 锁在应用未形成正常集群或者脑裂的时候 不可靠
        if (redissonClient == null) {
            log.error("{} lockSystem is down for order [{}] level [{}]", LogUtil.getClassMethodName(), orderId, orderType);
            return realConfirmWithoutLock(orderId, orderType);
        } else {
            Long lockOrderId;
            if (orderType == OrderLevel.SHOP.getValue()) {
                lockOrderId = orderId;
            } else {
                lockOrderId = skuOrderReadService.findById(orderId).getResult().getOrderId();
            }
            String lockName = LockKeyUtils.getShipConfirmLockName(lockOrderId);
            log.debug("{} AUTO CONFIRM FOR ORDER [{}]", LogUtil.getClassMethodName(), orderId);
            return TaskWithLock.processWithLock(redissonClient.getLock(lockName), 10, TimeUnit.SECONDS, lockName, orderId,
                    orderId, (Function<Long, Either<Boolean>>) (a) -> realConfirmWithoutLock(a, orderType)).orElse(Either.error("可能陷入死锁,请重试或联系客服"));
        }
    }

    private Either<Boolean> realConfirmWithoutLock(Long orderId, int orderType) {
        try {
            if (orderType == OrderLevel.SHOP.getValue()) {
                shipmentConfirmService.confirmOrder(orderId);
            } else if (orderType == OrderLevel.SKU.getValue()) {
                shipmentConfirmService.confirmOrder(skuOrderReadService.findById(orderId).getResult().getOrderId());
            } else {
                return Either.error("UNKNOWN ORDER TYPE");
            }
            return Either.ok(true);
        } catch (Exception e) {
            log.error("FAIL TO CONFIRM ORDER[{}]", orderId, e);
            return Either.error(e);
        }
    }

    @RequestMapping(value = "/seller/ship", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Long createShipment(@RequestParam("orderId") Long orderId,
                               @RequestParam(value = "orderType", defaultValue = "1") Integer orderType,
                               @RequestParam("corpCode") String shipmentCorpCode,
                               @RequestParam("serialNo") String shipmentSerialNo) {
        CommonUser user = UserUtil.getCurrentUser();
        OrderLevel orderLevel = OrderLevel.fromInt(orderType);
        OrderBase order = findOrder(orderId, orderLevel);
        checkSellerAuth(user, order);
        shipmentComponent.checkIfCanShip(order, orderLevel);

        return shipmentComponent.doCreateShipment(orderId, orderLevel, shipmentCorpCode, shipmentSerialNo, shipmentCorpCode);
    }

    @RequestMapping(value = "/buyer/confirm", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public APIResp<Boolean> confirm(@RequestParam("orderId") Long orderId,
                                    @RequestParam(value = "orderType", defaultValue = "1") Integer orderType) {
        try {
            log.debug("Shipments.confirm, currentUserId={}, orderId={}, orderType={}, 用户手动确认收货", UserUtil.getUserId(),
                    orderId, orderType);
            OrderLevel orderLevel = OrderLevel.fromInt(orderType);
            OrderBase order = findOrder(orderId, orderLevel);
            checkBuyerAuth(order);

            var result = realConfirm(order.getId(), orderType);
            log.debug("Shipments.confirm, currentUserId={}, orderId={}, orderType={}, 用户手动确认收货结果={}", UserUtil.getUserId(),
                    orderId, orderType, result.getResult());

            if (result.isSuccess() && result.getResult()) {
                return APIResp.ok(true);
            } else {
                return APIResp.error(result.getErrorMsg());
            }
        } catch (Exception ex) {
            log.error("Shipments.confirm error, orderId={}, orderType={}", orderId, orderType, ex);
            return APIResp.error(ex.getMessage());
        }
    }

    @GetMapping("/payment/trigger-paid")
    public int triggerPaid(Long shopOrderId, @RequestParam(value = "force", defaultValue = "false") boolean force) {
        ShopOrder shopOrder = shopOrderReadService.findById(shopOrderId).getResult();
        OrderOutFrom outFrom = OrderOutFrom.fromCode(shopOrder.getOutFrom());
        if (!force) {
            BalanceDetailCriteria criteria = new BalanceDetailCriteria();
            criteria.setRelatedId(shopOrderId);
            criteria.setSourceId(shopOrder.getShopId());
            criteria.setType(ProfitType.InCome.getValue());
            if (balanceDetailReadService.count(criteria).getResult() > 0) {
                throw new RuntimeException(new Translate("已经触发过支付利润回调").toString());
            }
        }
        balanceDetailManager.earnForeseeProfit(shopOrder, outFrom);
        return 1;
    }

    @GetMapping("/payment/trigger-confirm")
    public String triggerConfirm(Long shopOrderId, @RequestParam(value = "force", defaultValue = "false") boolean force) {
        ShopOrder shopOrder = shopOrderReadService.findById(shopOrderId).getResult();
        OrderOutFrom outFrom = OrderOutFrom.fromCode(shopOrder.getOutFrom());
        if (!force) {
            BalanceDetailCriteria criteria = new BalanceDetailCriteria();
            criteria.setRelatedId(shopOrderId);
            criteria.setSourceId(shopOrder.getShopId());
            criteria.setType(ProfitType.InCome.getValue());
            criteria.setStatusBitMarks(Collections.singletonList(IsPresent.presentMaskBit.Present.getValue()));
            if (balanceDetailReadService.count(criteria).getResult() > 0) {
                throw new RuntimeException(new Translate("已经触发过确认收货利润回调").toString());
            }
        }
        balanceDetailManager.persistForeseeProfit(shopOrder);
        switch (outFrom) {
            case SUB_STORE -> {
                return String.format("门店订单[%s]", shopOrderId);
            }
            case LEVEL_Distribution -> {
                return String.format("分销订单[%s]", shopOrderId);
            }
            default -> {
                return "未知订单";
            }
        }
    }

    @PostMapping("/payment/batch-trigger-confirm")
    public APIResp<String> batchTriggerConfirm(@RequestBody BatchTriggerConfirmRequest request) {
        if (request == null || CollectionUtils.isEmpty(request.getShopOrderIds())) {
            return APIResp.error("入参为空");
        }

        var sb = new StringBuilder();
        boolean force = request.getForce() != null && request.getForce();
        for (var shopOrderId : request.getShopOrderIds()) {
            sb.append(triggerConfirm(shopOrderId, force));
            sb.append(",");
        }

        return APIResp.ok(sb.toString());
    }

    @Data
    public static class BatchTriggerConfirmRequest {
        private List<Long> shopOrderIds;
        private Boolean force;
    }

    private void checkBuyerAuth(OrderBase order) {
        CommonUser loginUser = UserUtil.getCurrentUser();
        if (!com.google.common.base.Objects.equal(loginUser.getId(), order.getBuyerId())) {
            throw new RuntimeException("订单不属于这个买家");
        }
    }

    private void checkSellerAuth(CommonUser loginUser, OrderBase order) {
        if (!Objects.equals(loginUser.getShopId(), order.getShopId())) {
            log.warn("Order[ShopId -> {}], User[ShopId -> {}]", order.getShopId(), loginUser.getShopId());
            throw new JsonResponseException("order.not.belong.to.seller");
        }
    }

    private OrderBase findOrder(Long orderId, OrderLevel orderLevel) {
        return orderLevel == OrderLevel.SHOP ? findShopOrder(orderId) : findSkuOrder(orderId);
    }

    private ShopOrder findShopOrder(Long orderId) {
        Response<ShopOrder> shopOrderResp = shopOrderReadService.findById(orderId);
        if (!shopOrderResp.isSuccess()) {
            log.error("fail to find shop order by id:{},cause:{}", orderId, shopOrderResp.getError());
            throw new JsonResponseException(shopOrderResp.getError());
        }
        return shopOrderResp.getResult();
    }

    private SkuOrder findSkuOrder(Long orderId) {
        Response<SkuOrder> skuOrderResp = skuOrderReadService.findById(orderId);
        if (!skuOrderResp.isSuccess()) {
            log.error("fail to find sku order by sku order id:{},cause:{}", orderId, skuOrderResp.getError());
            throw new JsonResponseException(skuOrderResp.getError());
        }
        return skuOrderResp.getResult();
    }
}
