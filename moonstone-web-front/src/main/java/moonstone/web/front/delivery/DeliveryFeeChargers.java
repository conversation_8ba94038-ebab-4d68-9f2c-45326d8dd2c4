package moonstone.web.front.delivery;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import io.terminus.common.utils.JsonMapper;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.TaxCacher;
import moonstone.common.enums.BondedType;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.component.dto.item.TaxAndOrigin;
import moonstone.component.item.component.TaxChecker;
import moonstone.component.membership.component.MembershipPriceChecker;
import moonstone.item.model.Sku;
import moonstone.item.service.SkuReadService;
import moonstone.order.api.DeliveryFeeCharger;
import moonstone.order.api.RichOrderMaker;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.SubmittedOrder;
import moonstone.order.dto.SubmittedSku;
import moonstone.order.model.ReceiverInfo;
import moonstone.order.service.ReceiverInfoReadService;
import moonstone.promotion.component.OrderCharger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Author:cp
 * Created on 6/15/16.
 */
@Slf4j
@RestController
@RequestMapping("/api/delivery-fee-charge")
public class DeliveryFeeChargers {

    @Autowired
    private RichOrderMaker richOrderMaker;

    @Autowired
    private DeliveryFeeCharger deliveryFeeCharger;

    @RpcConsumer
    private ReceiverInfoReadService receiverInfoReadService;

    @RpcConsumer
    private SkuReadService skuReadService;

    @RpcConsumer
    private MembershipPriceChecker membershipPriceChecker;

    @Autowired
    private TaxChecker taxChecker;

    @Autowired
    private TaxCacher taxCacher;

    @Autowired
    private OrderCharger orderCharger;

    private final static TypeReference<List<SubmittedSku>> LIST_OF_SUBMITTED_SKU =
            new TypeReference<List<SubmittedSku>>() {
            };

    @RequestMapping(value = "/sku", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, Object> chargeForSku(@RequestParam("skuId") Long skuId,
                                            @RequestParam(value = "quantity", defaultValue = "1") Integer quantity,
                                            @RequestParam("addressId") Integer addressId) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Map<String, Object> result = new HashMap<>();
        result.put("deliveryFee", deliveryFeeCharger.charge(skuId, quantity, addressId));

        Sku sku = skuReadService.findSkuById(skuId).getResult();
        if(BondedType.fromInt(sku.getType()).isBonded()){
            if (commonUser !=null) {
                membershipPriceChecker.check(sku, commonUser.getId());
            }

            TaxAndOrigin taxAndOrigin = taxCacher.getOneTaxAndOrigin(sku);
            result.put("tax",taxAndOrigin.getTax());
            result.put("originId",taxAndOrigin.getOriginId());
            result.put("origin",taxAndOrigin.getOrigin());
            result.put("originUrl",taxAndOrigin.getOriginUrl());
        }
        return result;
    }

    @Deprecated
    @RequestMapping(value = "/order-preview", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<Long, Integer> chargeForOrderPreview(@RequestParam("skuInfo") String skuIdAndQuantity,
                                                    @RequestParam("receiverInfoId") Long receiverInfoId) {
        final CommonUser buyer = UserUtil.getCurrentUser();
        try {
            List<SubmittedSku> submittedSkus = JsonMapper.JSON_NON_EMPTY_MAPPER.getMapper()
                    .readValue(skuIdAndQuantity, LIST_OF_SUBMITTED_SKU);

            if (CollectionUtils.isEmpty(submittedSkus)) {
                throw new JsonResponseException("sku.not.provided");
            }

            Response<ReceiverInfo> findResp = receiverInfoReadService.findById(receiverInfoId);
            if (!findResp.isSuccess()) {
                log.error("fail to find receiverInfo by id:{},cause:{}", receiverInfoId, findResp.getError());
                throw new JsonResponseException(findResp.getError());
            }
            ReceiverInfo receiverInfo = findResp.getResult();

            RichOrder richOrder = richOrderMaker.partial(submittedSkus, buyer, null);
            return deliveryFeeCharger.charge(richOrder.getRichSkusByShops(), receiverInfo);
        } catch (Exception e) {
            log.error("fail to charge delivery fee for order preview,sku info:{},cause:{}",
                    skuIdAndQuantity, Throwables.getStackTraceAsString(e));
            Throwables.propagateIfInstanceOf(e, JsonResponseException.class);
            throw new JsonResponseException("charge.delivery.fee.fail");
        }
    }

    @RequestMapping(value = "/order-preview", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public RichOrder chargeForOrderPreview(@RequestBody SubmittedOrder submittedOrder) {
        try {
            RichOrder richOrder = richOrderMaker.full(submittedOrder, UserUtil.getCurrentUser());
            orderCharger.charge(richOrder, null);
            return richOrder;
        } catch (Exception e) {
            log.error("fail to charge delivery fee for order preview,submittedOrder:{},cause:{}",
                    submittedOrder, Throwables.getStackTraceAsString(e));
            throw new JsonResponseException("charge.delivery.fee.fail");
        }
    }

    /**
     * 根据传入的UserId作为查询会员价的依据 获取对应sku运费及税费等
     * @param skuId skuId
     * @param quantity 数量
     * @param addressId 地址id
     * @param pid 查询会员价依据的用户Id
     * @return 运费及税费等
     */
    @RequestMapping(value = "/membershipByPid/sku", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, Object> chargeMembershipByPUserIdForSku(@RequestParam("skuId") Long skuId,
                                                               @RequestParam(value = "quantity", defaultValue = "1") Integer quantity,
                                                               @RequestParam("addressId") Integer addressId,
                                                               @RequestParam(value = "pid", required = false) Long pid) {
        CommonUser commonUser = UserUtil.getCurrentUser();
        Map<String, Object> result = new HashMap<>();
        result.put("deliveryFee", deliveryFeeCharger.charge(skuId, quantity, addressId));

        Sku sku = skuReadService.findSkuById(skuId).getResult();
        if(BondedType.fromInt(sku.getType()).isBonded()){
            if (pid != null) {
                membershipPriceChecker.check(sku, pid);
            }

            TaxAndOrigin taxAndOrigin = taxCacher.getOneTaxAndOrigin(sku);
            result.put("tax",taxAndOrigin.getTax());
            result.put("originId",taxAndOrigin.getOriginId());
            result.put("origin",taxAndOrigin.getOrigin());
            result.put("originUrl",taxAndOrigin.getOriginUrl());
        }
        return result;
    }
}
