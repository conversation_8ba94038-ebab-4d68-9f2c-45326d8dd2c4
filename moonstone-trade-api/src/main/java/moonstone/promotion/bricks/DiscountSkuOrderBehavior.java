package moonstone.promotion.bricks;

import com.google.common.collect.Maps;
import io.terminus.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.CommonUser;
import moonstone.item.model.Sku;
import moonstone.order.dto.RichSku;
import moonstone.promotion.annotation.PromotionBrick;
import moonstone.promotion.api.BrickType;
import moonstone.promotion.api.SkuOrderBehavior;
import moonstone.promotion.dto.PromotionContext;
import moonstone.promotion.dto.SkuWithDiscountInfo;
import moonstone.promotion.util.PromotionInfoExtractor;

import java.util.Map;

/**
 * 商品折扣
 * Author:cp
 * Created on 6/29/16.
 */
@PromotionBrick(key = "sku-discount", type = BrickType.BEHAVIOR)
@Slf4j
public class DiscountSkuOrderBehavior implements SkuOrderBehavior {

    @Override
    public boolean affectPay() {
        return true;
    }

    @Override
    public boolean userOwn(CommonUser buyer, Long promotionId) {
        return true;
    }

    @Override
    public Map<String, Object> extractSkuOrderPromotionInfo(RichSku richSku, PromotionContext promotionContext, Map<String, String> conditionParams, Map<String, String> behaviorParams) {
        Map<String, Object> promotionInfo = Maps.newHashMap();

        final Sku sku = richSku.getSku();
        SkuWithDiscountInfo skuWithDiscountInfo = PromotionInfoExtractor.extractFromDiscountSkuOrderBehavior(sku.getId(), behaviorParams);
        if (skuWithDiscountInfo.getDiscount() != null) {
            promotionInfo.put("discount", skuWithDiscountInfo.getDiscount());
        } else if (skuWithDiscountInfo.getReduceFee() != null) {
            promotionInfo.put("reduceFee", skuWithDiscountInfo.getReduceFee());
        } else {
            log.error("reduceFee or discount miss for sku(id={}", sku.getId());
            throw new ServiceException("reduce.fee.or.discount.miss");
        }
        return promotionInfo;
    }

    @Override
    public boolean condition(RichSku richSku, PromotionContext promotionContext, Map<String, String> conditionParams) {
        return true;
    }

    @Override
    public void execute(RichSku richSku, PromotionContext promotionContext, Map<String, String> behaviorParams) {
        Sku sku = richSku.getSku();
        SkuWithDiscountInfo skuWithDiscountInfo = PromotionInfoExtractor.extractFromDiscountSkuOrderBehavior(sku.getId(), behaviorParams);

        long promotionPrice;
        if (skuWithDiscountInfo.getReduceFee() != null) {
            promotionPrice = sku.getPrice() >= skuWithDiscountInfo.getReduceFee() ? sku.getPrice() - skuWithDiscountInfo.getReduceFee() : 0;
        } else if (skuWithDiscountInfo.getDiscount() != null) {
            promotionPrice = (long) Math.ceil(sku.getPrice() * (1.0 * skuWithDiscountInfo.getDiscount() / 10 / 10));
        } else {
            log.error("reduceFee or discount should be provided for sku(id={})", sku.getId());
            throw new ServiceException("promotion.reduce.fee.or.discount.miss");
        }

        richSku.setSkuPromotionPrice(promotionPrice);

        long originFee = sku.getPrice() * richSku.getQuantity();
        long afterFee = promotionPrice * richSku.getQuantity();
        richSku.setOriginFee(originFee);
        richSku.setFee(afterFee);
        richSku.setDiscount(originFee - afterFee);
    }
}
