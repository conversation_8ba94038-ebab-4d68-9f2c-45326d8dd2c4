package moonstone.web.core.fileNew.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * author：书生
 */
@Data
public class ShopMiniBindQrCodeSaveDto {


    /**
     * 店铺id
     */
    @NotNull(message = "店铺id不能为空")
    private Long shopId;

    /**
     * 店铺小程序id
     */
    @NotNull(message = "店铺小程序id不能为空")
    private Long shopWxaId;

    /**
     * 小程序类型（0：微信小程序，1：支付宝小程序）
     */
    @NotNull(message = "小程序类型不能为空")
    private Integer appType;

    /**
     * 路由规则url
     */
    @NotBlank(message = "路由规则url不能为空")
    private String routerUrl;

    /**
     * 匹配规则（EXACT：精准匹配，FUZZY：模糊匹配）
     */
    @NotBlank(message = "匹配规则不能为空")
    private String mode;

    /**
     * 小程序功能页地址
     */
    @NotBlank(message = "小程序功能页地址不能为空")
    private String pageRedirection;

}
