package moonstone.thirdParty.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import moonstone.common.enums.ThirdPartySystem;

/**
 * 第三方系统帐号复合索引
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ThirdSystemAID {
    /**
     * @see ThirdPartySystem#Id()
     */
    Integer systemId;
    /**
     * @see ThirdPartyUserShop#getShopId()
     */
    Long shopId;
}
