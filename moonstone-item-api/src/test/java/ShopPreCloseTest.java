import com.google.common.collect.ImmutableMap;
import moonstone.common.constants.ShopExtra;
import moonstone.shop.model.Shop;
import moonstone.shop.slice.ShopFunctionSlice;
import org.junit.Test;

import java.time.LocalDateTime;
import java.time.ZoneId;

public class ShopPreCloseTest {
    @Test
    public void preCloseTest() {
        Shop shop = new Shop();
        assert !ShopFunctionSlice.build(shop).isProfitClosed();
        shop.setExtra(ImmutableMap.of(ShopExtra.PreClose.getCode(), "1"));
        assert ShopFunctionSlice.build(shop).isProfitClosed();
        shop.setExtra(ImmutableMap.of(ShopExtra.PreClose.getCode(),
                LocalDateTime.now().atZone(ZoneId.systemDefault()).toEpochSecond() + ""));
        assert !ShopFunctionSlice.build(shop).isProfitClosed();
        shop.setExtra(ImmutableMap.of(ShopExtra.PreClose.getCode(),
                LocalDateTime.now().minusSeconds(1).atZone(ZoneId.systemDefault()).toEpochSecond() + ""));
        assert ShopFunctionSlice.build(shop).isProfitClosed();
    }
}
