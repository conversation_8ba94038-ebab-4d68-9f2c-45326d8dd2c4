## 查询适用于指定商品的优惠券列表(给商品详情用)

* 请求url
```
GET item.coupon.list
```

* 请求参数示例
```
itemId=5144
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| itemId | 整形 | 商品id | 是 | |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": [
        {
          "promotion": {
            "id": 146,
            "shopId": 68,
            "name": "商品满200减50",
            "promotionDefId": 19,
            "type": 1,
            "status": 1,
            "startAt": 1483977600000,
            "endAt": 1515513600000,
            "conditionParams": {
              "conditionFee": "20000"
            },
            "behaviorParams": {
              "reduceFee": "5000"
            },
            "extra": {
              "sendLimit": "200",
              "receiveLimit": "200"
            },
            "createdAt": 1475895028000,
            "updatedAt": 1477929867000
          },
          "hasReceived": false
        },
        {
          "promotion": {
            "id": 202,
            "shopId": 68,
            "name": "商品满200减20",
            "promotionDefId": 19,
            "type": 1,
            "status": 1,
            "startAt": 1483977600000,
            "endAt": 1515513600000,
            "conditionParams": {
              "conditionFee": "20000"
            },
            "behaviorParams": {
              "reduceFee": "2000"
            },
            "extra": {
              "sendLimit": "200",
              "receiveLimit": "200"
            },
            "createdAt": 1478761535000,
            "updatedAt": 1478761535000
          },
          "hasReceived": false
        }
      ]
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询营销信息失败"
     }
    ```


## 查询适用于指定店铺的优惠券列表(给购物车页面使用)

* 请求url
```
GET shop.coupon.list
```

* 请求参数示例
```
shopId=1
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| shopId | 整形 | 店铺id | 是 | |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": [
        {
          "promotion": {
            "id": 12,
            "shopId": 1,
            "name": "店铺优惠券一",
            "promotionDefId": 18,
            "type": 2,
            "status": 1,
            "startAt": 1483977600000,
            "endAt": 1515513600000,
            "userScopeParams": null,
            "skuScopeParams": {},
            "conditionParams": {
              "conditionFee": "3000"
            },
            "behaviorParams": {
              "reduceFee": "1000"
            },
            "extra": {
              "sendLimit": "100",
              "receiveLimit": "1"
            },
            "createdAt": 1467972213000,
            "updatedAt": 1467972217000
          },
          "hasReceived": false
        },
        {
          "promotion": {
            "id": 13,
            "shopId": 1,
            "name": "店铺优惠券二",
            "promotionDefId": 18,
            "type": 2,
            "status": 1,
            "startAt": 1483977600000,
            "endAt": 1515513600000,
            "userScopeParams": null,
            "skuScopeParams": {},
            "conditionParams": {
              "conditionFee": "3000"
            },
            "behaviorParams": {
              "reduceFee": "1000"
            },
            "extra": {
              "sendLimit": "100",
              "receiveLimit": "1"
            },
            "createdAt": 1467972213000,
            "updatedAt": 1467972217000
          },
          "hasReceived": false
        }
      ]
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询营销信息失败"
     }
    ```


## 查询指定店铺的运费券列表

* 请求url
```
GET shop.shipment.coupon.list
```

* 请求参数示例
```
shopId=1
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| shopId | 整形 | 店铺id | 是 | |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": [
        {
          "promotion": {
            "id": 12,
            "shopId": 1,
            "name": "运费优惠券",
            "promotionDefId": 19,
            "type": 20,
            "status": 1,
            "startAt": 1483977600000,
            "endAt": 1515513600000,
            "userScopeParams": null,
            "skuScopeParams": {},
            "conditionParams": {
              "conditionFee": "3000"
            },
            "behaviorParams": {
              "reduceFee": "1000"
            },
            "extra": {
              "sendLimit": "100",
              "receiveLimit": "1"
            },
            "createdAt": 1467972213000,
            "updatedAt": 1467972217000
          },
          "hasReceived": false
        },
        {
          "promotion": {
            "id": 13,
            "shopId": 1,
            "name": "运费优惠券二",
            "promotionDefId": 19,
            "type": 20,
            "status": 1,
            "startAt": 1483977600000,
            "endAt": 1515513600000,
            "userScopeParams": null,
            "skuScopeParams": {},
            "conditionParams": {
              "conditionFee": "3000"
            },
            "behaviorParams": {
              "reduceFee": "1000"
            },
            "extra": {
              "sendLimit": "100",
              "receiveLimit": "1"
            },
            "createdAt": 1467972213000,
            "updatedAt": 1467972217000
          },
          "hasReceived": false
        }
      ]
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询营销信息失败"
     }
    ```



## 领券

* 请求url
```
POST coupon.receive
```

* 请求参数示例
```
promotionId=143
```

* 请求参数说明

| 参数名      | 类型 |  说明     |  必填   | 备注 |
|:-----------:|:----:|:--------:|:-----:|:-----:|
| promotionId | 整形 | 优惠券id | 是 | |

* 返回结果集

  - 正确结果示例，HTTP STATUS=200
    ```json
    {
      "success": true,
      "result": true
    }
    ```

  - 正确结果说明

| 参数名      | 类型 |  说明     | 备注 |
|:----------:|:----:|:--------:|:-----:|

  - 错误结果示例，HTTP STATUS=500
    ```json
    {
      "success": false,
      "error": "查询营销信息失败"
     }
    ```