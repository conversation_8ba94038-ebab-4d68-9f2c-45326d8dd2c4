package moonstone.web.front.profit.convert;

import com.alibaba.fastjson.JSONObject;
import moonstone.common.enums.SubStoreUserIdentityEnum;
import moonstone.common.utils.DateUtil;
import moonstone.order.model.OrderRoleSnapshot;
import moonstone.order.model.WithDrawProfitApply;
import moonstone.order.model.WithdrawAccount;
import moonstone.user.model.User;
import moonstone.web.front.profit.view.WithdrawProfitApplyExcelExportView;
import moonstone.web.op.allinpay.constant.WithdrawAccountAllinPayInfo;
import moonstone.web.op.allinpay.enums.AllinPayRoleTypeEnum;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Stream;

/**
 * 用于模型转换
 */
public class WithdrawProfitApplyNewExportConvertor {

    public static final String UNKNOWN_IDENTITY = "未知用户";

    public static List<WithdrawProfitApplyExcelExportView> convert(List<WithDrawProfitApply> sourceList) {
        if (CollectionUtils.isEmpty(sourceList)) {
            return Collections.emptyList();
        }

        return sourceList.stream()
                .filter(Objects::nonNull)
                .map(WithdrawProfitApplyNewExportConvertor::convert)
                .toList();
    }

    private static WithdrawProfitApplyExcelExportView convert(WithDrawProfitApply source) {
        WithdrawProfitApplyExcelExportView target = new WithdrawProfitApplyExcelExportView();

        target.setId(source.getId());
        target.setShopId(source.getSourceId());
        target.setUserRole(source.getUserRole());
        target.setWithdrawAccountId(source.getWithdrawAccountId());

        target.setAccountType("银行对私账户");
        target.setApplyAt(DateUtil.toString(source.getCreatedAt()));
        target.setPaySerialNo(source.getPaySerialNo() == null ? "" : source.getPaySerialNo());
        target.setShowStatus(convertShowStatus(source));

        target.setUserId(source.getUserId());

        String shouldPay = new BigDecimal(source.getFee())
                .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                .setScale(2, RoundingMode.HALF_DOWN)
                .subtract(new BigDecimal(Optional.ofNullable(source.getServiceFee()).orElse(0L))
                        .divide(new BigDecimal("100"), 2, RoundingMode.DOWN))
                .toString();
        target.setFee(shouldPay);

        var identityEnum = SubStoreUserIdentityEnum.parse(source.getUserRole());
        target.setIdentity(identityEnum == null ? UNKNOWN_IDENTITY : identityEnum.getDescription());

        var withdrawAt = DateUtil.toString(source.getWithDrawAt());
        target.setWithdrawAt(withdrawAt == null ? "" : withdrawAt);


        // 其实这里也可以直接查询提现账户表 而不是需要额外通联会员类型的快照到提现记录表
        Map<String, String> extra = source.getExtra();
        if (extra != null) {
            String allinpayRoleType = extra.get(WithdrawAccountAllinPayInfo.ALLINPAY_ROLE_TYPE);
            if(allinpayRoleType != null){
                AllinPayRoleTypeEnum allinPayRoleTypeEnum = AllinPayRoleTypeEnum.get(Long.parseLong(allinpayRoleType));
                if(allinPayRoleTypeEnum != null){
                    target.setAllinpayRoleType(allinPayRoleTypeEnum.getDesc());
                }
            }
        }

        String extraStr = source.getExtraStr();
        if(!StringUtils.isEmpty(extraStr)){
            JSONObject obj = JSONObject.parseObject(extraStr);
            target.setRemark(obj.getString("reason"));
            target.setServiceName(obj.getString("serviceName"));
        }

        return target;
    }

    private static String convertShowStatus(WithDrawProfitApply apply) {
        String showStatus = null;

        if (apply.isAuthed()) {
            showStatus = "已审核";
            if (apply.isWaiting()) {
                showStatus += "(已操作)";
            } else {
                showStatus += "(未操作)";
            }
            if (apply.isPaid()) {
                showStatus = "已支付";
            } else if (apply.isPaying()) {
                showStatus = "支付中";
            }
        } else if (apply.isReject()) {
            showStatus = "已拒绝";
        } else {
            showStatus = "未审核";
        }
        if (apply.isError()) {
            showStatus = "提现错误";
        }
        if (apply.isClosed()) {
            showStatus = "已关闭";
        }

        return showStatus;
    }

    public static void appendWithdrawAccountInfo(WithdrawProfitApplyExcelExportView target, WithdrawAccount account) {
        if (target == null || account == null) {
            return;
        }

        target.setBankName(convertBankName(account));
        target.setFrom(convertFrom(account));
        target.setOwnerName(StringUtils.hasText(account.getRealName()) ? account.getRealName() : account.getName());
        target.setType(convertType(account));
        target.setWithdrawAccount(Optional.ofNullable(account.getAccount()).orElse(account.getWithdrawWay()));
    }

    private static String convertType(WithdrawAccount account) {
        return WithDrawProfitApply.WithdrawPaidType.from(account.getType())
                .map(WithDrawProfitApply.WithdrawPaidType::getName)
                .orElse("");
    }

    private static String convertFrom(WithdrawAccount account) {
        StringBuilder from = new StringBuilder();

        if (Stream.of(account.getName(), account.getRealName()).anyMatch(StringUtils::hasText)) {
            from.append(Optional.ofNullable(account.getName()).orElseGet(account::getRealName));
        }
        if (StringUtils.hasLength(account.getFrom())) {
            from.append(" - ");
            from.append(account.getFrom());
        }

        return from.toString();
    }

    private static String convertBankName(WithdrawAccount account) {
        String bankName = account.getName();

        if (StringUtils.hasLength(account.getFrom())) {
            bankName = account.getFrom();
        }
        if (ObjectUtils.isEmpty(account.getRealName())) {
            bankName = account.getFrom();
        }

        return bankName;
    }

    public static void appendUserInfo(WithdrawProfitApplyExcelExportView target, User user) {
        if (target == null) {
            return;
        }

        if (!StringUtils.hasText(target.getMobile())) {
            target.setMobile(user != null && StringUtils.hasText(user.getMobile()) ? user.getMobile() : "");
        }

        if (!StringUtils.hasText(target.getUserName())) {
            String name = (user != null && StringUtils.hasText(user.getName())) ?
                    user.getName() : "用户" + target.getUserId() + "名字丢失";
            target.setUserName(name);
        }
    }

    public static void appendRoleSnapshotInfo(WithdrawProfitApplyExcelExportView view, List<OrderRoleSnapshot> snapshots) {
        if (view == null || CollectionUtils.isEmpty(snapshots)) {
            return;
        }

        snapshots.stream()
                .filter(snapshot -> snapshot.getUserId().equals(view.getUserId()))
                .forEach(snapshot -> {
                    view.setIdentity(SubStoreUserIdentityEnum.findDescription(snapshot.getUserRole()));
                    view.setMobile(snapshot.getMobile());
                    view.setUserName(snapshot.getName());
                });

    }
}
