package moonstone.order.rule;

import lombok.extern.slf4j.Slf4j;
import moonstone.common.exception.InvalidException;
import moonstone.item.model.Sku;
import moonstone.order.api.OrderChecker;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.RichSku;
import moonstone.order.dto.RichSkusByShop;

/**
 * Desc: 下单预览页库存校验逻辑
 * Mail: <EMAIL>
 * Data: 16/7/27
 * Author: yangzefeng
 */
@Slf4j
public class PreOrderStockChecker implements OrderChecker {

    /**
     * 检查库存,库存不足时不抛异常,返回标记位,用于订单预览页
     *
     * @param richOrder 订单信息
     */
    @Override
    public void canBuy(RichOrder richOrder) throws InvalidException {
        for (RichSkusByShop skusByShop : richOrder.getRichSkusByShops()) {
            for (RichSku richSku : skusByShop.getRichSkus()) {
                final Sku sku = richSku.getSku();
                if (sku.getStockQuantity() < richSku.getQuantity()) {
                    // 仅需检查 sku 库存数量
                    log.warn("sku(id={}) stock quantity not enough:{}, required:{}", sku.getId(), sku.getStockQuantity(), richSku.getQuantity());
                    richSku.setMask(0);
                } else {
                    richSku.setMask(1);
                }
            }
        }
    }
}
