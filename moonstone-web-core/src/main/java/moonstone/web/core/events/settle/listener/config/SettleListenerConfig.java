package moonstone.web.core.events.settle.listener.config;

import moonstone.web.core.events.settle.listener.CreateCommissionRateListener;
import moonstone.web.core.events.settle.listener.PayTransCollectedListener;
import moonstone.web.core.events.settle.listener.SettleAbnormalListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 结算相关的事件配置
 *
 * 以下这些bean的顺序按事件发生的先后顺序来排列
 *
 * DATE: 16/8/13 下午2:52 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
@Configuration
@ConditionalOnProperty(name = "settle.moonstone.web.core.component.listener.enable", havingValue = "true", matchIfMissing = true)
public class SettleListenerConfig {

    /**
     * 在店铺创建时创建店铺的费率
     * @return
     */
    @Bean
    public CreateCommissionRateListener commissionRateCreateListener(){
        return new CreateCommissionRateListener();
    }

    /**
     * 处理结算出现的异常
     * @return
     */
    @Bean
    public SettleAbnormalListener settleAbnormalListener(){
        return new SettleAbnormalListener();
    }

    @Bean
    public PayTransCollectedListener payTransCollectedListener(){
        return new PayTransCollectedListener();
    }


}
