package moonstone.web.front.order;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import io.terminus.common.exception.JsonResponseException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.cache.ShopCacheHolder;
import moonstone.common.api.APIResp;
import moonstone.common.api.Result;
import moonstone.common.constants.ShopExtra;
import moonstone.common.enums.OpBusinessType;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.exception.ApiException;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.*;
import moonstone.order.dto.ReturnInfo;
import moonstone.order.dto.fsm.OrderEvent;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.enu.RefundReasonType;
import moonstone.order.model.*;
import moonstone.order.service.PaymentReadService;
import moonstone.order.service.RefundReadService;
import moonstone.order.service.RefundWriteService;
import moonstone.web.core.fileNew.dto.RefundDto;
import moonstone.web.core.fileNew.dto.RefundReqDto;
import moonstone.web.core.order.cache.OrderStaticPartCache;
import moonstone.web.core.refund.application.RefundForBuyerApplication;
import moonstone.web.core.refund.application.RefundForSellerApplication;
import moonstone.web.front.order.dto.SimpleOrderRelationImpl;
import org.joda.time.DateTime;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Stream;

/**
 * 退款相关接口
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
@AllArgsConstructor
public class Refunds {

    private final RefundForBuyerApplication refundForBuyerApplication;
    private final RefundForSellerApplication refundForSellerApplication;
    private final RefundReadService refundReadService;
    private final PaymentReadService paymentReadService;
    private final OrderStaticPartCache orderStaticPartCache;
    private final ShopCacheHolder shopCacheHolder;
    private final RefundWriteService refundWriteService;


    /**
     * 当前不能跨订单申请退款, 但是支持同一订单的多个子订单申请退款
     *
     * @param buyerNote 买家备注
     * @param orderIds  (子)订单列表
     * @param orderType 订单级别
     * @return 创建的退款单id
     */
    @MyLog(title = "订单管理", value = "申请退款", opBusinessType = OpBusinessType.OTHER)
    @RequestMapping(value = "/api/buyer/order/refund", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Long applyRefund(@RequestParam(value = "buyerNote", required = false) String buyerNote,
                            @RequestParam("orderIds") List<Long> orderIds,
                            @RequestParam(value = "orderType", defaultValue = "2") Integer orderType,
                            @RequestParam("imagesJson") String imagesJson,
                            @RequestParam("reasonType") Integer reasonType) {
        if (CollectionUtils.isEmpty(orderIds)) {
            log.error("no orderIds specified when apply refund");
            throw new JsonResponseException("order.refund.fail");
        }
        try {
            log.info("申请退款 请求参数 订单id {}", JSONUtil.toJsonStr(orderIds));
            CommonUser buyer = UserUtil.requireLoginUser();
            List<OrderRelation> relationList = new ArrayList<>(orderIds.size());
            boolean isShop = false;
            long orderShopId = -1;
            boolean confirmed = false;

            for (Long orderId : orderIds) {
                relationList.add(new SimpleOrderRelationImpl(orderId, orderType));

                OrderBase order;
                if (OrderLevel.fromInt(orderType) == OrderLevel.SHOP) {
                    order = orderStaticPartCache.getOrderStaticPartByOrderId(orderId).get();
                } else {
                    order = orderStaticPartCache.getOrderStaticPartBySubOrderId(orderId).get();
                }

                orderShopId = order.getShopId();
                if (Objects.equals(orderShopId, buyer.getShopId()) && !isShop) {
                    isShop = true;
                }
                if (OrderStatus.CONFIRMED.getValue() == order.getStatus()) {
                    confirmed = true;
                }
            }

            var refundType = confirmed ? Refund.RefundType.AFTER_SALE_RETURN : Refund.RefundType.ON_SALE_REFUND;
            if (isShop || judgeIfRefundAble(orderIds, orderType)) {
                return refundForBuyerApplication.apply(buyer, relationList, buyerNote,
                        StringUtils.isBlank(imagesJson) ? Collections.emptyList() : Json.parseObject(imagesJson, new TypeReference<List<String>>() {
                        }),
                        refundType, RefundReasonType.from(reasonType), null).take();
            }
            log.error("{} reject refund by timeLimit orderIds[{}] orderType[{}] operator:{} order-shopId:{}", LogUtil.getClassMethodName(), JSON.toJSONString(orderIds), orderType, buyer.getShopId(), orderShopId);
            throw new JsonResponseException("order.refund.fail");
        } catch (Exception e) {
            log.error("failed to apply refund for order(ids={}, level={}), cause:{}",
                    orderIds, orderType, e);
            throw new JsonResponseException(e.getMessage());
        }
    }
    /**
     * 当前不能跨订单申请退款, 但是支持同一订单的多个子订单申请退款
     *
     * @param req 请求参数
     * @return 创建的退款单id
     */
    @MyLog(title = "订单管理", value = "申请退款", opBusinessType = OpBusinessType.OTHER)
    @RequestMapping(value = "/api/buyer/order/v2/refund", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Result<Long> applyRefund(@RequestBody RefundDto req) {
        log.info("申请退款 请求参数 {}",JSONUtil.toJsonStr(req));
        if (CollectionUtils.isEmpty(req.getOrderIds())) {
            log.error("no orderIds specified when apply refund");
            throw new JsonResponseException("order.refund.fail");
        }
        if (req.getOrderType() == null) {
            req.setOrderType(2);
        }
        try {
            log.info("申请退款 请求参数 订单id {}", JSONUtil.toJsonStr(req.getOrderIds()));
            CommonUser buyer = UserUtil.requireLoginUser();
            List<OrderRelation> relationList = new ArrayList<>(req.getOrderIds().size());
            boolean isShop = false;
            long orderShopId = -1;
            boolean confirmed = false;

            for (Long orderId : req.getOrderIds()) {
                relationList.add(new SimpleOrderRelationImpl(orderId, req.getOrderType()));

                OrderBase order;
                if (OrderLevel.fromInt(req.getOrderType()) == OrderLevel.SHOP) {
                    order = orderStaticPartCache.getOrderStaticPartByOrderId(orderId).get();
                } else {
                    order = orderStaticPartCache.getOrderStaticPartBySubOrderId(orderId).get();
                }

                orderShopId = order.getShopId();
                if (Objects.equals(orderShopId, buyer.getShopId()) && !isShop) {
                    isShop = true;
                }
                if (OrderStatus.CONFIRMED.getValue() == order.getStatus()) {
                    confirmed = true;
                }
            }

            var refundType = confirmed ? Refund.RefundType.AFTER_SALE_RETURN : Refund.RefundType.ON_SALE_REFUND;
            if (isShop || judgeIfRefundAble(req.getOrderIds(), req.getOrderType())) {
                return Result.data(refundForBuyerApplication.apply(buyer, relationList, req.getBuyerNote(),
                        StringUtils.isBlank(req.getImagesJson()) ? Collections.emptyList() : Json.parseObject(req.getImagesJson(), new TypeReference<List<String>>() {
                        }),
                        refundType, RefundReasonType.from(req.getReasonType()), null).take());
            }
            log.error("{} reject refund by timeLimit orderIds[{}] orderType[{}] operator:{} order-shopId:{}", LogUtil.getClassMethodName(), JSON.toJSONString(req.getOrderIds()), req.getOrderType(), buyer.getShopId(), orderShopId);
            throw new JsonResponseException("order.refund.fail");
        } catch (Exception e) {
            log.error("failed to apply refund for order(ids={}, level={}), cause:",
                    req.getOrderIds(), req.getOrderType(), e);
            throw new JsonResponseException(e.getMessage());
        }
    }


    /**
     * 裁决订单是否允许退款
     * 约束:  主订单绑定支付单
     *
     * @param orderIds  订单Id列表 实际上只取第一个
     * @param orderType 订单类型
     */
    private Boolean judgeIfRefundAble(List<Long> orderIds, Integer orderType) {
        Long orderId;
        Long shopId;
        switch (orderType) {
            case 1 -> {
                //店铺订单
                orderId = orderIds.get(0);
                val shopOrderPart = orderStaticPartCache.getOrderStaticPartByOrderId(orderId);
                if (shopOrderPart.isEmpty()) {
                    log.error("{} fail to refund orderId[{}] type[{}]", LogUtil.getClassMethodName(), orderId, orderType);
                    return false;
                }
                shopId = shopOrderPart.get().getShopId();
            }
            case 2 -> {
                //sku订单
                val skuOrderPart = orderStaticPartCache.getOrderStaticPartBySubOrderId(orderIds.get(0));
                if (!skuOrderPart.isDefined()) {
                    log.error("{} fail to refund orderId[{}] type[{}]", LogUtil.getClassMethodName(), orderIds.get(0), orderType);
                    return false;
                }
                orderId = skuOrderPart.get().getId();
                shopId = skuOrderPart.get().getShopId();
            }
            default -> {
                log.error("{} unknown orderIds[{}] orderType:{}", LogUtil.getClassMethodName(), JSON.toJSONString(orderIds), orderType);
                return false;
            }
        }
        Optional<Payment> paymentOpt = Optional.ofNullable(paymentReadService.findByOrderIdAndOrderLevel(orderId, OrderLevel.SHOP).getResult()).map(Collection::stream)
                .orElse(Stream.empty()).filter(payment -> payment.getPaidAt() != null).findFirst();
        if (!paymentOpt.isPresent()) {
            boolean isImportedOrder = orderStaticPartCache.getOrderStaticPartByOrderId(orderId).map(OrderBase::getOutFrom).filter(OrderOutFrom.EXCEL_IMPORT.Code()::equals).isDefined();
            if (isImportedOrder) {
                return true;
            }
            log.error("{} fail to find payment for order[{}] orderLevel[{}]", LogUtil.getClassMethodName(), orderId, orderType);
            return false;
        }

        val shopExtra = Optional.ofNullable(shopCacheHolder.findShopById(shopId).getExtra()).orElse(new HashMap<>(0));
        if (CollectionUtils.isEmpty(shopExtra) || !shopExtra.containsKey(ShopExtra.AllowRefund.getCode())) {
            return true;
        }

        val allowRefundSet = shopExtra.getOrDefault(ShopExtra.AllowRefund.getCode(), Boolean.FALSE.toString().toLowerCase()).trim();
        if (Boolean.TRUE.toString().equalsIgnoreCase(allowRefundSet)) {
            return true;
        }
        if (Boolean.FALSE.toString().equalsIgnoreCase(allowRefundSet) || allowRefundSet.isEmpty()) {
            return false;
        }
        int refundAllowedMinute = Integer.parseInt(allowRefundSet);
        val now = new DateTime(new Date());
        return !paymentOpt.get().getPaidAt().before(now.minusMinutes(refundAllowedMinute).toDate());
    }


    public void batchAction(Long[] refundIds, @PathVariable("action") String action, @RequestParam(defaultValue = "拒绝退款") String reason) {
        batchAction(refundIds, action, null, reason);
    }

    @RequestMapping(value = "/api/seller/refund/{action}", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public APIResp<Boolean> batchAction(Long[] refundIds, @PathVariable("action") String action, @RequestBody(required = false) ReturnInfo returnInfo,
                                        @RequestParam(defaultValue = "拒绝退款") String reason) {
        try {
            List<Refund> refundList = new ArrayList<>();
            for (Long refundId : refundIds) {
                refundList.add(refundReadService.findById(refundId).getResult());
            }
            if (refundList.stream().anyMatch(Objects::isNull)) {
                throw new JsonResponseException(new Translate("数据错误,请确认后重试").toString());
            }
            if (!refundList.stream().map(Refund::getStatus).allMatch(refundList.get(0).getStatus()::equals)) {
                throw new JsonResponseException(new Translate("退款单状态不一致,请刷新重拾").toString());
            }
            Consumer<Long> actor;
            switch (action) {
                case "agree":
                    actor = (refundId) -> agreeAction(refundId, returnInfo);
                    break;
                case "reject":
                    actor = (refundId) -> rejectRefund(refundId, reason);
                    break;
                case "pre":
                    actor = this::preRefund;
                    break;
                default:
                    throw new JsonResponseException(404, new Translate("未知的操作").toString());
            }
            StringBuilder builder = new StringBuilder();
            for (Long refundId : refundIds) {
                try {
                    actor.accept(refundId);
                } catch (Exception ex) {
                    log.error("{} fail to {} refundId:{}", LogUtil.getClassMethodName(), action, refundId, ex);
                    builder.append(String.format("退款单[%s] 处理失败 原因[%s]", refundId, ex.getMessage()));
                    builder.append('\n');
                }
            }
            if (!builder.toString().isEmpty()) {
                throw new JsonResponseException(builder.toString());
            }

            return APIResp.ok(true);
        } catch (Exception exception) {
            log.error("Refunds.batchAction error, action={}, refundIds={}", action, JSON.toJSONString(refundIds), exception);
            return APIResp.error(exception.getMessage());
        }
    }

    /**
     * 退款单审核通过
     *
     * @param refundId 退款单id
     */
    @RequestMapping(value = "/api/seller/refund/{refundId}/agree", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public APIResp<Boolean> agreeAction(@PathVariable("refundId") Long refundId, @RequestBody(required = false) ReturnInfo returnInfo) {
        try {
            CommonUser seller = UserUtil.getCurrentUser();
            Refund refund = refundReadService.findById(refundId).getResult();
            if (!Objects.equals(seller.getShopId(), refund.getShopId())) {
                throw new RuntimeException(Translate.of("该退款单[Id => %s, ShopId => %s] 不属于你", refundId, refund.getShopId()));
            }
            return APIResp.ok(refundForSellerApplication.acceptRefund(refundId, returnInfo, seller).take());
        } catch (Exception e) {
            log.error("fail to agree refund by refund id={} by user(id={}), cause:{}",
                    refundId, UserUtil.getUserId(), e);
            return APIResp.error("同意退款失败");
        }
    }

    /**
     * 发货后退款情况下，退款单审核通过，强制退款
     * 仅作为退款流程的标记，需要提示商家手动去进行正式操作
     *
     * @param refundId
     * @param returnInfo
     */
    @RequestMapping(value = "/api/seller/refund/{refundId}/agree/compulsory", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public APIResp<Boolean> compulsoryAgreeAction(@PathVariable("refundId") Long refundId, @RequestBody(required = false) ReturnInfo returnInfo) {
        try {
            CommonUser seller = UserUtil.getCurrentUser();
            Refund refund = refundReadService.findById(refundId).getResult();
            //对各表的状态直接进行修改
            if (!Objects.equals(seller.getShopId(), refund.getShopId())) {
                throw new RuntimeException(Translate.of("该退款单[Id => %s, ShopId => %s] 不属于你", refundId, refund.getShopId()));
            }
            refundForSellerApplication.acceptRefund(refundId, returnInfo, seller, OrderEvent.FORCE_REFUND_AGREE);
            return APIResp.ok(refundWriteService.updateStatusForCompulsoryAgree(refundId).getResult());
        } catch (Exception e) {
            log.error("fail to agree refund by refund id={} by user(id={}), cause:{}",
                    refundId, UserUtil.getUserId(), e);
            return APIResp.error("同意退款失败");
        }
    }

    /**
     * 强制退款
     *
     * @param returnInfo 退款相关信息
     * @return 是否退款成功
     */
    @PostMapping("/api/seller/refund/agree/compulsory/v2")
    public Result<Boolean> compulsoryAgreeAction(@RequestBody ReturnInfo returnInfo) {
        log.info("强制退款 请求参数 {}",JSONUtil.toJsonStr(returnInfo));
        if (returnInfo.getRefundId() == null) {
            throw new ApiException("退款单id不能为空");
        }
        Long refundId = returnInfo.getRefundId();
        try {
            CommonUser seller = UserUtil.getCurrentUser();
            Refund refund = refundReadService.findById(refundId).getResult();
            //对各表的状态直接进行修改
            if (!Objects.equals(seller.getShopId(), refund.getShopId())) {
                throw new RuntimeException(Translate.of("该退款单[Id => %s, ShopId => %s] 不属于你", refundId, refund.getShopId()));
            }
            refundForSellerApplication.acceptRefund(refundId, returnInfo, seller, OrderEvent.FORCE_REFUND_AGREE);
            return Result.data(refundWriteService.updateStatusForCompulsoryAgree(refundId).getResult());
        } catch (Exception e) {
            log.error("fail to agree refund by refund id={} by user(id={})",
                    refundId, UserUtil.getUserId(), e);
            throw new ApiException("同意退款失败");
        }
    }

    /**
     * 退款单审核拒绝
     *
     * @param refundId 退款单id
     */
    @RequestMapping(value = "/api/seller/refund/{refundId}/reject", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void rejectRefund(@PathVariable("refundId") Long refundId, String reason) {
        try {
            CommonUser seller = UserUtil.getCurrentUser();
            Refund refund = refundReadService.findById(refundId).getResult();
            if (!Objects.equals(seller.getShopId(), refund.getShopId())) {
                throw new RuntimeException(Translate.of("该退款单[Id => %s, ShopId => %s] 不属于你", refundId, refund.getShopId()));
            }
            refundForSellerApplication.rejectRefund(refundId, seller, reason).take();
        } catch (Exception e) {
            log.error("fail to reject refund by refund id={}, cause:{}",
                    refundId, e);
            throw new JsonResponseException("order.refund.reject.fail");
        }
    }

    /**
     * 退款单审核拒绝
     *
     * @param refundId 退款单id
     */
    @PostMapping(value = "/api/seller/refund/{refundId}/reject/v2")
    public Result<Boolean> rejectRefundV2(@PathVariable("refundId") Long refundId,
                                          @RequestBody RefundReqDto req) {
        try {
            CommonUser seller = UserUtil.getCurrentUser();
            Refund refund = refundReadService.findById(refundId).getResult();
            if (!Objects.equals(seller.getShopId(), refund.getShopId())) {
                throw new RuntimeException(Translate.of("该退款单[Id => %s, ShopId => %s] 不属于你", refundId, refund.getShopId()));
            }
            refundForSellerApplication.rejectRefund(refundId, seller, req.getSellerNote()).take();
            return Result.data(true);
        } catch (Exception e) {
            log.error("fail to reject refund by refund id={}, cause:{}",
                    refundId, e);
            throw new ApiException("退款拒绝失败");
        }
    }

    /**
     * 取消退款
     *
     * @param refundId 退款单id
     */
    @RequestMapping(value = "/api/buyer/refund/{refundId}/cancel", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public void cancelRefund(@PathVariable("refundId") Long refundId) {
        try {
            CommonUser buyer = UserUtil.getCurrentUser();
            Refund refund = refundReadService.findById(refundId).getResult();
            if (!Objects.equals(buyer.getId(), refund.getBuyerId())) {
                throw new RuntimeException(Translate.of("该退款单[Id => %s] 不属于你", refundId));
            }
            refundForBuyerApplication.cancel(refundId);
        } catch (Exception e) {
            log.error("fail to cancel refund by refund id={}",
                    refundId, e);
            throw new JsonResponseException("order.refund.cancel.fail");
        }
    }

    /**
     * 退款预处理
     *
     * @param refundId 退款单id
     */
    @RequestMapping(value = "/api/seller/refund/{refundId}/pre", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public APIResp<Boolean> preRefund(@PathVariable("refundId") Long refundId) {
        try {
            log.info("退款预处理 请求参数 退款单id {}",refundId);
            CommonUser seller = UserUtil.getCurrentUser();
            Refund refund = refundReadService.findById(refundId).getResult();
            if (!Objects.equals(seller.getShopId(), refund.getShopId())) {
                throw new RuntimeException(Translate.of("该退款单[Id => %s, ShopId => %s] 不属于你", refundId, refund.getShopId()));
            }

            return APIResp.ok(refundForSellerApplication.refund(refundId, seller).take());
        } catch (Exception e) {
            log.error("fail to pre refund by refund id={}", refundId, e);
            return APIResp.error(String.format("退款预处理失败: %s", e.getMessage()));
        }
    }
    /**
     * 退款预处理
     *
     * @param refundId 退款单id
     */
    @RequestMapping(value = "/api/seller/refund/{refundId}/pre/v2", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Result<Boolean> preRefundV2(@PathVariable("refundId") Long refundId) {
        try {
            log.info("退款预处理 请求参数 退款单id {}",refundId);
            CommonUser seller = UserUtil.getCurrentUser();
            Refund refund = refundReadService.findById(refundId).getResult();
            if (!Objects.equals(seller.getShopId(), refund.getShopId())) {
                throw new RuntimeException(Translate.of("该退款单[Id => %s, ShopId => %s] 不属于你", refundId, refund.getShopId()));
            }

            return Result.data(refundForSellerApplication.refund(refundId, seller).take());
        } catch (Exception e) {
            log.error("fail to pre refund by refund id={}", refundId, e);
            throw new ApiException(String.format("退款预处理失败: %s", e.getMessage()));
        }
    }
}