package moonstone.web.front;

import lombok.AllArgsConstructor;
import moonstone.order.service.ShopDetailsfwbReadService;
import moonstone.user.model.ShopDetailsfwb;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.Writer;

@Controller
@AllArgsConstructor
public class H5Generator {
    ShopDetailsfwbReadService shopDetailsfwbReadService;

    @RequestMapping(value = "/api/h5/build.html")
    public void request(HttpServletResponse response, Long shopId, Integer status) throws Exception {
        response.setHeader("content-type", "text/html");
        response.setCharacterEncoding("utf-8");
        Writer writer = response.getWriter();
        writer.write(
                shopDetailsfwbReadService.findShopDetailsfwbByShop(shopId, status).getResult()
                        .map(ShopDetailsfwb::getFwb).orElse(""));
    }
}
