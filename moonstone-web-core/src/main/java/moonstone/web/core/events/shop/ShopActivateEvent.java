/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.web.core.events.shop;

import lombok.Getter;

import java.io.Serializable;

/**
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-01-31
 */
public class ShopActivateEvent implements Serializable {
    private static final long serialVersionUID = 3052183949414022083L;

    @Getter
    private final Long shopId;

    @Getter
    private final Long operatorId;

    public ShopActivateEvent(Long shopId, Long operatorId) {
        this.shopId = shopId;
        this.operatorId = operatorId;
    }
}
