package moonstone.web.core.events.trade.app;

import com.alibaba.fastjson.JSON;
import io.terminus.common.exception.JsonResponseException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.cache.WeShopCacheHolder;
import moonstone.common.enums.OrderOutFrom;
import moonstone.common.model.Either;
import moonstone.common.model.IsPersistAble;
import moonstone.common.utils.LogUtil;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.dto.BalanceDetailCriteria;
import moonstone.order.dto.InComeDetail;
import moonstone.order.dto.OutComeDetail;
import moonstone.order.enu.ProfitType;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.OrderRefund;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.BalanceDetailReadService;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.shop.model.SubStore;
import moonstone.shop.service.SubStoreReadService;
import moonstone.user.model.StoreProxy;
import moonstone.user.service.StoreProxyReadService;
import moonstone.weShop.model.WeShop;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 子订单利润退款
 * fixme: 修改sku订单退款记录以及门店订单退款导致的用尽问题,SkuOrder退款是否牵连ShopOrder退款?
 */
@Component
@Slf4j
@AllArgsConstructor
@Deprecated
public class SkuOrderProfitReturnApp {
    SkuOrderReadService skuOrderReadService;
    ShopOrderReadService shopOrderReadService;
    WeShopCacheHolder weShopCacheHolder;
    SubStoreReadService subStoreReadService;
    BalanceDetailManager balanceDetailManager;
    BalanceDetailReadService balanceDetailReadService;
    StoreProxyReadService storeProxyReadService;



    @Deprecated
    public void refundSkuOrderProfit(OrderRefund orderRefund) {
        /// fixme: sku退款有问题 是否牵连整个shopOrder退款?
        /// 默认为SkuOrder
        log.warn("{} unsupported profit-refund for skuOrder[{}] refund[{}]", LogUtil.getClassMethodName(), orderRefund.getOrderId(), orderRefund.getRefundId());
        SkuOrder skuOrder =
                (Optional.ofNullable(skuOrderReadService.findById(orderRefund.getOrderId()).getResult()).orElseThrow(() -> new RuntimeException("[ProfitMakerWithComplexListener](refund) fail to find skuOrder by " + orderRefund.getOrderId())));
        ShopOrder shopOrder = Optional.ofNullable(shopOrderReadService.findById(skuOrder.getOrderId()).getResult()).orElseThrow(() -> new RuntimeException("[ProfitMakerWithComplexListener](refund) fail to find shopOrder by " + skuOrder.getOrderId()));
        OrderOutFrom outFrom = OrderOutFrom.fromCode(shopOrder.getOutFrom());
        Optional<Long> profitOwnerUserId = Optional.empty();
        switch (outFrom) {
            case LEVEL_Distribution: {
                profitOwnerUserId = Optional.ofNullable(shopOrder.getReferenceId());
                break;
            }
            case SUB_STORE: {
                SubStore subStore = Optional.ofNullable(subStoreReadService.findById(Long.parseLong(shopOrder.getOutShopId())).getResult()).orElseThrow(() -> new RuntimeException("[ProfitMakerWithComplexListener](refund) fail to find subStore by " + shopOrder.getOutShopId()));
                profitOwnerUserId = Optional.ofNullable(subStore.getUserId());
                break;
            }
            case WE_SHOP: {
                Long weShopId = Long.parseLong(shopOrder.getOutShopId());
                profitOwnerUserId = weShopCacheHolder.findByWeShopId(weShopId).map(WeShop::getUserId);
                break;
            }
            default:
        }
        profitOwnerUserId.ifPresent(userId -> refundProfit(orderRefund, skuOrder, outFrom, shopOrder.getReferenceId(), userId));
    }

    @Deprecated
    /// 回溯相关的利润记录进行添加退款扣除利润
    private void revokeSkuOrderProfit(Long refundId, Long profitOwnerUserId, Long
            relatedProfitOwnerUserId, SkuOrder skuOrder) {
        log.info("[ProfitMakerWithComplexListener](refund) working on SkuOrder:{},refundId:{},profitOwnerUserId:{},referenceId:{}", skuOrder.getId(), refundId, profitOwnerUserId, relatedProfitOwnerUserId);
        BalanceDetailCriteria criteria = new BalanceDetailCriteria();
        criteria.setUserId(profitOwnerUserId);
        criteria.setRelatedId(skuOrder.getId());
        criteria.setType(ProfitType.InCome.getValue());
        criteria.setNotStatusBitMarks(Arrays.asList(BalanceDetail.orderRelatedMask.ShopOrder.getValue()
                , BalanceDetail.maskBit.RefundRelated.getValue()
                , BalanceDetail.maskBit.WithDrawRelated.getValue()));
        criteria.setStatusBitMarks(Arrays.asList(BalanceDetail.maskBit.OrderRelated.getValue(), BalanceDetail.orderRelatedMask.SkuOrder.getValue(), IsPersistAble.maskBit.PersistAble.getValue()));
        List<InComeDetail> inComeDetails = Optional.ofNullable(balanceDetailReadService.paging(criteria.toMap()).getResult()).orElseThrow(() -> new RuntimeException("[ProfitMakerWithComplexListener](refund) fail to query skuOrder inComeDetail")).getData()
                .stream()
                .map(InComeDetail::new)
                .collect(Collectors.toList());
        /// 理论上应该只有一个 所以也只取第一个
        if (inComeDetails.size() != 1) {
            log.info("[ProfitMakerWithComplexListener](refund) puzzle about this,should send an email,{}", JSON.toJSONString(inComeDetails));
        }

        OutComeDetail outComeDetail = IncomeReverse.reverseInCome(inComeDetails.get(0));
        /// 无意义 因为值是查询而来 并且复制的
        outComeDetail.setStatus(outComeDetail.getStatus()
                & (~BalanceDetail.maskBit.OrderRelated.getValue())
                & (~BalanceDetail.orderRelatedMask.ShopOrder.getValue())
                | OutComeDetail.maskBit.RefundRelated.getValue()
                | IsPersistAble.maskBit.PersistAble.getValue());
        outComeDetail.setRelatedId(refundId);
        balanceDetailManager.changeRealProfit(outComeDetail).take();
        /// 以下可重构
        if (relatedProfitOwnerUserId != null) {
            criteria.setUserId(relatedProfitOwnerUserId);
            inComeDetails = Optional.ofNullable(balanceDetailReadService.paging(criteria.toMap()).getResult()).orElseThrow(() -> new RuntimeException("[ProfitMakerWithComplexListener](refund) fail to query skuOrder inComeDetail")).getData()
                    .stream()
                    .map(InComeDetail::new)
                    .collect(Collectors.toList());
            /// 理论上应该只有一个 所以也只取第一个
            if (inComeDetails.size() != 1) {
                log.info("[ProfitMakerWithComplexListener](refund) puzzle about this,should send an email,{}", JSON.toJSONString(inComeDetails));
            }
            /// 无意义 因为值是查询而来 并且复制的// outComeDetail.setUserId(referenceId);
            outComeDetail = IncomeReverse.reverseInCome(inComeDetails.get(0));
            outComeDetail.setStatus(outComeDetail.getStatus()
                    & (~BalanceDetail.maskBit.OrderRelated.getValue())
                    & (~BalanceDetail.orderRelatedMask.ShopOrder.getValue())
                    | OutComeDetail.maskBit.RefundRelated.getValue()
                    | IsPersistAble.maskBit.PersistAble.getValue());
            outComeDetail.setRelatedId(refundId);
            balanceDetailManager.changeRealProfit(outComeDetail).take();
        }
    }

    /**
     * 回退订单涉及到的利润
     */
    @Deprecated
    private void refundProfit(OrderRefund orderRefund, SkuOrder skuOrder, OrderOutFrom outFrom, Long referenceId, Long profitOwnerUserId) {
        log.debug("{} skuOrderId:{} orderId:{} outFrom:{} profitOwnerId:{}", LogUtil.getClassMethodName(), skuOrder.getId(), skuOrder.getOrderId(), outFrom.Code(), profitOwnerUserId);
        switch (outFrom) {
            case LEVEL_Distribution: {
                ShopOrder majorOrder = Optional.ofNullable(shopOrderReadService.findById(skuOrder.getOrderId()).getResult()).orElseThrow(() -> new JsonResponseException("shopOrder.not.find"));
                Long relatedProfitUserId = getRelatedProfitUserId(majorOrder, outFrom).orElse(null);
                revokeSkuOrderProfit(orderRefund.getRefundId(), profitOwnerUserId, relatedProfitUserId, skuOrder);
                return;
            }
            case SUB_STORE: {
                revokeSkuOrderProfit(orderRefund.getRefundId(), profitOwnerUserId, referenceId, skuOrder);
                return;
            }
            case WE_SHOP: {
                revokeSkuOrderProfit(orderRefund.getRefundId(), profitOwnerUserId, profitOwnerUserId, skuOrder);
                return;
            }
            default: {
                log.info("{} skuOrderId:{} orderId:{} outFrom:{}", LogUtil.getClassMethodName("not-support-refund"), skuOrder.getId(), skuOrder.getOrderId(), outFrom.Code());
            }
        }
    }



    /**
     * 获取附属的利润所有人的userId
     */
    private Optional<Long> getRelatedProfitUserId(ShopOrder shopOrder, OrderOutFrom outFrom) {
        switch (outFrom) {
            case LEVEL_Distribution: {
                long shopId = shopOrder.getShopId();
                val rFindSelf = storeProxyReadService.findByShopIdAndUserId(shopId, shopOrder.getRefererId());
                return rFindSelf.flatMap(self ->
                        self.map(entity ->
                                storeProxyReadService.findByShopIdAndUserId(shopId, entity.getUserId()))
                                .orElse(Either.error("error.gain.supperProxy")))
                        .orElse(Optional.empty())
                        .map(StoreProxy::getUserId);
            }
            case SUB_STORE:
                return Optional.ofNullable(shopOrder.getReferenceId());
            default:
                return Optional.empty();
        }
    }

}
