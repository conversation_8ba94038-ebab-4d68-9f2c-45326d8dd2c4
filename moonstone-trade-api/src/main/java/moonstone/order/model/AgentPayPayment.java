package moonstone.order.model;

import lombok.Data;
import moonstone.common.model.BaseEntity;

import java.util.Date;

/**
 * 代付订单的支付单
 */
@Data
public class AgentPayPayment extends BaseEntity {

    private Long id;

    /**
     * 商家平台ID
     */
    private Long shopId;

    /**
     * 代付订单id
     */
    private Long agentPayOrderId;

    /**
     * 支付单号
     */
    private String orderNo;

    /**
     * 支付平台返回的交易单号
     */
    private String tradeNo;

    /**
     * 支付状态(0-未支付，1-支付中，2-支付成功，3-支付失败)
     *
     * @see moonstone.order.enu.AgentPayPaymentStatusEnum
     */
    private Integer status;

    /**
     * 支付渠道
     *
     * @see moonstone.order.enu.PaymentChannelEnum
     */
    private String payChannel;

    /**
     * 支付完成时间
     */
    private Date paidAt;

    /**
     * 支付金额(单位：分)
     */
    private Long amount;

    /**
     * 支付的请求报文
     */
    private String payRequest;

    /**
     * 支付的响应报文
     */
    private String payResponse;

    /**
     * 支付回调报文
     */
    private String payCallback;
}
