/*
 * Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
 */

package moonstone.promotion.component;

import lombok.extern.slf4j.Slf4j;
import moonstone.cache.PromotionCacher;
import moonstone.common.utils.Translate;
import moonstone.order.dto.RichOrder;
import moonstone.order.dto.RichSku;
import moonstone.order.dto.RichSkusByShop;
import moonstone.promotion.exception.InvalidPromotionException;
import moonstone.promotion.model.Promotion;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 商家营销活动有效性检查, 一般在创建订单的时候检查
 * <p>
 * Author:  <a href="mailto:<EMAIL>">jlchen</a>
 * Date: 2016-06-15
 */
@Component
@Slf4j
public class PromotionOngoingValidator {

    private final PromotionCacher promotionCacher;

    @Autowired
    public PromotionOngoingValidator(PromotionCacher promotionCacher) {
        this.promotionCacher = promotionCacher;
    }

    /**
     * 检查买家提交的营销活动信息的有效性(即检查营销活动是否尚在进行中)
     *
     * @param richOrder 买家提交的订单相关信息
     */
    public void validate(RichOrder richOrder) {

        //检查支付级别的营销活动是否正在进行中
        if (richOrder.getPromotionId() != null) {
            doValidate(richOrder.getPromotionId());
        }

        for (RichSkusByShop richSkusByShop : richOrder.getRichSkusByShops()) {
            //检查店铺级别的营销活动是否正在进行中
            if (richSkusByShop.getPromotionId() != null) {
                doValidate(richSkusByShop.getPromotionId());
            }
            //检查运费营销活动是否正在进行中
            if (richSkusByShop.getShipmentPromotionId() != null) {
                doValidate(richSkusByShop.getShipmentPromotionId());
            }

            //检查sku级别的营销活动是否正在进行中
            for (RichSku richSku : richSkusByShop.getRichSkus()) {
                if (richSku.getPromotionId() != null) {
                    doValidate(richSku.getPromotionId());
                }
            }
        }

    }

    private void doValidate(Long promotionId) {
        Promotion promotion = promotionCacher.findByPromotionId(promotionId);
        if (!promotion.inProcess()) {
            log.error("promotion(id={}) is NOT in process", promotionId);
            throw new InvalidPromotionException(promotion, Translate.of("优惠活动已失效"));
        }
    }
}
