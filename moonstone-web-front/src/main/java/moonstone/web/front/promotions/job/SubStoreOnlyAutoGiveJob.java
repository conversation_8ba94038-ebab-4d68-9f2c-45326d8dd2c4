package moonstone.web.front.promotions.job;

import com.alibaba.fastjson.JSON;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.constants.SalePattern;
import moonstone.common.constants.ShopExtra;
import moonstone.common.utils.EventSender;
import moonstone.event.OrderConfirmCompletedEvent;
import moonstone.order.dto.OrderCriteria;
import moonstone.order.dto.fsm.OrderStatus;
import moonstone.order.model.OrderLevel;
import moonstone.order.model.ShopOrder;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.ShopOrderReadService;
import moonstone.order.service.SkuOrderReadService;
import moonstone.promotion.component.SubStoreOnlyManager;
import moonstone.promotion.enums.PromotionType;
import moonstone.promotion.model.Promotion;
import moonstone.promotion.model.UserPromotion;
import moonstone.promotion.service.PromotionReadService;
import moonstone.promotion.service.PromotionWriteService;
import moonstone.promotion.service.UserPromotionReadService;
import moonstone.shop.service.ShopReadService;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.web.core.events.promotion.PromotionPublishEvent;
import moonstone.web.core.events.promotion.PromotionTrackChangeEvent;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import moonstone.web.core.shop.model.ServiceProvider;
import org.redisson.api.RedissonClient;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;

/**
 * 门店限定卷，<br/>
 * 门店角色在 活动时间内 下单购买 指定的活动商品（多选）总数满足 指定件数 后获得门店限定券
 */
@Slf4j
@Component
public class SubStoreOnlyAutoGiveJob {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ShopReadService shopReadService;

    @Resource
    private PromotionReadService promotionReadService;

    @Resource
    private SubStoreOnlyManager subStoreOnlyManager;

    @Resource
    private ShopOrderReadService shopOrderReadService;

    @Resource
    private ServiceProviderCache serviceProviderCache;

    @Resource
    private UserRelationEntityReadService userRelationEntityReadService;

    @Resource
    private SkuOrderReadService skuOrderReadService;

    @Resource
    private UserPromotionReadService userPromotionReadService;

    @Resource
    private PromotionWriteService promotionWriteService;

    @Resource
    private SubStoreCache subStoreCache;

    @Resource
    private GuiderCache guiderCache;

    private final String LOCK_KEY = getClass().getName() + ":issueSubStoreOnly:" + "promotionId[%s]" + ":" + "userId[%s]";

    // @Scheduled(cron = "${scheduled.cron.subStoreOnlyAutoGive}")
    public void autoGive() {
        Lock iLock = redissonClient.getLock(getClass().getName() + "#autoGive");
        if (!iLock.tryLock()) {
            return;
        }

        log.info("SubStoreOnlyAutoGiveJob.autoGive begin.");
        try {
            for (Long shopId : shopReadService.allShopId().getResult()) {
                if (!salePatternSupported(shopId)) {
                    continue;
                }

                // 当前进行中的卷
                var list = promotionReadService.findOngoingByShopId(shopId, PromotionType.SUB_STORE_ONLY).getResult();
                if (CollectionUtils.isEmpty(list)) {
                    continue;
                }

                // 逐一发放
                for (var promotion : list) {
                    try {
                        issueSubStoreOnly(promotion);
                    } catch (Exception ex) {
                        log.error("SubStoreOnlyAutoGiveJob.autoGive error, promotion={} ", JSON.toJSONString(promotion), ex);
                    }
                }
            }
        } finally {
            iLock.unlock();
            log.info("SubStoreOnlyAutoGiveJob.autoGive end.");
        }
    }

    /**
     * 防止商家延迟到使用期才点击发布
     *
     * @param event
     */
    @EventListener(PromotionPublishEvent.class)
    public void onPublish(PromotionPublishEvent event) {
        log.debug("SubStoreOnlyAutoGiveJob.onPublish receive promotionId={}", event.getPromotionId());
        issueSubStoreOnly(promotionReadService.findById(event.getPromotionId()).getResult());
    }

    /**
     * 订单确认收货时，进行发卷尝试
     *
     * @param event
     */
    @EventListener(OrderConfirmCompletedEvent.class)
    public void onOrderConfirm(OrderConfirmCompletedEvent event) {
        log.debug("SubStoreOnlyAutoGiveJob.onOrderConfirm receive OrderId={}, orderType={}", event.getOrderId(), event.getOrderType());
        if (event.getOrderId() == null || event.getOrderType() == null || OrderLevel.SHOP.getValue() != event.getOrderType()) {
            return;
        }

        var shopOrder = shopOrderReadService.findById(event.getOrderId()).getResult();
        if (shopOrder == null) {
            log.error("SubStoreOnlyAutoGiveJob.onOrderConfirm, shopOrderId={} has no data found.", event.getOrderId());
            return;
        }

        // 当前进行中的卷
        var list = promotionReadService.findOngoingByShopId(shopOrder.getShopId(), PromotionType.SUB_STORE_ONLY).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        for (var promotion : list) {
            try {
                issueSubStoreOnly(promotion);
            } catch (Exception ex) {
                log.error("SubStoreOnlyAutoGiveJob.onOrderConfirm, shopOrderId={}, promotionId={} issue failed.",
                        event.getOrderId(), promotion.getId(), ex);
            }
        }
    }

    /**
     * 门店限定卷发放
     *
     * @param promotion
     */
    private void issueSubStoreOnly(Promotion promotion) {
        if (promotion == null || PromotionType.SUB_STORE_ONLY.getValue() != promotion.getType() || !promotion.inProcess()) {
            return;
        }

        // 该卷所允许的门店用户id
        var validSubStoreUserIds = getValidSubStoreUserIds(promotion);

        // key = userId, value = 该用户购买活动商品的数量
        Map<Long, Integer> userActivityItemMap = new HashMap<>();

        // 分页查询符合条件的订单
        OrderCriteria orderCriteria = buildOrderCriteria(promotion);
        for (int pageNo = 1; ; pageNo++) {
            orderCriteria.setPageNo(pageNo);
            var shopOrders = shopOrderReadService.listShopOrdersBy(orderCriteria).getResult();
            if (CollectionUtils.isEmpty(shopOrders)) {
                break;
            }

            // 统计累加活动商品数量
            sumUpActivityItemNum(userActivityItemMap, shopOrders, promotion, validSubStoreUserIds);
        }

        // 根据统计结果开始分发限定卷
        doIssue(userActivityItemMap, promotion);
    }

    /**
     * 统计门店角色用户的累计活动商品数量
     *
     * @param userActivityItemMap key = 用户id, value = 活动商品的购买数
     * @param shopOrders
     */
    private void sumUpActivityItemNum(Map<Long, Integer> userActivityItemMap, List<ShopOrder> shopOrders, Promotion promotion,
                                      List<Long> validSubStoreUserIds) {
        // 该卷所允许的门店用户id
        if (CollectionUtils.isEmpty(validSubStoreUserIds)) {
            return;
        }

        // 该卷指定的活动商品id
        var activityItemIds = subStoreOnlyManager.getActivityItemId(promotion);
        if (CollectionUtils.isEmpty(activityItemIds)) {
            return;
        }

        // 子订单信息, key = shopOrderId
        var skuOrderMap = skuOrderReadService.getSkuOrderMap(shopOrders.stream().map(ShopOrder::getId).toList());

        for (var shopOrder : shopOrders) {
            Long targetUserId = getTargetUserId(shopOrder, validSubStoreUserIds);
            if (targetUserId == null) {
                continue;
            }

            var skuOrders = skuOrderMap.get(shopOrder.getId());
            int validActivityNum = countValidActivityNum(activityItemIds, skuOrders);
            log.debug("SubStoreOnlyAutoGiveJob.sumUpActivityItemNum, targetUserId={}, shopOrderId={}, promotionId={}, validActivityNum={}",
                    targetUserId, shopOrder.getId(), promotion.getId(), validActivityNum);
            if (validActivityNum <= 0) {
                continue;
            }

            userActivityItemMap.putIfAbsent(targetUserId, 0);
            userActivityItemMap.put(targetUserId, userActivityItemMap.get(targetUserId) + validActivityNum);
        }
        log.debug("SubStoreOnlyAutoGiveJob.sumUpActivityItemNum, promotionId={}, 当前 userActivityItemMap={}",
                promotion.getId(), JSON.toJSONString(userActivityItemMap));
    }

    private Long getTargetUserId(ShopOrder shopOrder, List<Long> validSubStoreUserIds) {
        if (shopOrder == null || CollectionUtils.isEmpty(validSubStoreUserIds)) {
            return null;
        }

        // 买家为门店本人
        if (validSubStoreUserIds.contains(shopOrder.getBuyerId())) {
            return shopOrder.getBuyerId();
        }

        // 买家为门店所属导购
        var guider = guiderCache.findByShopIdAndUserId(shopOrder.getShopId(), shopOrder.getBuyerId());
        if (guider.isEmpty()) {
            return null;
        }
        var subStore = subStoreCache.findById(guider.get().getSubStoreId());
        if (subStore.isPresent() && validSubStoreUserIds.contains(subStore.get().getUserId())) {
            return subStore.get().getUserId();
        }

        return null;
    }

    private int countValidActivityNum(List<Long> activityItemIds, List<SkuOrder> skuOrders) {
        if (CollectionUtils.isEmpty(skuOrders)) {
            return 0;
        }

        return skuOrders.stream()
                .filter(skuOrder -> activityItemIds.contains(skuOrder.getItemId()))
                .mapToInt(SkuOrder::getQuantity)
                .sum();
    }

    /**
     * 根据 promotion 上配置的服务商，查询其对应的下属门店用户的用户id
     *
     * @param promotion
     * @return
     */
    private List<Long> getValidSubStoreUserIds(Promotion promotion) {
        var ids = subStoreOnlyManager.getServiceProviderId(promotion);
        if (CollectionUtils.isEmpty(ids)) {
            throw new RuntimeException("服务商id配置为空");
        }

        var serviceProviders = serviceProviderCache.findByIds(ids);
        if (CollectionUtils.isEmpty(serviceProviders)) {
            throw new RuntimeException("服务商数据查询为空");
        }

        var list = userRelationEntityReadService.find(promotion.getShopId(), UserRelationEntity.UserRelationType.SUPER,
                serviceProviders.stream().map(ServiceProvider::getUserId).toList()).getResult();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream()
                .map(UserRelationEntity::getUserId)
                .toList();
    }

    /**
     * 发放限定卷
     *
     * @param userActivityItemMap key = 用户id, value = 活动商品的购买数
     * @param promotion
     */
    private void doIssue(Map<Long, Integer> userActivityItemMap, Promotion promotion) {
        if (CollectionUtils.isEmpty(userActivityItemMap)) {
            return;
        }

        // 统计并构造
        var list = build(userActivityItemMap, promotion);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 逐一发放
        int totalReceive = list.stream().mapToInt(this::doIssue).sum();

        // 触发事件更新对应的追踪对象
        EventSender.sendApplicationEvent(new PromotionTrackChangeEvent(promotion.getId(), totalReceive,
                PromotionTrackChangeEvent.ChangeType.RECEIVED_QUANTITY));
    }

    @SneakyThrows
    private int doIssue(UserPromotion userPromotion) {
        Lock iLock = redissonClient.getLock(
                LOCK_KEY.formatted(userPromotion.getPromotionId(), userPromotion.getUserId()));
        if (!iLock.tryLock(10, TimeUnit.MINUTES)) {
            throw new RuntimeException("获取锁失败");
        }

        try {
            return promotionWriteService.doIssue(userPromotion);
        } catch (Exception ex) {
            log.error("SubStoreOnlyAutoGiveJob.doIssue error, userPromotion={}", JSON.toJSONString(userPromotion), ex);
        } finally {
            iLock.unlock();
        }

        return 0;
    }

    private List<UserPromotion> build(Map<Long, Integer> userActivityItemMap, Promotion promotion) {
        // 限定卷的配置
        Integer conditionNum = subStoreOnlyManager.getActivityConditionItemNum(promotion);
        if (conditionNum == null) {
            throw new RuntimeException("指定数量为空");
        }
        Boolean accumulate = subStoreOnlyManager.getActivityItemNumAccumulate(promotion);
        if (accumulate == null) {
            throw new RuntimeException("活动商品数量累加配置为空");
        }

        // 计算可用数量并构造对象
        var list = new ArrayList<UserPromotion>();
        for (var entry : userActivityItemMap.entrySet()) {
            int quantity = calculateQuantity(entry.getValue(), conditionNum, accumulate);
            if (quantity <= 0) {
                continue;
            }

            list.add(build(entry.getKey(), quantity, promotion));
        }

        return list;
    }

    private Integer calculateQuantity(Integer boughtNum, Integer conditionNum, boolean accumulate) {
        int quantity = boughtNum / conditionNum;
        if (!accumulate) {
            if (quantity > 1) {
                quantity = 1;
            }
        }

        return quantity;
    }

    private UserPromotion build(Long userId, Integer quantity, Promotion promotion) {
        UserPromotion userPromotion = new UserPromotion();

        userPromotion.setUserId(userId);
        userPromotion.setShopId(promotion.getShopId());
        userPromotion.setPromotionId(promotion.getId());
        userPromotion.setType(promotion.getType());

        userPromotion.setName(promotion.getName());
        userPromotion.setAvailableQuantity(quantity);
        userPromotion.setFrozenQuantity(0);
        userPromotion.setStatus(promotion.getStatus());

        userPromotion.setStartAt(promotion.getStartAt());
        userPromotion.setEndAt(promotion.getEndAt());
        userPromotion.setReceiveQuantity(quantity);

        return userPromotion;
    }

    private OrderCriteria buildOrderCriteria(Promotion promotion) {
        OrderCriteria criteria = new OrderCriteria();

        criteria.setSize(100);
        criteria.setShopId(promotion.getShopId());
        criteria.setStatus(List.of(OrderStatus.CONFIRMED.getValue()));
        criteria.setStartAt(subStoreOnlyManager.getActivityStartTime(promotion));
        criteria.setEndAt(subStoreOnlyManager.getActivityEndTime(promotion));

        return criteria;
    }

    /**
     * 当下只有 subStore 模式支持 门店限定卷 的业务
     *
     * @param shopId
     * @return
     */
    private boolean salePatternSupported(Long shopId) {
        var shop = shopReadService.findById(shopId).getResult();
        if (shop == null || CollectionUtils.isEmpty(shop.getExtra()) ||
                !shop.getExtra().containsKey(ShopExtra.SalesPattern.getCode())) {
            return false;
        }

        return SalePattern.SubStore.getCode().equals(shop.getExtra().get(ShopExtra.SalesPattern.getCode()));
    }
}
