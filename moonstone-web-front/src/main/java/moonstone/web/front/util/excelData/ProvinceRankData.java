package moonstone.web.front.util.excelData;


import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.enums.poi.HorizontalAlignmentEnum;
import lombok.Data;

/**
 * easyExcel的SKU排行数据模型
 */

@Data
@ContentStyle(horizontalAlignment = HorizontalAlignmentEnum.CENTER)
public class ProvinceRankData {

    /**
     * 名次
     */
    @ExcelProperty("排名")
    private Integer rank;

    /**
     * 省份
     */
    @ColumnWidth(30)
    @ExcelProperty("省份")
    private String province;

    /**
     * 省份GMV
     */
    @NumberFormat("#.00")
    @ExcelProperty("GMV")
    private double gmv;

    /**
     * 省份单量
     */
    @ExcelProperty("单量")
    private Long orderQuantity;

}
