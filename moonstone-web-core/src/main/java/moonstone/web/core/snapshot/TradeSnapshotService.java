package moonstone.web.core.snapshot;

import io.terminus.boot.rpc.common.annotation.RpcConsumer;
import io.terminus.common.exception.ServiceException;
import io.terminus.common.model.Response;
import io.terminus.common.utils.BeanMapper;
import lombok.extern.slf4j.Slf4j;
import moonstone.attribute.dto.SkuAttribute;
import moonstone.item.model.ItemSnapshot;
import moonstone.item.service.ItemSnapshotReadService;
import moonstone.order.model.SkuOrder;
import moonstone.order.service.SkuOrderReadService;
import moonstone.web.core.snapshot.dto.OrderItemSnapshotDetail;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Author:cp
 * Created on 5/31/16.
 */
@Slf4j
@Component
public class TradeSnapshotService {

    @RpcConsumer
    private SkuOrderReadService skuOrderReadService;

    @RpcConsumer
    private ItemSnapshotReadService itemSnapshotReadService;

    private SkuOrder findSkuOrder(Long skuOrderId) {
        Response<SkuOrder> findSkuOrderResp = skuOrderReadService.findById(skuOrderId);
        if (!findSkuOrderResp.isSuccess()) {
            log.error("find to find sku order by id:{},cause:{}", skuOrderId, findSkuOrderResp.getError());
            throw new ServiceException(findSkuOrderResp.getError());
        }
        return findSkuOrderResp.getResult();
    }

    public Response<OrderItemSnapshotDetail> findOrderItemSnapshot(Long skuOrderId) {
        SkuOrder skuOrder = findSkuOrder(skuOrderId);
        if (skuOrder.getItemSnapshotId() == null) {
            log.warn("item snapshot id is null where sku order id={}", skuOrder.getId());
            return null;
        }
        Response<ItemSnapshot> snapshotResp = itemSnapshotReadService.findById(skuOrder.getItemSnapshotId());
        if (!snapshotResp.isSuccess()) {
            log.error("fail to find item snapshot by id:{},cause:{}", skuOrder.getItemSnapshotId(), snapshotResp.getError());
            throw new ServiceException(snapshotResp.getError());
        }

        ItemSnapshot itemSnapshot = snapshotResp.getResult();
        OrderItemSnapshotDetail orderItemSnapshotDetail = new OrderItemSnapshotDetail();
        BeanMapper.copy(itemSnapshot, orderItemSnapshotDetail);
        orderItemSnapshotDetail.setImages(itemSnapshot.getImages());
        Long skuOriginPrice = skuOrder.getOriginFee() / skuOrder.getQuantity() + Long.parseLong(Optional.ofNullable(skuOrder.getExtra()).orElseGet(HashMap::new).getOrDefault("enhancedFee", "0"));
        orderItemSnapshotDetail.setSkuOriginPrice(skuOriginPrice);
        orderItemSnapshotDetail.setSkuPrice(skuOriginPrice);
        orderItemSnapshotDetail.setAttributeMap(buildAttributes(skuOrder.getSkuAttrs()));
        return Response.ok(orderItemSnapshotDetail);
    }

    private Map<String, String> buildAttributes(List<SkuAttribute> skuAttributes) {
        if (CollectionUtils.isEmpty(skuAttributes)) {
            return Collections.EMPTY_MAP;
        }

        Map<String, String> attributes = new HashMap<>();
        for (SkuAttribute skuAttribute : skuAttributes) {
            attributes.put(skuAttribute.getAttrKey(), skuAttribute.getAttrVal());
        }
        return attributes;
    }
}
