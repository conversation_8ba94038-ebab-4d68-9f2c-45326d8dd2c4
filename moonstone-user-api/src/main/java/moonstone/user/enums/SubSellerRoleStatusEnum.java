package moonstone.user.enums;

public enum SubSellerRoleStatusEnum {
    FROZEN(0, "未生效(冻结)"),
    VALID(1, "生效"),
    DELETED(-1, "已删除"),
    ;

    private final Integer code;
    private final String description;

    SubSellerRoleStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
