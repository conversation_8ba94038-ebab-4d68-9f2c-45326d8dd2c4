<?xml version="1.0" encoding="UTF-8"?>
<properties>
    <application>
        <name>supply-shop-front</name>
    </application>
    <environment>dev</environment>
    <logging>
        <config>classpath:config/dev/logback.xml</config>
    </logging>
    <ext.messages.classpath>messages_zh_CN</ext.messages.classpath>
    <soul>
        <http>
            <adminUrl>http://soul-admin-dev.yang800.com.cn:8800</adminUrl>
            <contextPath>/shop</contextPath>
            <appName>shop</appName>
        </http>
        <dubbo>
            <adminUrl>http://soul-admin-dev.yang800.com.cn:8800</adminUrl>
            <contextPath>/shop</contextPath>
            <appName>shop</appName>
        </dubbo>
    </soul>
    <dubbo>
        <application>
            <name>parana-web</name>
        </application>
        <port>20880</port>
        <address>${nacos.discovery.serverAddr}</address>
        <registry>
            <protocol>nacos</protocol>
            <address>${nacos.discovery.serverAddr}</address>
        </registry>
    </dubbo>
    <nacos>
        <discovery>
            <serverAddr>***********:8848</serverAddr>
        </discovery>
    </nacos>
    <ucenter>
        <url>http://ucenter-server-dev.yang800.com.cn:8800/sdk-api/gateway</url>
        <systemCode>PARANA_WEB</systemCode>
        <appKey>PARANA_WEB_dev</appKey>
        <appSecret>dev</appSecret>
        <packages/>
    </ucenter>
    <mybatis>
        <mapperLocations>classpath*:mapper/*Mapper.xml</mapperLocations>
        <type-aliases-package>moonstone.(item|category|brand|shop|spu|user|cart|order|express|promotion|delivery|file|settle|membership|countryImage|integral).model, moonstone.(adv|thirdParty|shopWxa|wxa).model, moonstone.(weCart|weShop|weDistributionApplication).model, moonstone.user.address.model, moonstone.user.area.model, moonstone.pay.mock.model, moonstone.auth.model</type-aliases-package>
        <typeHandlersPackage>moonstone.common.type.handler</typeHandlersPackage>
    </mybatis>
    <server>
        <context-path>/</context-path>
        <port>10081</port>
    </server>
    <hazelcast>
        <config>hazelcast-config.xml</config>
    </hazelcast>
    <spring>
        <main>
            <allow-bean-definition-overriding>true</allow-bean-definition-overriding>
            <allow-circular-references>true</allow-circular-references>
        </main>
        <datasource>
            <driver-class-name>com.mysql.cj.jdbc.Driver</driver-class-name>
            <url><![CDATA[*******************************************************************************************************************************************************************************************]]></url>
            <username>dt_wms</username>
            <password>b80c157@c3791</password>
        </datasource>
        <data>
            <mongodb>
                <uri>mongodb://127.0.0.1:27017/parana-dev</uri>
            </mongodb>
        </data>
        <http>
            <converters>
                <preferred-json-mapper>gson</preferred-json-mapper>
            </converters>
            <multipart>
                <max-file-size>50Mb</max-file-size>
                <max-request-size>50Mb</max-request-size>
            </multipart>
        </http>
    </spring>
    <parana>
        <app_backend>
            <url>http://localhost:10081</url>
        </app_backend>
        <web>
            <url>http://webdev.parana.yang800.cn:10091</url>
        </web>
        <mall>
            <url>http://webdev.parana.yang800.cn:10091</url>
        </mall>
        <h5>
            <url>https://m.mall.yang800.com</url>
        </h5>
        <wxa>
            <url>https://m.mall.yang800.com</url>
            <appId>wx90e48a315747ccb9</appId>
            <appSecret>ebb83ced7885897e4fd5ecfc403ed92d</appSecret>
        </wxa>
        <we>
            <seller>
                <url>https://m.mall.yang800.com</url>
                <appId>wx0fb538f6dc093892</appId>
                <appSecret>5d260bcf67eecf19e96a341b7b4eb95a</appSecret>
            </seller>
            <buyer>
                <url>https://m.mall.yang800.com</url>
                <appId>wxb25691b47b974630</appId>
                <appSecret>088dedd8ec3382f5ba981a2560afc187</appSecret>
            </buyer>
        </we>
        <wxopen>
            <componentAppId>wxfb7d4a7ab73f6ba6</componentAppId>
            <componentSecret>a9a41dcfd8e7b564a2ec93c0ee58fb9f</componentSecret>
            <componentToken>wx569abcf3c77ff1de</componentToken>
            <componentAesKey>0987654321qazwsxedcrfvtgbyhnujmik1234567890</componentAesKey>
            <requestdomain>https://m.mall.yang800.com;https://www.yang800.com;https://m.yang800.com</requestdomain>
            <wsrequestdomain>wss://m.mall.yang800.com;wss://www.yang800.com</wsrequestdomain>
            <uploaddomain>https://m.mall.yang800.com;https://www.yang800.com</uploaddomain>
            <downloaddomain>https://m.mall.yang800.com;https://www.yang800.com</downloaddomain>
        </wxopen>
        <substore>
            <appId>wx2b917de6e30d8357</appId>
            <appSecret>b5abba8986b399d33c14f4e5ecc114a3</appSecret>
        </substore>
        <levelDistribution>
            <appId>wx2b917de6e30d8357</appId>
            <appSecret>b5abba8986b399d33c14f4e5ecc114a3</appSecret>
        </levelDistribution>
    </parana>
    <search>
        <host>search.localhost</host>
        <new_host>search.localhost</new_host>
        <port>9200</port>
    </search>
    <item.search>
        <index-name>t_items</index-name>
        <index-type>item</index-type>
        <mapping-path>item_mapping.json</mapping-path>
        <full-dump-range>3000</full-dump-range>
        <batch-size>100</batch-size>
    </item.search>
    <shop.search>
        <index-name>t_shops</index-name>
        <index-type>shop</index-type>
        <mapping-path>shop_mapping.json</mapping-path>
        <full-dump-range>3000</full-dump-range>
        <batch-size>100</batch-size>
    </shop.search>
    <weShopItem.search>
        <index-name>t_we_shop_items</index-name>
        <index-type>we_shop_item</index-type>
        <mapping-path>we_shop_item_mapping.json</mapping-path>
        <full-dump-range>3000</full-dump-range>
        <batch-size>100</batch-size>
    </weShopItem.search>
    <session>
        <cookie-domain>none</cookie-domain>
        <cookie-context-path>/</cookie-context-path>
        <cookie-name>msid</cookie-name>
        <cookie-max-age>180000</cookie-max-age>
        <source>redis</source>
        <serialize-type>json</serialize-type>
        <redis-host>redis.localhost</redis-host>
        <redis-port>6379</redis-port>
        <redis-index>0</redis-index>
        <redis-cluster>false</redis-cluster>
        <redis-test-on-borrow>true</redis-test-on-borrow>
        <redis-max-total>10</redis-max-total>
        <redis-max-idle>0</redis-max-idle>
        <redis-prefix>afsession</redis-prefix>
        <max-inactive-interval>18000</max-inactive-interval>
        <jwt_key>WEB_TEST_FCHcgv1NCx1NHhjLPdYeFrUu87C8RPYa</jwt_key>
        <jwt_name>test_app_token</jwt_name>
    </session>
    <express.100>
        <key>f93c3f9e5a89fb22</key>
        <customer>xxxx</customer>
        <regularUrl>xxxx</regularUrl>
    </express.100>
    <ip.dic>data/ip.txt</ip.dic>
    <wechat>
        <mp>
            <appId>wx86fe5657944bcaa2</appId>
            <secret>f72b211010c13e17141accd17fdbb23b</secret>
        </mp>
        <authorize>
            <redirectUrl>http://webdev.parana.yang800.cn:10081/api/wechat/auth/jump</redirectUrl>
            <resultUrl>http://webdev.parana.yang800.cn:10091/seller/authorize-success</resultUrl>
        </authorize>
        <applet>
            <enable>true</enable>
            <mchId>**********</mchId>
            <appId>wx86fe5657944bcaa2</appId>
            <secret>8ac1cbf290b0fe98ff1142acf94e7351</secret>
        </applet>
    </wechat>
    <pay>
        <easy-pay>
            <gateway>https://test_nucc.bhecard.com:9088/api_gateway.do</gateway>
            <publicKey>MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2WTfvas1JvvaRuJWIKmKlBLmkRvr2O7Fu3k/zvhJs+X1JQorPWq/yZduY6HKu0up7Qi3T6ULHWyKBS1nRqhhHpmLHnI3sIO8E/RzNXJiTd9/bpXMv+H8F8DW5ElLxCIVuwHBROkBLWS9fIpslkFPt+r13oKFnuWhXgRr+K/YkJQIDAQAB</publicKey>
        </easy-pay>
        <debug>false</debug>
        <channel>
            <alipay>enable</alipay>
        </channel>
        <notifyUrl>http://webdev.parana.yang800.cn:10081/api/order/paid</notifyUrl>
        <refundNotifyUrl>http://webdev.parana.yang800.cn:10081/api/refund/notify</refundNotifyUrl>
        <returnUrl>http://webdev.parana.yang800.cn:10091/buyer/trade-success</returnUrl>
        <h5ReturnUrl>https://m.mall.yang800.com/order/pay-success</h5ReturnUrl>
        <unionpay.wap.token.verifyCerDir>e:/nfs/dev/logs/terminus-web</unionpay.wap.token.verifyCerDir>
        <unionpay.pc.token.verifyCerDir>e:/nfs/dev/logs/terminus-web</unionpay.pc.token.verifyCerDir>
        <unionpay.app.token.verifyCerDir>e:/nfs/dev/logs/terminus-web</unionpay.app.token.verifyCerDir>
        <certFilePath>e:/nfs/dev/certFiles/terminus-web</certFilePath>
    </pay>
    <pay.mockpay.token>
        <notifyUrl>http://webdev.parana.yang800.cn:10081/api/order/paid</notifyUrl>
        <returnUrl>http://webdev.parana.yang800.cn:10091/buyer/trade-success</returnUrl>
        <refundNotifyUrl>http://webdev.parana.yang800.cn:10081/api/refund/notify</refundNotifyUrl>
    </pay.mockpay.token>
    <pay.integral.token>
        <returnUrl>http://webdev.parana.yang800.cn:10091/buyer/trade-success</returnUrl>
        <refundNotifyUrl>http://webdev.parana.yang800.cn:10081/api/refund/notify</refundNotifyUrl>
    </pay.integral.token>
    <wechat.applet>
        <enable>true</enable>
        <mchId>**********</mchId>
        <appId>wx86fe5657944bcaa2</appId>
        <secret>8ac1cbf290b0fe98ff1142acf94e7351</secret>
    </wechat.applet>
    <alipay>
        <pid>****************</pid>
        <key>pil5t34m4qsdor9ujuffkqfvrgfjt3mp</key>
        <account><EMAIL></account>
    </alipay>
    <cache.duration.in.minutes>1</cache.duration.in.minutes>
    <settle>
        <enable>true</enable>
        <listener>
            <enable>false</enable>
        </listener>
    </settle>
    <msg>
        <current>
            <smsService>aliYunSmsService</smsService>
            <emailService>javaxEmailService</emailService>
        </current>
        <aliyun>
            <appKey>LTAIMbTR37BKlN62</appKey>
            <appSecret>******************************</appSecret>
        </aliyun>
        <javaxemail>
            <mailServerHost>smtp.exmail.qq.com</mailServerHost>
            <mailServerPort>465</mailServerPort>
            <fromAddress>Y800<![CDATA[<<EMAIL>>]]></fromAddress>
            <userName><EMAIL></userName>
            <password>Dd111111</password>
        </javaxemail>
    </msg>
    <msg.email>
        <templates>
            <key>email.error.notice</key>
            <templateName>sendEmailErrorNotice</templateName>
            <subject>但丁商城 - 异常通知</subject>
        </templates>
    </msg.email>
    <enable.open.api>true</enable.open.api>
    <oss>
        <endpoint>https://oss-cn-hangzhou.aliyuncs.com</endpoint>
        <appKey>LTAI4GDcG22sccSsU4BCd3U9</appKey>
        <appSecret>******************************</appSecret>
        <bucketName>dante-img</bucketName>
    </oss>
    <image>
        <base.url>https://dante-img.oss-cn-hangzhou.aliyuncs.com</base.url>
        <upload.allowedFileTypes>jpeg,jpg,gif,png,mp4,webp</upload.allowedFileTypes>
        <protocol>http</protocol>
        <domain>dante-img.oss-cn-hangzhou.aliyuncs.com</domain>
    </image>
    <enable.shop.search>true</enable.shop.search>
    <enable.item.search>true</enable.item.search>
    <Y800>
        <partnerCode>websc</partnerCode>
        <partnerKey>websc</partnerKey>
        <sourcePlatform>WEBSC</sourcePlatform>
        <merchantCode/>
        <wd.support>http://192.168.199:6086</wd.support>
        <yang.support>http://127.0.0.1:4000</yang.support>
        <finance.project.url>http://127.0.0.1:8080/xhr</finance.project.url>
        <open.api.gate>http://localhost:4000/api/</open.api.gate>
    </Y800>
    <nio>
        <post.url>http://127.0.0.1:8091/xhr</post.url>
    </nio>
    <aoxin>
        <gateway>http://************:61/API</gateway>
    </aoxin>
    <pay.alipay.app.token>
        <notifyUrl>${pay.notifyUrl}</notifyUrl>
        <returnUrl>${pay.returnUrl}</returnUrl>
        <refundNotifyUrl>${pay.refundNotifyUrl}</refundNotifyUrl>
    </pay.alipay.app.token>
    <pay.alipay.app.account.list>
        <accountNo>default</accountNo>
        <accountName>default app account</accountName>
        <pid>${alipay.pid}</pid>
        <account>${alipay.account}</account>
        <md5Key>${alipay.key}</md5Key>
    </pay.alipay.app.account.list>
    <pay.job.trans.alipay.app.enable>true</pay.job.trans.alipay.app.enable>
    <pay.alipay.pc.token>
        <notifyUrl>${pay.notifyUrl}</notifyUrl>
        <returnUrl>${pay.returnUrl}</returnUrl>
        <refundNotifyUrl>${pay.refundNotifyUrl}</refundNotifyUrl>
    </pay.alipay.pc.token>
    <pay.alipay.pc.account.list>
        <accountNo>default</accountNo>
        <accountName>default app account</accountName>
        <pid>${alipay.pid}</pid>
        <account>${alipay.account}</account>
        <md5Key>${alipay.key}</md5Key>
    </pay.alipay.pc.account.list>
    <pay.job.trans.alipay.pc.enable>true</pay.job.trans.alipay.pc.enable>
    <pay.alipay.wap.token>
        <notifyUrl>${pay.notifyUrl}</notifyUrl>
        <returnUrl>${pay.h5ReturnUrl}</returnUrl>
        <refundNotifyUrl>${pay.refundNotifyUrl}</refundNotifyUrl>
    </pay.alipay.wap.token>
    <pay.alipay.wap.account.list>
        <accountNo>default</accountNo>
        <accountName>default app account</accountName>
        <pid>${alipay.pid}</pid>
        <account>${alipay.account}</account>
        <md5Key>${alipay.key}</md5Key>
    </pay.alipay.wap.account.list>
    <pay.job.trans.alipay.wap.enable>true</pay.job.trans.alipay.wap.enable>
    <pay.wechatpay.app.token>
        <notifyUrl>${pay.notifyUrl}</notifyUrl>
        <returnUrl>${pay.returnUrl}</returnUrl>
        <refundNotifyUrl>${pay.refundNotifyUrl}</refundNotifyUrl>
        <caFilePath>e:/nfs/dev/certFiles/terminus-web/default/apiclient_cert_app.p12</caFilePath>
    </pay.wechatpay.app.token>
    <pay.wechatpay.app.account.list>
        <accountNo>default</accountNo>
        <accountName>default app account</accountName>
        <mchId>${wechat.applet.mchId}</mchId>
        <appId>${wechat.applet.appId}</appId>
        <partnerKey>${wechat.applet.secret}</partnerKey>
    </pay.wechatpay.app.account.list>
    <pay.job.trans.wechatpay.app.enable>true</pay.job.trans.wechatpay.app.enable>
    <pay.wechatpay.jsapi.token>
        <notifyUrl>${pay.notifyUrl}</notifyUrl>
        <returnUrl>${pay.h5ReturnUrl}</returnUrl>
        <refundNotifyUrl>${pay.refundNotifyUrl}</refundNotifyUrl>
        <caFilePath>e:/nfs/dev/certFiles/terminus-web/default/apiclient_cert_app.p12</caFilePath>
    </pay.wechatpay.jsapi.token>
    <pay.wechatpay.jsapi.account.list>
        <accountNo>default</accountNo>
        <accountName>default app account</accountName>
        <mchId>${wechat.applet.mchId}</mchId>
        <appId>${parana.wxa.appId}</appId>
        <partnerKey>${wechat.applet.secret}</partnerKey>
    </pay.wechatpay.jsapi.account.list>
    <pay.job.trans.wechatpay.jsapi.enable>true</pay.job.trans.wechatpay.jsapi.enable>
    <pay.wechatpay.qr.token>
        <notifyUrl>${pay.notifyUrl}</notifyUrl>
        <returnUrl>${pay.returnUrl}</returnUrl>
        <refundNotifyUrl>${pay.refundNotifyUrl}</refundNotifyUrl>
        <caFilePath>e:/nfs/dev/certFiles/terminus-web/default/apiclient_cert_app.p12</caFilePath>
    </pay.wechatpay.qr.token>
    <pay.wechatpay.qr.account.list>
        <accountNo>default</accountNo>
        <accountName>default app account</accountName>
        <mchId>${wechat.applet.mchId}</mchId>
        <appId>${wechat.applet.appId}</appId>
        <partnerKey>${wechat.applet.secret}</partnerKey>
    </pay.wechatpay.qr.account.list>
    <pay.job.trans.wechatpay.qr.enable>true</pay.job.trans.wechatpay.qr.enable>
    <m.mall.yang800>https://**************</m.mall.yang800>
    <m.mall.jifen.path>F:/test/images</m.mall.jifen.path>
    <m.mall.jifen.url>https://m.bellamy.shop</m.mall.jifen.url>
    <function>
        <switch>
            <aoXinPush>false</aoXinPush>
            <financePush>true</financePush>
            <unifiedPayment>true</unifiedPayment>
        </switch>
    </function>
    <mercury>
        <pay>
            <host>http://127.0.0.1:8080</host>
            <appCode>DDFXSC</appCode>
            <merchantCode>M2019030616533627870</merchantCode>
            <customs>
                <notify>http://127.0.0.1:10082/api/customs/payment/declare/notify</notify>
            </customs>
        </pay>
    </mercury>
    <ebpCode>3301964J31</ebpCode>
    <wx>
        <backend>
            <url>https://api.weixin.qq.com/cgi-bin</url>
        </backend>
    </wx>
    <gx.api.mall>http://127.0.0.1:4000/gx</gx.api.mall>
</properties>