package moonstone.web.front.user.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class QrCoderCreateRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = -2405228463928350689L;

    /**
     * 商家平台id
     */
    private Long shopId;

    /**
     * 小程序项目id
     */
    private Long projectId;

    /**
     * 小程序页面路径
     */
    private String page;

    /**
     * 小程序前端页面跳转的自定义参数
     */
    private String scene;
}
