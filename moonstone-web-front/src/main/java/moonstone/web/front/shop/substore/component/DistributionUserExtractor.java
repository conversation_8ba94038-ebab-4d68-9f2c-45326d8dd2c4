package moonstone.web.front.shop.substore.component;

import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import moonstone.common.model.CommonUser;
import moonstone.common.model.Either;
import moonstone.order.api.BalanceDetailManager;
import moonstone.order.dto.InComeDetail;
import moonstone.order.model.BalanceDetail;
import moonstone.order.model.SubProxySum;
import moonstone.order.service.HonestFanDataReadService;
import moonstone.order.service.NormalFanDataReadService;
import moonstone.order.service.SubProxyDataReadService;
import moonstone.user.dto.StoreProxyCriteria;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.StoreProxyInfo;
import moonstone.user.model.User;
import moonstone.user.model.UserProfile;
import moonstone.user.service.StoreProxyInfoReadService;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.UserProfileReadService;
import moonstone.user.service.UserReadService;
import moonstone.web.core.model.dto.CurrentProfitVO;
import moonstone.web.core.user.service.StoreProxySubUserManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class DistributionUserExtractor {
    @Autowired
    private UserReadService<User> userReadService;
    @Autowired
    private UserProfileReadService userProfileReadService;
    @Autowired
    private StoreProxyReadService storeProxyReadService;
    @Autowired
    private StoreProxyInfoReadService storeProxyInfoReadService;
    @Autowired
    private NormalFanDataReadService normalFanDataReadService;
    @Autowired
    private HonestFanDataReadService honestFanDataReadService;
    @Autowired
    private BalanceDetailManager balanceDetailManager;
    @Autowired
    private SubProxyDataReadService subProxyDataReadService;
    @Autowired
    private FinishedTradeFeeSumTakerForProxy finishedTradeFeeSumTakerForProxy;

    @Autowired
    private StoreProxySubUserManager storeProxySubUserManager;

    /**
     * 查找分销用户
     *
     * @param user     平台(渠道/店铺)的帐号
     * @param criteria 查询条件
     * @return 该帐号旗下的代理用户
     */
    public Paging<StoreProxy> pagingDistributionUser(CommonUser user, StoreProxyCriteria criteria) {
        //查询条件 用户昵称（可多条）   用户真名（可多条）  手机号（单条）
        //如果用户真名（可多条）不为空，或者用户昵称（可多条和手机号（单条） 为空查询分页
        //获取提现金额需要source_id
        Long sourceId = criteria.getSourceId();
        if (sourceId != null) {
            criteria.setShopId(sourceId);
            criteria.setSourceId(sourceId);
        }
        List<Long> userIdList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(criteria.getMobile())) {
            //查询用户表
            Response<User> rUser = userReadService.findByMobile(criteria.getMobile());

            if (rUser.isSuccess() && rUser.getResult() != null) {
                User targetUser = rUser.getResult();
                userIdList.add(targetUser.getId());
            }
        }
        if (!ObjectUtils.isEmpty(criteria.getNickName())) {
            Either<List<UserProfile>> rProfile = userProfileReadService.findProfileByNickName(criteria.getNickName());
            if (rProfile.isSuccess() && !rProfile.take().isEmpty()) {
                for (UserProfile rProfiles : rProfile.take()) {
                    userIdList.add(rProfiles.getUserId());
                }
            }
        }
        if (!ObjectUtils.isEmpty(criteria.getRecommender())) {
            Either<List<UserProfile>> rProfile = userProfileReadService.findProfileByNickName(criteria.getRecommender());
            if (rProfile.isSuccess() && !rProfile.take().isEmpty()) {
                for (UserProfile rProfiles : rProfile.take()) {
                    Either<Optional<StoreProxy>> optionalResult = storeProxyReadService.findByShopIdAndUserId(criteria.getShopId(), rProfiles.getUserId());
                    if (optionalResult.isSuccess() && optionalResult.take().isPresent()) {
                        userIdList.add(rProfiles.getUserId());
                    }
                }
            }
        }

        if (!ObjectUtils.isEmpty(criteria.getMobile()) || !StringUtils.isEmpty(criteria.getNickName()) || !StringUtils.isEmpty(criteria.getRecommender())) {
            if (userIdList.isEmpty()) {
                return new Paging<>();
            }
            criteria.setUserIds(userIdList);
        }
        //数据隔离
        List<Long> userIdListD;
        if (!ObjectUtils.isEmpty(user.getShopId())) {
            Boolean b = storeProxySubUserManager.isAuthProxy(user.getId(), user.getShopId());
            if (b) {
                userIdListD = storeProxySubUserManager.queryReferenceIdBelongUserAtShop(user.getId(), user.getShopId());
                if (userIdListD.isEmpty()) {
                    return Paging.empty();
                }
                if (!ObjectUtils.isEmpty(criteria.getUserIds()) && !criteria.getUserIds().isEmpty()) {
                    userIdListD.retainAll(criteria.getUserIds());
                    criteria.setUserIds(userIdListD);
                } else {
                    criteria.setUserIds(userIdListD);
                }
            }
        }

        Either<Paging<StoreProxy>> storeProxyPagingRes = storeProxyReadService.paging(criteria);
        for (StoreProxy storeProxy : storeProxyPagingRes.take().getData()) {
            //查询用户表
            Response<User> rUser = userReadService.findById(storeProxy.getUserId());
            if (!rUser.isSuccess() || rUser.getResult() == null) {
                storeProxy.setMobile("");
            } else {
                User storeProxyUser = rUser.getResult();
                storeProxy.setMobile(storeProxyUser.getMobile());
            }
            //查询用户详情表
            Response<UserProfile> rProfile = userProfileReadService.findProfileByUserId(storeProxy.getUserId());

            if (!rProfile.isSuccess() || rProfile.getResult() == null) {
                storeProxy.setRealName("");
                storeProxy.setAvatar("");
            } else {
                UserProfile userProfile = rProfile.getResult();
                storeProxy.setRealName(userProfile.getRealName());
                storeProxy.setAvatar(userProfile.getAvatar());
            }

            //查询父级的用户名
            if (Optional.ofNullable(storeProxy.getSupperId()).isPresent()) {

                Response<UserProfile> rProfile1 = userProfileReadService.findProfileByUserId(storeProxy.getSupperId());
                if (rProfile1.isSuccess() && rProfile1.getResult() != null) {
                    storeProxy.setParentUserName(Optional.ofNullable(rProfile1.getResult().getRealName()).orElse(""));
                } else {
                    storeProxy.setParentUserName("");
                }
                storeProxy.setProxyCradName("门店");
                //设置普粉
                Either<Long> normalFanNum = normalFanDataReadService.countByProxyId(storeProxy.getUserId(), storeProxy.getShopId());
                storeProxy.setNormalFanNum(Optional.ofNullable(normalFanNum.take()).orElse(0L));
                //设置忠粉
                Either<Long> honestFanNum = honestFanDataReadService.countByProxyId(storeProxy.getUserId(), storeProxy.getShopId());
                storeProxy.setHonestFanNum(Optional.ofNullable(honestFanNum.take()).orElse(0L));
            } else {
                storeProxy.setParentUserName("平台");
                storeProxy.setProxyCradName("经销商");
                //获取下级（经销商-下级门店）
                storeProxy.setFanNum(Optional.ofNullable(storeProxyReadService.getSubProxyIsAuth(storeProxy.getUserId(), storeProxy.getShopId()).take()).orElse(0L));

                //设置普粉
                Either<Long> normalFanNum = normalFanDataReadService.countByProxyId(storeProxy.getUserId(), storeProxy.getShopId());
                storeProxy.setNormalFanNum(Optional.ofNullable(normalFanNum.take()).orElse(0L));
                //设置忠粉
                Either<Long> honestFanNum = honestFanDataReadService.countByProxyId(storeProxy.getUserId(), storeProxy.getShopId());
                storeProxy.setHonestFanNum(Optional.ofNullable(honestFanNum.take()).orElse(0L));
            }
            balanceDetailManager.getEarned(storeProxy.getUserId(), storeProxy.getShopId())
                    .map(BalanceDetail::getFee)
                    .ifSuccess(storeProxy::setIncomeSum);
            storeProxy.setAddress(storeProxyInfoReadService.findByProxyId(storeProxy.getId()).take().map(StoreProxyInfo::getAddress).orElse(""));
            //获取累计佣金 下级（经销商-下级门店）
            Either<Optional<SubProxySum>> subProxySumRes = subProxyDataReadService.findByUserId(storeProxy.getUserId(), storeProxy.getShopId());
            if (!subProxySumRes.isSuccess() || !subProxySumRes.take().isPresent()) {
                storeProxy.setIncomeSum(0L);
                storeProxy.setTradeFeeSum(0L);
            } else {
                storeProxy.setTradeFeeSum(Optional.ofNullable(subProxySumRes.take().get().getTradeFeeSum()).orElse(0L));
            }
            storeProxy.setFinishedTradeFeeSum(finishedTradeFeeSumTakerForProxy.getFinishedTradeFeeSum(storeProxy.getShopId(), storeProxy.getUserId()));
            //如果SourceId不为空才会去取可提现金额
            if (!ObjectUtils.isEmpty(criteria.getSourceId())) {
                //获取可提现金额
                val cashRes = balanceDetailManager.getCashByStoreProxy(storeProxy.getUserId(), criteria.getSourceId());

                if (!cashRes.isSuccess()) {
                    log.debug("[Profit](search) find cash  SourceId:{}", criteria.getSourceId());
                    storeProxy.setOutcomeSum(0L);
                }
                if (CollectionUtils.isEmpty(cashRes.getResult())) {
                    log.debug("[Profit](search) find cash  SourceId:{}", criteria.getSourceId());
                    storeProxy.setOutcomeSum(0L);
                }
                CurrentProfitVO dto = new CurrentProfitVO();
                for (InComeDetail inComeDetail : cashRes.getResult()) {
                    if (inComeDetail.isPresent()) {
                        dto.setAvalidProfit(inComeDetail.getFee());
                    } else {
                        dto.setForecastProfit(inComeDetail.getFee());
                    }
                }
                //Optional.ofNullable(dto.getAvalidProfit()).orEuserlse("")
                storeProxy.setOutcomeSum(Optional.of(dto.getAvalidProfit()).orElse(0L));
            } else {
                storeProxy.setOutcomeSum(0L);
            }


        }
        return storeProxyPagingRes.take();
    }
}
