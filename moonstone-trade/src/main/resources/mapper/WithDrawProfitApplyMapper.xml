<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="WithDrawProfitApply">

    <resultMap id="WithDrawProfitApplyMap" type="WithDrawProfitApply">
        <id column="id" property="id"/>
        <result column="fee" property="fee"/>
        <result column="service_fee" property="serviceFee"/>
        <result column="with_draw_at" property="withDrawAt"/>
        <result column="pay_serial_no" property="paySerialNo"/>
        <result column="user_id" property="userId"/>
        <result column="withdraw_account_id" property="withdrawAccountId"/>
        <result column="source_id" property="sourceId"/>
        <result column="status" property="status"/>
        <result column="require_query" property="requireQuery"/>
        <result column="extra_str" property="extraStr"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="user_role" property="userRole"/>
    </resultMap>

    <sql id="tb">
        parana_with_draw_profit_apply
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `fee`
        ,`service_fee`,`with_draw_at`,`pay_serial_no`,`user_id`,`source_id`,`withdraw_account_id`,`status`,
        `require_query`,`extra_str`,`created_at`,`updated_at`, user_role
    </sql>

    <sql id="vals">
        #{fee},
        #{serviceFee},
        #{withDrawAt},
        #{paySerialNo},
        #{userId},
        #{sourceId},
        #{withdrawAccountId},
        #{status},
        #{requireQuery},
        #{extraStr},
        now(),
        now(),
        #{userRole}
    </sql>

    <insert id="create" parameterType="WithDrawProfitApply" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <select id="findById" parameterType="long" resultMap="WithDrawProfitApplyMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>
    <select id="findPaidApplyAfter" parameterType="map" resultType="long">
        SELECT
        id
        FROM
        <include refid="tb"/><![CDATA[
        WHERE updated_at > #{date} and (`status` & (2<<4) = (2<<4))
    ]]>
    </select>

    <select id="findByPaySerialId" parameterType="map" resultMap="WithDrawProfitApplyMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE pay_serial_no= #{paySerialNo} and `status` != -1 limit 1
    </select>
    <select id="findByUserIdAndSourceId" parameterType="map" resultMap="WithDrawProfitApplyMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE user_id= #{userId} and `source_id`=#{sourceId} and `status` != -1
    </select>

    <select id="findByIds" parameterType="list" resultMap="WithDrawProfitApplyMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="update" parameterType="WithDrawProfitApply">
        UPDATE
        <include refid="tb"/>
        <set>
            updated_at = now()
            <if test="fee != null">,`fee` = #{fee}</if>
            <if test="serviceFee != null">,`service_fee` = #{serviceFee}</if>
            <if test="paySerialNo!=null">, `pay_serial_no`=#{paySerialNo}</if>
            <if test="status != null">,`status` = #{status}</if>
            <if test="withDrawAt!= null">,`with_draw_at` = #{withDrawAt}</if>
            <if test="userId!= null">,`user_id` = #{userId}</if>
            <if test="sourceId!= null">,`source_id` = #{sourceId}</if>
            <if test="withdrawAccountId!=null">,`withdraw_account_id`=#{withdrawAccountId}</if>
            <if test="requireQuery!= null">,`require_query` = #{requireQuery}</if>
            <if test="extraStr!= null">,`extra_str` = #{extraStr}</if>
        </set>
        WHERE id=#{id}
    </update>
    <select id="getRequireQuery" parameterType="map" resultMap="WithDrawProfitApplyMap">
        SELECT *
        FROM
        <include refid="tb"/>
        <where>
            require_query = 1
            <if test="sourceId != null">
                AND source_id = #{sourceId}
            </if>
            AND created_at > DATE_ADD(now(), interval -3 month)
            <if test="limit != null">
                limit #{limit}
            </if>
            <if test="limit == null">
                limit 50
            </if>
        </where>
    </select>

    <sql id="criteria">
        <if test="ids!=null">
            id in
            <foreach collection="ids" item="i" open="(" separator="," close=")">
                #{i}
            </foreach>
        </if>
        <if test="sourceId!=null">AND `source_id`=#{sourceId}</if>
        <if test="userId!= null">AND `user_id`= #{userId}</if>
        <if test="userIds!=null">
            and `user_id` in
            <foreach collection="userIds" item="i" open="(-1," separator="," close=")">
                #{i}
            </foreach>
        </if>
        <if test="paySerialNo!=null">AND `pay_serial_no` =#{paySerialNo}</if>
        <if test="fee!= null">AND `fee`= #{fee}</if>
        <if test="bitmasks!=null">
            <foreach collection="bitmasks" item="bit">
                and <![CDATA[
                    `status` & #{bit} = #{bit}
                ]]>
            </foreach>
        </if>
        <if test="notBitMasks!=null">
            <foreach collection="notBitMasks" item="bit">
                and <![CDATA[
                    `status` & #{bit} != #{bit}
                ]]>
            </foreach>
        </if>
        <if test="status!= null">AND `status`= #{status}</if>
        <if test="status = null">AND `status`!= -1</if>
        <if test="withdraw_account_id != null">AND `withdraw_account_id`=#{withdrawAccountId}</if>
        <if test="withDrawStartAt!= null">AND <![CDATA[with_draw_at>= #{withDrawStartAt}]]> </if>
        <if test="withDrawEndAt!= null">AND <![CDATA[with_draw_at<= #{withDrawEndAt}]]> </if>
        <if test="applyStartAt!= null">AND <![CDATA[created_at>= #{applyStartAt}]]> </if>
        <if test="applyEndAt!= null">AND <![CDATA[created_at<= #{applyEndAt}]]> </if>
        <if test="sourceId!=null">AND source_id=#{sourceId}</if>

        <if test="hasUserRole != null"> AND user_role is not null</if>
        <if test="userRole != null"> AND user_role = #{userRole} </if>
        <if test="withdrawApplyStatus != null">
            AND EXISTS( SELECT r.id
                          FROM `parana_account_statement_withdraw_relation` r
                         WHERE r.withdraw_apply_id = t.id
                           AND r.is_valid = 1
                           AND withdraw_apply_status = #{withdrawApplyStatus} )
        </if>

        <if test="userType != null and userType == 1">
            <!-- 服务商 -->
            and exists( SELECT t_ure.`id`
                          FROM `parana_user_relation_entity` t_ure
                         WHERE t_ure.`type` = 4
                           and t_ure.`relation_id_b` = 1
                           and t_ure.`relation_id_a` = t.`source_id`
                           and t_ure.`user_id`  = t.`user_id`)
        </if>
        <if test="userType != null and userType == 2">
            <!-- 门店 -->
            and exists( SELECT t_ss.`id`
                          FROM parana_sub_store t_ss
                         WHERE t_ss.shop_id = t.`source_id`
                           AND t_ss.user_id = t.`user_id` )
        </if>
        <if test="userType != null and userType == 3">
            <!-- 导购 -->
            and exists( SELECT t_sstsg.`id`
                          FROM parana_sub_store_t_store_guider t_sstsg
                         WHERE t_sstsg.shop_id = t.`source_id`
                           AND t_sstsg.store_guider_id = t.`user_id` )
        </if>
    </sql>

    <select id="count" parameterType="map" resultType="long">
        SELECT
        count(1)
        FROM
        <include refid="tb"/> t
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="WithDrawProfitApplyMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/> t
        <where>
            <include refid="criteria"/>
        </where>
        ORDER by
        id desc
        LIMIT #{offset}, #{limit}
    </select>

    <select id="list" parameterType="map" resultMap="WithDrawProfitApplyMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/> t
        <where>
            <include refid="criteria"/>
        </where>
        ORDER by id desc
    </select>

    <select id="pagingBySearchCount" parameterType="map" resultType="long">
        SELECT
        count(distinct(apply.id))
        FROM
        `parana_with_draw_profit_apply` as apply
        <if test="userType != null and userType == 2">
            LEFT JOIN parana_sub_store as store on apply.`user_id` = store.`user_id`
            left join parana_order_role_snapshot as store_ss on apply.`id` = store_ss.`shop_order_id`
                                                            and store_ss.`order_type` = 2
                                                            and store_ss.`user_role` = 2
                                                            and store_ss.`user_id` = apply.`user_id`
                                                            and store_ss.is_valid = 1
        </if>
        <if test="userType != null and userType == 3">
            LEFT JOIN parana_sub_store_t_store_guider as guider on apply.`user_id` = guider.`store_guider_id`
        </if>
        WHERE
        apply.`source_id`=#{sourceId}
        <if test="ids!=null">
            and apply.id in
            <foreach collection="ids" item="i" open="(" separator="," close=")">
                #{i}
            </foreach>
        </if>
        <if test="userId!= null">AND apply.`user_id`= #{userId}</if>
        <if test="userIds!=null">
            and apply.`user_id` in
            <foreach collection="userIds" item="i" open="(-1," separator="," close=")">
                #{i}
            </foreach>
        </if>
        <if test="status!= null">AND apply.`status`= #{status}</if>
        <if test="status = null">AND apply.`status`!= -1</if>
        <if test="userType != null and userType == 1">
            and apply.`user_id` in (
            SELECT user_id FROM `parana_user_relation_entity`
            where type = 4 and `relation_id` = #{shopUserId} and `relation_id_a` = #{sourceId}
            )
        </if>
        <if test="userType != null and userType == 2">and ( store.`shop_id` = #{sourceId} or store_ss.`shop_id` = #{sourceId} )</if>
        <if test="userType != null and userType == 3">and guider.`shop_id` = #{sourceId}</if>
        <if test="bitmasks!=null">
            <foreach collection="bitmasks" item="bit">
                and <![CDATA[
                    apply.`status` & #{bit} = #{bit}
                ]]>
            </foreach>
        </if>
        <if test="notBitMasks!=null">
            <foreach collection="notBitMasks" item="bit">
                and <![CDATA[
                    apply.`status` & #{bit} != #{bit}
                ]]>
            </foreach>
        </if>
        <if test="hasUserRole == null">
            and apply.user_role is null
        </if>
    </select>


    <select id="pagingBySearch" parameterType="map" resultMap="WithDrawProfitApplyMap">
        SELECT distinct
        apply.id,
        apply.`fee`,apply.`service_fee`,apply.`with_draw_at`,
        apply.`pay_serial_no`,apply.`user_id`,
        apply.`source_id`,
        apply.`withdraw_account_id`,apply.`status`,apply.`require_query`,
        apply.`extra_str`,apply.`created_at`,apply.`updated_at`
        FROM
        `parana_with_draw_profit_apply` as apply
        <if test="userType != null and userType == 2">
            LEFT JOIN parana_sub_store as store on apply.`user_id` = store.`user_id`
            left join parana_order_role_snapshot as store_ss on apply.`id` = store_ss.`shop_order_id`
                                                            and store_ss.`order_type` = 2
                                                            and store_ss.`user_role` = 2
                                                            and store_ss.`user_id` = apply.`user_id`
                                                            and store_ss.is_valid = 1
        </if>
        <if test="userType != null and userType == 3">
            LEFT JOIN parana_sub_store_t_store_guider as guider on apply.`user_id` = guider.`store_guider_id`
        </if>
        WHERE
        apply.`source_id`=#{sourceId}
        <if test="ids!=null">
            and apply.id in
            <foreach collection="ids" item="i" open="(" separator="," close=")">
                #{i}
            </foreach>
        </if>
        <if test="userId!= null">AND apply.`user_id`= #{userId}</if>
        <if test="userIds!=null">
            and apply.`user_id` in
            <foreach collection="userIds" item="i" open="(-1," separator="," close=")">
                #{i}
            </foreach>
        </if>
        <if test="status!= null">AND apply.`status`= #{status}</if>
        <if test="status = null">AND apply.`status`!= -1</if>
        <if test="userType != null and userType == 1">
            and apply.`user_id` in (
            SELECT user_id FROM `parana_user_relation_entity`
            where type = 4 and `relation_id` = #{shopUserId} and `relation_id_a` = #{sourceId}
            )
        </if>
        <if test="userType != null and userType == 2">and ( store.`shop_id` = #{sourceId} or store_ss.`shop_id` = #{sourceId} )</if>
        <if test="userType != null and userType == 3">and guider.`shop_id` = #{sourceId}</if>
        <if test="bitmasks!=null">
            <foreach collection="bitmasks" item="bit">
                and <![CDATA[
                    apply.`status` & #{bit} = #{bit}
                ]]>
            </foreach>
        </if>
        <if test="notBitMasks!=null">
            <foreach collection="notBitMasks" item="bit">
                and <![CDATA[
                    apply.`status` & #{bit} != #{bit}
                ]]>
            </foreach>
        </if>
        <if test="hasUserRole == null">
            and apply.user_role is null
        </if>
        ORDER by
        id desc
        LIMIT #{offset}, #{limit}
    </select>


    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>



    <!--    WAITING (等待) - 2 << 3 - 二进制：1000 - 作用位：第4位-->
    <!--    PAID (已支付) - 2 << 4 - 二进制：10000 - 作用位：第5位-->
    <!--    ONLINE (线上支付) - 2 << 5 - 二进制：100000 - 作用位：第6位-->
    <!--    OFFLINE (线下支付) - 2 << 6 - 二进制：1000000 - 作用位：第7位-->
    <!--    CLOSED (已关闭) - 2 << 7 - 二进制：10000000 - 作用位：第8位-->
    <!--    ERROR (错误) - 2 << 8 - 二进制：100000000 - 作用位：第9位-->
    <!--    PAYING (支付中) - 2 << 9 - 二进制：1000000000 - 作用位：第10位-->

    <!--    查询是否存在存在【未处理、未关闭】  或者 存在【 审核操作过、未支付、未关闭】的记录-->
    <!--    追加条件：不等于133、页面上显示【已拒绝】    -->
    <!--    追加条件：不等于1669、页面上显示【提现错误】    -->
    <select id="getPendingWithdrawalRequestCount" resultType="java.lang.Long">

        SELECT COUNT(*)
        FROM <include refid="tb"/>
        where source_id = #{shopId} and user_id = #{userId}
        AND
        (
        ((status &amp; 2) &lt;> 2 AND (status &amp; 256) &lt;> 256)
        OR
        ((status &amp; 2) = 2 AND (status &amp; 32) &lt;> 32 AND (status &amp; 256) &lt;> 256)
        )
        and status != 133 and status != 1669
    </select>
</mapper>