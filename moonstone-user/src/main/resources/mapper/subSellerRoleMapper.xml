<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2022. 杭州但丁云电子科技有限公司.  All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="SubSellerRole">

    <resultMap id="SubSellerRoleMap" type="SubSellerRole">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="desc" property="desc"/>
        <result column="master_user_id" property="masterUserId"/>
        <result column="status" property="status"/>
        <result column="type" property="type"/>
        <result column="allow_json" property="allowJson"/>
        <result column="extra_json" property="extraJson"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_sub_seller_roles
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        `name`, `desc`, master_user_id, status,type, allow_json, extra_json, created_at, updated_at
    </sql>

    <sql id="vals">
        #{name}, #{desc}, #{masterUserId}, #{status},#{type}, #{allowJson}, #{extraJson}, now(), now()
    </sql>

    <insert id="create" parameterType="SubSellerRole" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.name}, #{i.desc}, #{i.masterUserId}, #{i.status}, #{i.type}, #{i.allowJson}, #{i.extraJson}, now(), now()
            )
        </foreach>
    </insert>

    <select id="findById" parameterType="long" resultMap="SubSellerRoleMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </select>

    <select id="findByIds" parameterType="list" resultMap="SubSellerRoleMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="update" parameterType="SubSellerRole">
        update
        <include refid="tb"/>
        <set>
            <if test="name!=null">`name` = #{name},</if>
            <if test="desc!=null">`desc` = #{desc},</if>
            <if test="masterUserId!=null">master_user_id = #{masterUserId},</if>
            <if test="status!=null">status = #{status},</if>
            <if test="status!=null">type = #{type},</if>
            <if test="allowJson!=null">allow_json = #{allowJson},</if>
            <if test="extraJson!=null">extra_json = #{extraJson},</if>
            updated_at = now()
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="long">
        delete from
        <include refid="tb"/>
        where id = #{id}
    </delete>

    <select id="findByMasterUserIdAndStatus" parameterType="map" resultMap="SubSellerRoleMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE master_user_id = #{masterUserId} AND `status` = #{status}
    </select>

    <select id="findByMasterUserIdAndName" parameterType="map" resultMap="SubSellerRoleMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE master_user_id = #{masterUserId}
          AND `name` = #{name}
          AND `status` = 1
    </select>

    <select id="findByMasterUserIdAndNames" parameterType="map" resultMap="SubSellerRoleMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE master_user_id = #{masterUserId}
        AND `name` in
         <foreach collection="nameList" open="(" item="item" separator="," close=")">
             #{item}
         </foreach>
        AND `status` = 1
    </select>

    <sql id="criteria">
        status != -1
        <if test="status != null">AND status = #{status}</if>
        <if test="masterUserId != null">AND master_user_id = #{masterUserId}</if>
    </sql>

    <select id="paging" parameterType="map" resultMap="SubSellerRoleMap">
        select id,
        <include refid="cols_exclude_id"/>
        from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        order by `id` desc
        limit #{offset}, #{limit}
    </select>

    <select id="count" parameterType="map" resultType="long">
        select count(1) from
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="findByUserIdAndType" parameterType="map" resultMap="SubSellerRoleMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        WHERE master_user_id = #{userId} AND `status` = 1 AND `type`=#{type}
    </select>

    <select id="findByUserId" parameterType="map" resultMap="SubSellerRoleMap">
        select
        <include refid="cols_all"/>
        from
        <include refid="tb"/>
        WHERE master_user_id = #{userId} AND `status` = 1
    </select>

</mapper>