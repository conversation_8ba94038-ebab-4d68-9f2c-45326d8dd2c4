package moonstone.web.front.profit.view;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serial;
import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class WithdrawAccountSimpleVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 3102783376934250291L;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行卡姓名
     */
    private String bankCardOwnerName;

    public static WithdrawAccountSimpleVO emptyAccount() {
        WithdrawAccountSimpleVO target = new WithdrawAccountSimpleVO();

        target.setBankCardNo(StringUtils.EMPTY);
        target.setBankCardOwnerName(StringUtils.EMPTY);
        target.setBankName(StringUtils.EMPTY);

        return target;
    }
}
