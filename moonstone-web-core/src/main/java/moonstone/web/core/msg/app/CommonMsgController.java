package moonstone.web.core.msg.app;

import moonstone.cache.ShopCacheHolder;
import moonstone.common.api.APIResp;
import moonstone.common.model.CommonUser;
import moonstone.common.utils.UserUtil;
import moonstone.web.core.component.siteMessage.SiteMessageComponent;
import moonstone.web.core.msg.enu.SiteMessageLevel;
import moonstone.web.core.msg.model.CommonMsg;
import moonstone.web.core.msg.model.UserMsg;
import moonstone.web.core.msg.service.CommonMsgService;
import moonstone.web.core.shop.cache.GuiderCache;
import moonstone.web.core.shop.cache.ServiceProviderCache;
import moonstone.web.core.shop.cache.SubStoreCache;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/msg/")
public record CommonMsgController(MongoTemplate mongoTemplate,
                                  CommonMsgService commonMsgService,
                                  ServiceProviderCache serviceProviderCache,
                                  SubStoreCache subStoreCache,
                                  ShopCacheHolder shopCacheHolder,
                                  GuiderCache guiderCache,
                                  SiteMessageComponent siteMessageComponent) {

    @PostMapping("/add-template")
    public APIResp<Boolean> addTemplate(@RequestBody CommonMsg.CommonMsgTemplate template) {
        commonMsgService().addTemplate(template.title(), template.template(), template.level());
        return APIResp.ok(true);
    }

    @GetMapping("/list")
    public APIResp<List<List<?>>> listGroup(Long shopId) {
        CommonUser user = UserUtil.getCurrentUser();
        long userId = user.getId();

        var messageLevelSet = new HashSet<SiteMessageLevel>();
        if (shopId == null) {
            shopId = UserUtil.getCurrentShopId();
        }
        if (shopId == null) {
            return APIResp.ok(Collections.emptyList());
        }

        //用户角色带来的消息类别
        messageLevelSet.addAll(siteMessageComponent.findMessageLevel(shopId, userId));

        if (guiderCache.findByShopIdAndUserId(shopId, userId).isPresent()) {
            messageLevelSet.add(SiteMessageLevel.REFUND);
        }
        if (subStoreCache.findByShopIdAndUserId(shopId, userId).isPresent()) {
            messageLevelSet.addAll(List.of(SiteMessageLevel.REFUND, SiteMessageLevel.WITHDRAW, SiteMessageLevel.SHOP_AUTH));
        }
        if (serviceProviderCache.findServiceProviderByUserIdAndShopId(userId, shopId) != null) {
            messageLevelSet.addAll(List.of(SiteMessageLevel.REFUND, SiteMessageLevel.WITHDRAW, SiteMessageLevel.SHOP_AUTH));
        }
        if (messageLevelSet.isEmpty()) {
            var shop = shopCacheHolder.findShopById(shopId);
            if (userId == shop.getUserId()) {
                messageLevelSet.addAll(List.of(SiteMessageLevel.REFUND, SiteMessageLevel.WITHDRAW, SiteMessageLevel.SHOP_AUTH));
            } else {
                return APIResp.error("没有信息哦!");
            }
        }

        var list = messageLevelSet.stream()
                .sorted(Comparator.comparingInt(SiteMessageLevel::getDisplayOrder))
                .collect(Collectors.toList());

        List<List<?>> unreadMark = new LinkedList<>();
        for (SiteMessageLevel level : list) {
            unreadMark.add(List.of(level.getDescription(),
                    mongoTemplate.exists(Query.query(Criteria.where("level").is(level.getCode()))
                                    .addCriteria(Criteria.where("shopId").is(shopId))
                                    .addCriteria(Criteria.where("userId").is(userId)).addCriteria(Criteria.where("readAt").is(null)),
                            UserMsg.class)));
        }
        return APIResp.ok(unreadMark);
    }

    @GetMapping("/unread-main")
    public APIResp<Boolean> unreadMain(Long shopId) {
        if (UserUtil.getCurrentUser() == null) {
            return APIResp.ok(false);
        }
        var list = showUnreadList(shopId).getData();
        if (list == null) {
            return APIResp.error("没有消息哦");
        }
        return APIResp.ok(list
                .stream()
                .anyMatch(Boolean.TRUE::equals));
    }

    @GetMapping("/unread")
    public APIResp<List<Boolean>> showUnreadList(Long shopId) {
        var groups = listGroup(shopId);
        if (!groups.ok()) {
            return APIResp.error(groups.getErrorMsg());
        }
        var group = groups.getData();

        return APIResp.ok(group.stream().map(subList -> (Boolean) (subList.get(1))).collect(Collectors.toList()));
    }

    @GetMapping("/read")
    public APIResp<Boolean> read(String uuid) {
        CommonUser user = UserUtil.getCurrentUser();
        if (user == null) {
            return APIResp.ok(false);
        }
        boolean read = mongoTemplate.updateMulti(Query.query(Criteria.where("uuid").is(uuid))
                        .addCriteria(Criteria.where("userId").is(user.getId()))
                        .addCriteria(Criteria.where("readAt").isNull())
                , Update.update("readAt", new Date())
                , UserMsg.class
        ).getModifiedCount() > 0;
        return APIResp.ok(read);
    }

    @GetMapping("/current")
    public APIResp<MsgView> currentMsg(Long shopId, String type, @RequestParam(defaultValue = "1") Integer page, @RequestParam(defaultValue = "20") Integer pageSize) {
        CommonUser user = UserUtil.getCurrentUser();
        if (shopId == null) {
            if (user == null || user.getShopId() == null) {
                return APIResp.error(-2, "未知请求状态, 参数丢失");
            }
            shopId = user.getShopId();
        }
        if (user == null) {
            return APIResp.notLogin();
        }
        int size = pageSize;
        var res = listGroup(shopId);
        if (res.getData() == null) {
            return APIResp.error(res.getErrorMsg());
        }
        int level = SiteMessageLevel.parseLevel(type);
        Query query = Query.query(Criteria.where("userId").is(user.getId()))
                .addCriteria(Criteria.where("shopId").is(shopId))
                .addCriteria(Criteria.where("level").is(level))
                .with(Sort.by(Sort.Order.desc("sentAt")))
                .skip((long) Math.max(0, page - 1) * size).limit(size);
        // query all
        List<UserMsg> userMsgList = mongoTemplate.find(query
                , UserMsg.class);
        Map<CommonMsg, List<UserMsg>> map = new HashMap<>(userMsgList.size());
        for (UserMsg userMsg : userMsgList) {
            CommonMsg msg = commonMsgService().getMsg(userMsg.msgId());
            if (msg == null) {
                continue;
            }
            commonMsgService().getTemplate(msg.template());
            map.computeIfAbsent(msg, a -> new LinkedList<>());
            map.get(msg).add(userMsg);
        }

        long total = mongoTemplate.count(Query.query(Criteria.where("userId").is(user.getId()))
                .addCriteria(Criteria.where("level").is(level))
                .addCriteria(Criteria.where("shopId").is(shopId)), UserMsg.class);
        long unread = mongoTemplate.count(Query.query(Criteria.where("userId").is(user.getId()))
                .addCriteria(Criteria.where("level").is(level))
                .addCriteria(Criteria.where("shopId").is(shopId)).addCriteria(Criteria.where("readAt").isNull()), UserMsg.class);
        // read all
        mongoTemplate.updateMulti(new Query(Criteria.where("userId").is(user.getId()))
                        .addCriteria(Criteria.where("shopId").is(shopId))
                        .addCriteria(Criteria.where("level").is(level)),
                Update.update("readAt", new Date()), UserMsg.class);
        List<IMsgView> iMsgViews = map.entrySet().stream().map(e -> IMsgView.from(e.getKey(), commonMsgService().asMap(), e.getValue()))
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(IMsgView::sentAt).reversed())
                .collect(Collectors.toList());
        return APIResp.ok(new MsgView(unread, iMsgViews, total, page.longValue() * size));
    }


    public record IMsgView(String uuid, String title, String content, Boolean read, Date sentAt) {
        public static IMsgView from(CommonMsg msg, Map<String, CommonMsg.CommonMsgTemplate> templateMap, List<UserMsg> userMsg) {
            CommonMsg.CommonMsgTemplate template = templateMap.get(msg.template());
            StringBuilder builder = new StringBuilder();
            if (template == null) {
                return null;
            }
            String[] parts = template.template().split("\\{\\{");
            builder.append(parts[0]);
            for (int i = 1; i < parts.length; i++) {
                String part = parts[i];
                int v = part.indexOf("}}");
                if (v >= 0) {
                    String index = part.substring(0, v);
                    if (index.length() > 0) {
                        builder.append(msg.content().get(index));
                    }
                    builder.append(part.substring(v + 2));
                }
            }
            return new IMsgView(msg.uuid(), template.title(), builder.toString(), userMsg.stream().anyMatch(read -> read.readAt() != null),
                    userMsg.stream().map(UserMsg::sentAt).filter(Objects::nonNull).findFirst().orElse(null));
        }
    }

    public record MsgView(Long unread, List<IMsgView> msgViewList, Long total, Long currentAt) {
    }
}
