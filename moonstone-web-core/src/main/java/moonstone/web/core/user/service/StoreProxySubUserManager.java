package moonstone.web.core.user.service;

import io.terminus.common.model.Paging;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.model.AuthAbleByStatus;
import moonstone.order.model.HonestFanSum;
import moonstone.order.model.ShopOrder;
import moonstone.order.service.HonestFanDataReadService;
import moonstone.user.criteria.UserRelationEntityCriteria;
import moonstone.user.model.StoreProxy;
import moonstone.user.model.UserRelationEntity;
import moonstone.user.service.StoreProxyReadService;
import moonstone.user.service.UserRelationEntityReadService;
import moonstone.web.core.util.StoreProxyAuthProxy;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * 提供代理下级用户数据
 */
@Slf4j
@Service
@AllArgsConstructor
public class StoreProxySubUserManager {

    private final StoreProxyReadService storeProxyReadService;

    private final UserRelationEntityReadService userRelationEntityReadService;

    private final HonestFanDataReadService honestFanDataReadService;

    /**
     * 查询阶级分销模式中, 用户在这个平台下的所有推荐人Id
     *
     * @param userId 代理用户Id
     * @param shopId 平台的店铺Id
     * @return 查询出来的所有推荐人Id, 可用于订单查询
     * @see moonstone.common.enums.OrderOutFrom#LEVEL_Distribution 阶级分销模式专用
     * @see StoreProxy#getUserId() 推荐人Id
     * @see ShopOrder#getReferenceId() 订单推荐人Id
     */
    public List<Long> queryReferenceIdBelongUserAtShop(Long userId, Long shopId) {
        List<Long> belongedReferenceIdList = new ArrayList<>(25);
        storeProxyReadService.findByShopIdAndUserId(shopId, userId)
                .orElseGet(Optional::empty)
                .ifPresent(self -> {
                    // 如果的确是代理 添加自己的userId
                    belongedReferenceIdList.add(userId);
                    // 经销商拥有自己的所有门店的推荐人Id
                    switch (self.getLevel()) {
                        case 1:
                            storeProxyReadService.findBySupperIdAndShopId(userId, shopId)
                                    .map(List::stream).orElseGet(Stream::empty)
                                    .map(StoreProxy::getUserId).forEach(belongedReferenceIdList::add);
                        case 2:
                        default:
                            break;
                    }
                });
        return belongedReferenceIdList;
    }

    /**
     * 判断是否为授权过的合法代理
     */
    public Boolean isAuthProxy(Long userId, Long shopId) {
        return storeProxyReadService.findByShopIdAndUserId(shopId, userId)
                .orElseGet(Optional::empty).map(StoreProxyAuthProxy::build)
                .filter(AuthAbleByStatus::isAuthed).isPresent();
    }

    /**
     * 查找该代理旗下的所有用户Id
     *
     * @param shopId 平台店铺Id
     * @param userId 代理用户Id
     * @return 代理旗下的用户的userId
     */
    public List<Long> findUserIdFromStoreProxyAtShop(Long userId, Long shopId) {
        // 没有平台Id 则不运行查询, 如果不是代理 也不查询
        if (!isAuthProxy(userId, Objects.requireNonNull(shopId))) {
            return Collections.emptyList();
        }
        UserRelationEntityCriteria criteria = new UserRelationEntityCriteria();
        // 设置为查询属于下级店铺的用户信息
        criteria.setType(UserRelationEntity.UserRelationType.Member.getType());
        // 设置查询用户信息的店铺Id
        criteria.setRelationId(shopId);
        // 设置查询用户的下级店铺Id
        criteria.setRelationIdA(queryReferenceIdBelongUserAtShop(userId, shopId));
        // 直接查询所有的用户
        criteria.setPageSize(Integer.MAX_VALUE);
        return Optional.ofNullable(userRelationEntityReadService.paging(criteria).getResult())
                .map(Paging::getData).map(List::stream).orElseGet(Stream::empty)
                .map(UserRelationEntity::getUserId).collect(Collectors.toList());
    }

    /**
     * 查询旗下所有的代理包括自己的忠实粉丝数据
     */
    public List<Long> queryBelongProxyHonestFanUserIdList(Long userId, Long shopId) {
        Set<Long> userIdSet = new HashSet<>();
        storeProxyReadService.findByShopIdAndUserId(shopId, userId).orElseGet(Optional::empty)
                .ifPresent(self -> {
                    switch (self.getLevel()) {
                        case 1:
                            for (StoreProxy storeProxy : storeProxyReadService.findBySupperIdAndShopId(userId, shopId).orElseGet(Collections::emptyList)) {
                                userIdSet.addAll(honestFanDataReadService.findByProxyId(storeProxy.getUserId(), storeProxy.getShopId()).map(List::stream)
                                        .orElseGet(Stream::empty).map(HonestFanSum::getUserId).collect(Collectors.toList()));
                            }
                        case 2:
                            // 下级分销商查询自己的忠粉数据
                            userIdSet.addAll(honestFanDataReadService.findByProxyId(userId, shopId).map(List::stream)
                                    .orElseGet(Stream::empty).map(HonestFanSum::getUserId).collect(Collectors.toList()));
                        default:
                            break;
                    }
                });
        return new ArrayList<>(userIdSet);
    }

}