package moonstone.settle.api;

import moonstone.settle.model.PayChannelDailySummary;
import moonstone.settle.model.PlatformTradeDailySummary;
import moonstone.settle.model.SellerTradeDailySummary;

import java.util.List;

/**
 * 汇总规则接口
 *
 * DATE: 16/8/12 上午10:12 <br>
 * MAIL: <EMAIL> <br>
 * AUTHOR: zhanghecheng
 */
public interface SummaryRule {
    /**
     * 平台日汇总合并公式
     * @param forward 正向订单的平台日汇总
     * @param backward 逆向订单的平台日汇总
     * @return 待持久化的平台订单日汇总
     */
    List<PlatformTradeDailySummary> platformDaily(PlatformTradeDailySummary forward, PlatformTradeDailySummary backward);

    /**
     * 商家日汇总合并公式
     * @param forwardList 每个商家的正向订单日汇总
     * @param backwardList 每个商家的逆向订单日汇总
     * @return 待持久化的商家订单日汇总
     */
    List<SellerTradeDailySummary> sellerDaily(List<SellerTradeDailySummary> forwardList, List<SellerTradeDailySummary> backwardList);

    /**
     * 支付渠道日汇总合并公式
     * @param forwardList 正向订单的支付渠道日汇总
     * @param backwardList 逆向订单的支付渠道日汇总
     * @return 待持久化的支付渠道日汇总
     */
    List<PayChannelDailySummary> channelDaily(List<PayChannelDailySummary> forwardList, List<PayChannelDailySummary> backwardList);
}
