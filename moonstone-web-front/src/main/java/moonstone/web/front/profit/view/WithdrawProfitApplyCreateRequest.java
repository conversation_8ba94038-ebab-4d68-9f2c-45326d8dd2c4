package moonstone.web.front.profit.view;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class WithdrawProfitApplyCreateRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = -3212606905660936584L;

    private Long shopId;

    /**
     * 银行卡号
     */
    private String bankCardNo;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行卡姓名
     */
    private String bankCardOwnerName;
}
