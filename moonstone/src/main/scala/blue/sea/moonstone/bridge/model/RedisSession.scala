package blue.sea.moonstone.bridge.model

import io.vertx.core.Future
import io.vertx.redis.client.{Command, Redis, Request, Response}

import java.nio.charset.Charset
import java.util.function.Consumer
import java.util.stream.Collectors
import java.util.{Collections, Objects, Optional, UUID}

object RedisSession {


  /**
   * redis index use
   */
  private object Keys extends Enumeration {
    type Keys = Value
    val creationTime, keys, lastAccessTime = Value
  }

}

/**
 * init the redis session
 * read the session creation at jobs
 *
 * @param redis         redis
 * @param sessionPrefix session prefix
 * @param sessionId     session id
 * @param sessionInject inject session action
 * @param maxAge        session max age
 * @param createdAt     session created at
 */
class RedisSession(var redis: Redis, var sessionPrefix: String, var sessionId: String, var sessionInject: Consumer[String], var maxAge: Int, var createdAt: Long) {
  if (sessionId != null && !sessionId.matches("\\w*")) {
    sessionId = UUID.randomUUID.toString.replace("-", "")
  }
  if (createdAt == -2L && sessionId != null) {
    redis.send(Request.cmd(Command.GET).arg(getIndex(RedisSession.Keys.creationTime.toString, meta = true))).map((res: Response) => if (res == null) {
      -1L
    }
    else {
      res.toLong
    }).asInstanceOf[Future[Long]].onSuccess(this.setCreatedAt)
  }

  private def setCreatedAt(createdAt: Long): Unit = {
    this.createdAt = createdAt
  }

  /**
   * get session (name:value), the key map to name, redis(key:value)
   *
   * @param name session name
   * @param meta meta used by redis session system, not for session
   * @return key
   */
  private def getIndex(name: String, meta: Boolean): String = {
    if (Objects.isNull(sessionId)) {
      sessionId = UUID.randomUUID.toString.replace("-", "")
    }
    String.format(if (meta) {
      "%s:%s@_%s"
    }
    else {
      "%s:%s#%s"
    }, sessionPrefix, sessionId, name)
  }

  def getCreationTime: Long = {
    createdAt
  }

  def getId: String = {
    sessionId
  }

  def getLastAccessedTime: Future[Long] = {
    redis.send(Request.cmd(Command.GET).arg(getIndex(RedisSession.Keys.lastAccessTime.toString, meta = true)))
      .map((res: Response) =>
        Optional.ofNullable(res)
          .map[Long](_.toLong)
          .orElse(-1L))
      .asInstanceOf[Future[Long]]
  }

  def getValue(name: String): Future[String] = {
    if (getId != null) {
      redis.send(Request.cmd(Command.GET).arg(getIndex(name, meta = false)))
        .map((response: Response) => if (response == null) {
          null
        }
        else {
          response.toString(Charset.defaultCharset)
        }).asInstanceOf[Future[String]]
        .onSuccess((_: String) => freshAccess())
    } else {
      Future.failedFuture("No Session Init")
    }
  }

  private def freshAccess(): Unit = {
    redis.send(Request.cmd(Command.SET).arg(getIndex(RedisSession.Keys.lastAccessTime.toString, meta = true)).arg(System.currentTimeMillis))
    keySet.onSuccess((keys: java.util.Set[String]) => {
      def foo(keys: java.util.Set[String]) = {
        keys.forEach(key => redis.send(Request.cmd(Command.EXPIRE).arg(getIndex(key, meta = false)).arg(maxAge)))
        redis.send(Request.cmd(Command.EXPIRE).arg(getIndex(RedisSession.Keys.keys.toString, meta = true)).arg(maxAge))
      }

      foo(keys)
    })
  }

  def getKeys: Future[java.util.Set[String]] = {
    keySet.onSuccess((_: java.util.Set[String]) => freshAccess())
  }

  private def keySet: Future[java.util.Set[String]] = {
    redis.send(Request.cmd(Command.SMEMBERS).arg(getIndex(RedisSession.Keys.keys.toString, meta = true)))
      .map((response: Response) =>
        if (response == null) {
          Collections.emptySet[String]
        }
        else {
          response.stream
            .map((value: Response) => value.toString(Charset.defaultCharset()))
            .collect(Collectors.toSet[String])
        }).asInstanceOf[Future[java.util.Set[String]]]
  }

  def putValue(name: String, value: Any): Future[Void] = {
    if (isNew) {
      redis.send(Request.cmd(Command.SET).arg(getIndex(RedisSession.Keys.creationTime.toString, meta = true)).arg(System.currentTimeMillis))
      createdAt = System.currentTimeMillis
      sessionInject.accept(sessionId)
    }
    if (value != null) {
      redis.send(Request.cmd(Command.SET).arg(getIndex(name, meta = false)).arg(value.toString))
        .onSuccess((_: Response) => redis.send(Request.cmd(Command.SADD).arg(getIndex(RedisSession.Keys.keys.toString, meta = true)).arg(name)))
        .onSuccess((_: Response) => redis.send(Request.cmd(Command.EXPIRE).arg(getIndex(name, meta = false)).arg(maxAge))
        ).onSuccess((_: Response) => freshAccess()).mapEmpty
    }
    else {
      removeValue(name)
    }
  }

  def removeValue(name: String): Future[Void] = {
    redis.send(Request.cmd(Command.DEL).arg(getIndex(name, meta = false)))
      .onSuccess((_: Response) => redis.send(Request.cmd(Command.SREM).arg(getIndex(RedisSession.Keys.keys.toString, meta = true)).arg(name))
      ).onSuccess((_: Response) => freshAccess()).mapEmpty
  }

  def invalidate(): Unit = {
    keySet.onSuccess((keys: java.util.Set[String]) => {
      def foo(keys: java.util.Set[String]) = {
        keys.forEach(key => redis.send(Request.cmd(Command.DEL).arg(getIndex(key, meta = false))))
        redis.send(Request.cmd(Command.DEL).arg(getIndex(RedisSession.Keys.keys.toString, meta = true)))
        redis.send(Request.cmd(Command.DEL).arg(getIndex(RedisSession.Keys.lastAccessTime.toString, meta = true)))
        redis.send(Request.cmd(Command.DEL).arg(getIndex(RedisSession.Keys.creationTime.toString, meta = true)))
      }

      foo(keys: java.util.Set[String])
    })
  }

  def isNew: Boolean = {
    getCreationTime < 0
  }
}
