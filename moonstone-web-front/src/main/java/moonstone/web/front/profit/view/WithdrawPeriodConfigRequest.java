package moonstone.web.front.profit.view;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
public class WithdrawPeriodConfigRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 4799859860884554534L;

    /**
     * 配置明细
     */
    private List<DetailConfig> detailList;

    @Data
    public static class DetailConfig implements Serializable {
        @Serial
        private static final long serialVersionUID = -6235508049421258220L;

        /**
         * 角色类型
         *
         * @see moonstone.common.enums.SubStoreUserIdentityEnum
         */
        private Integer userRole;

        /**
         * 每月可提现的开始日期
         */
        private Integer dayOfMonthFrom;

        /**
         * 每月可提现的结束日期
         */
        private Integer dayOfMonthTo;
    }
}
