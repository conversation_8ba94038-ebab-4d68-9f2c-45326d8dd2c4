package moonstone.web.core.user.view;

import lombok.AllArgsConstructor;
import lombok.Getter;
import moonstone.common.model.AuthAbleByStatus;

@AllArgsConstructor
@Getter
public final class Auth implements AuthAbleByStatus {
    public static Auth Pass = new Auth(2);
    public static Auth Pending = new Auth(6);
    public static Auth Reject = new Auth(4);
    final Integer status;

    public void setStatus(Integer status){
        // don't impl it, this should be static and final
    }
}
