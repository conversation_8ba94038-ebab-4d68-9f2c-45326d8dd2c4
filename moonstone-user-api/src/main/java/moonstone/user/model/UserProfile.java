package moonstone.user.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSetter;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import io.terminus.common.utils.JsonMapper;
import lombok.*;
import moonstone.common.constants.JacksonType;
import moonstone.common.utils.EmojiBreaker;
import moonstone.common.utils.ImageUrlHandler;

import java.io.Serializable;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.Map;

/**
 * Created by cuiwentao on 16/3/8.
 */
@ToString
@EqualsAndHashCode(of = "userId")
@NoArgsConstructor
public class UserProfile implements Serializable {


    private static final long serialVersionUID = 8610043299655883012L;

    private static final ObjectMapper objectMapper = JsonMapper.nonEmptyMapper().getMapper();


    @Getter
    @Setter
    private Long id;                                    //主键

    @Getter
    @Setter
    private Long userId;                                //用户id

    @Getter
    @Setter
    private String birth;                               //出生日期

    /**
     * 目前被当作用户Wx名称使用了
     * 请尽可能使用UserWx中的nickname来当作真实名称
     */
    private String realName;

    @Setter
    private String encodeName;

    @Getter
    @Setter
    private Integer gender;                             //性别:0男 1女

    @Getter
    @Setter
    private Integer provinceId;                         //省id

    @Getter
    @Setter
    private String province;                            //省

    @Getter
    @Setter
    private Integer cityId;                             //城id

    @Getter
    @Setter
    private String city;                                //城

    @Getter
    @Setter
    private Integer regionId;                           //区id

    @Getter
    @Setter
    private String region;                              //区

    @Getter
    @Setter
    private String street;                             //地址

    private String avatar;                              //头像

    /**
     * 不存存数据库
     */
    @Getter
    private Map<String, String> extra;

    /**
     * 放扩展信息, json存储, 存数据库
     */
    @Getter
    @JsonIgnore
    private String extraJson;


    @Getter
    @Setter
    private Date createdAt;

    @Getter
    @Setter
    private Date updatedAt;

    public UserProfile(Long userId) {
        this.userId = userId;
    }

    @JsonSetter
    public void setAvatar(String avatar) {
        this.avatar = ImageUrlHandler.complete(avatar);
    }

    @JsonIgnore
    public String getAvatar() {
        return ImageUrlHandler.simplify(this.avatar);
    }

    @JsonProperty("avatar")
    public String getAvatar_() {
        return this.avatar;
    }

    public void setExtraJson(String extraJson) throws Exception {
        this.extraJson = extraJson;
        if (Strings.isNullOrEmpty(extraJson)) {
            this.extra = Collections.emptyMap();
        } else {
            this.extra = objectMapper.readValue(extraJson, JacksonType.MAP_OF_STRING);
        }
    }

    public void setRealName(String realName) {
        if (realName == null) {
            this.realName = null;
            encodeName = null;
            return;
        }
        EmojiBreaker emojiBreaker = new EmojiBreaker.EmojiBringer(realName);
        setEncodeName(emojiBreaker.getEncode());
        this.realName = emojiBreaker.getStr();
    }

    public String getRealName() {
        if (encodeName == null) return getRealName_();
        return new String(Base64.getDecoder().decode(encodeName.getBytes()));
    }

    public String getEncodeName() {
        if (encodeName == null && realName != null) {
            setRealName(realName);
        } else return encodeName;
        return encodeName;
    }

    public String getRealName_() {
        if (encodeName == null && realName != null) {
            setRealName(this.realName);
        }
        return realName;
    }

    public void setRealName_(String realName) {
        this.realName = realName;
    }
}
