import io.vertx.core.Vertx
import io.vertx.ext.web.client.WebClient

object GateWayTest extends App {
  val now = System.currentTimeMillis().toString
  val clientId = "HNG78FDVNG78FDNG"
  val path = "/shop/api.bridge"
  val data = md5("")
  val secret: String = "8F86CC66275A4FFC85D36E5FC6DDA1DA"
  val sign: String = md5(
    Map("data" -> data, "timestamp" -> now, "path" -> path, "clientId" -> clientId)
      .toSeq.sortBy(_._1)
      .foldLeft(new StringBuilder())((builder, pair) => builder.addAll(pair._1).addAll(pair._2)).addAll(secret)
      .toString())
  val client = WebClient.create(Vertx.vertx())
  System.out.println(s"http://danding-gateway-dev.yang800.com.cn:8800$path")
  client.postAbs(s"http://danding-gateway-dev.yang800.com.cn:8800$path")
    .putHeader("timestamp", now)
    .putHeader("clientId", clientId)
    .putHeader("sign", sign)
    .sendJson(null)
    .onSuccess(res => {
      System.out.println(res.body())
    })
    .onFailure(error => {
      error.printStackTrace()
    })


  def md5(data: String): String = {
    val m = java.security.MessageDigest.getInstance("MD5")
    val b = data.getBytes("UTF-8")
    m.update(b, 0, b.length)
    new java.math.BigInteger(1, m.digest()).toString(16).toUpperCase
  }
}