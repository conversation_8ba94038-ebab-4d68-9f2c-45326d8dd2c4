package moonstone.common.utils;

import io.vertx.core.json.JsonObject;

import java.net.InetAddress;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;

public interface UUID {
    /**
     * UUID -> [ CHECK | TIME-STAMP + SALT | hash-Random-SALT | ID]
     * it's predicate-able when know the address
     *
     * @param id id
     * @return UUID
     */
    static UUID generate(Number id) {
        return generate(Long.toHexString(id.longValue()).getBytes(StandardCharsets.UTF_8));
    }

    static UUID generate() {
        return UUID.generate(Context.rand.random.nextLong());
    }

    /**
     * generate the 32 uuid as normal one
     *
     * @param id id
     * @return uuid
     */
    static UUID generate(byte[] id) {
        long now = System.currentTimeMillis();
        byte[] timestamp = Context.longToBytes(now);
        LocalDateTime.now().format(DateTimeFormatter.ofPattern("YYYYMMdd"));
        byte[] salt = Context.nodeSalt.join();
        byte[] uuid = new byte[16];
        byte[] base = new byte[salt.length + timestamp.length + 4];
        byte[] rand = Context.rand.random.generateSeed(4);
        System.arraycopy(salt, 0, base, 0, salt.length);
        System.arraycopy(timestamp, 0, base, salt.length, timestamp.length);
        System.arraycopy(rand, 0, base, base.length - 4, 4);
        base = B3Util.b3sum(base);
        for (int i = 0; i < salt.length; i++) {
            uuid[i] = (byte) (base[i] ^ salt[salt.length - 1 - i]);
        }
        for (int i = 0; i < timestamp.length; i++) {
            uuid[i + salt.length] = (byte) (base[i + salt.length] ^ timestamp[i]);
        }
        System.arraycopy(base, 12, uuid, 12, 4);
        for (int i = 0; i < id.length && i < 16; i++) {
            int addr = 16 - id.length + i;
            uuid[addr] = (byte) (uuid[addr] ^ id[i]);
        }
        // copy into the rom
        StringBuilder uuidBuilder = new StringBuilder();
        int start = 0;
        int[] copy = new int[]{5, 7, 9, 11, 16};
        for (int limit : copy) {
            for (int i = start; i < limit; i++) {
                uuidBuilder.append(Long.toHexString(0xFF & uuid[i]));
            }
            if (limit != 16) {
                uuidBuilder.append("-");
            }
            start = limit;
        }
        // 2 as the check all
        // give a rand one to help gen no duplicate uuid
        return new UUIDImp(uuidBuilder.toString().toUpperCase(Locale.ROOT), now, id);
    }

    /**
     * generate 64 uuid with check bits
     *
     * @param id the id
     * @return uuid
     */
    static UUID uuid64(byte[] id) {
        long now = System.currentTimeMillis();
        byte[] timestamp = Context.longToBytes(now);
        byte[] salt = Context.nodeSalt.join();
        byte[] rand = Context.rand.random.generateSeed(4);
        // use the time as the base
        byte[] base = B3Util.b3sum(salt);
        // left 10 as the id
        byte[] uuid = new byte[32];
        for (int i = 0; i < salt.length; i++) {
            // save the rand for the decode use
            uuid[i] = (byte) (salt[i] ^ rand[i]);
        }
        System.arraycopy(base, 4, uuid, 4, 24);
        // change the time
        for (int i = 0; i < timestamp.length; i++) {
            uuid[8 + i] = (byte) (uuid[8 + i] ^ timestamp[i]);
        }
        uuid[20] = 0x00;
        uuid[21] = 0x00;
        // enlarge the id length
        System.arraycopy(base, 22, uuid, 22, 10 - id.length);
        for (int i = 0; i < id.length; i++) {
            uuid[32 - id.length + i] = (byte) (uuid[32 - id.length + i] ^ id[i]);
        }
        byte[] checked = B3Util.b3sum(uuid);
        uuid[20] = checked[0];
        uuid[20] = checked[1];
        // copy into the rom
        StringBuilder uuidBuilder = new StringBuilder();
        int start = 0;
        int[] copy = new int[]{9, 14, 19, 24, 32};
        for (int limit : copy) {
            for (int i = start; i < limit; i++) {
                uuidBuilder.append(Long.toHexString(0xFF & uuid[i]));
            }
            if (limit != 32) {
                uuidBuilder.append("-");
            }
            start = limit;
        }
        // 2 as the check all
        // give a rand one to help gen no duplicate uuid
        return new UUIDImp(uuidBuilder.toString().toUpperCase(Locale.ROOT), now, id);
    }

    static UUID generate(String id) {
        return generate(id.getBytes(StandardCharsets.UTF_8));
    }

    static UUID randomUUID() {
        return generate(Context.rand.id.incrementAndGet());
    }

    /**
     * generate a outside uuid from a entity contain export id
     * @apiNote the uuid is static generate, same id will generate a same uuid in same date
     * @param idRelatedEntity entity
     * @return uuid
     */
    static UUID generateStaticAtNow(Object idRelatedEntity) {
        return generateStatic(idRelatedEntity, System.currentTimeMillis());
    }
    /**
     * generate a outside uuid from a entity contain export id
     * @apiNote the uuid is static generate, same id will generate a same uuid in same date
     * @param idRelatedEntity entity
     * @return uuid
     */
    static UUID generateStatic(Object idRelatedEntity, long timestamp) {
        Object id = JsonObject.mapFrom(idRelatedEntity).getValue("id");
        byte[] idSeq;
        if (id instanceof Number number) {
            idSeq = Context.longToBytes(number.longValue());
        } else {
            idSeq = id.toString().getBytes(StandardCharsets.UTF_8);
        }
        return generateStatic(idRelatedEntity.getClass().getSimpleName(), timestamp, idSeq);
    }

    static UUID generateStatic(String namespace, long timestamp, Number id) {
        return generateStatic(namespace, timestamp,Context.longToBytes(id.longValue()));
    }

    /**
     * generate a static uuid from id
     *
     * @param id id
     * @return uuid
     */
    static UUID generateStatic(String namespace, long timestamp, byte[] id) {
        LocalDateTime time = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        short year = (short) time.getYear();
        short day = (short) time.getDayOfYear();
        byte[] nodeSalt = Context.nodeSalt.join();
        byte[] nodeCoverPrefix = new byte[8];

        System.arraycopy(nodeSalt, 0, nodeCoverPrefix, 0, Math.min(nodeSalt.length, 8));
        nodeCoverPrefix = B3Util.b3sum(nodeCoverPrefix);

        byte[] cover = B3Util.b3sum(namespace.getBytes(StandardCharsets.UTF_8));
        ByteBuffer byteBuffer = ByteBuffer.allocate(8);
        byteBuffer.putShort(year);
        byteBuffer.putShort(day);
        byte[] timeField = byteBuffer.array();

        byte[] uuid = new byte[16];
        System.arraycopy(nodeCoverPrefix, 8, uuid, 0, 16);
        // copy the salt reversed
        for (int i = 0; i < 4; i++) {
            uuid[i] ^= nodeSalt[nodeSalt.length - 1 - i];
        }
        // copy the time, ignore the year
        for (int i = 0; i < 4; i++) {
            uuid[2 + i] ^= timeField[i];
        }
        byte checkA = (byte) 0xff;
        // check sum
        for (int i = 0; i < 7; i++) {
            checkA ^= uuid[i];
        }
        uuid[7] = checkA;
        System.arraycopy(cover, 0, uuid, 8, 8);
        // copy the id
        for (int i = 0; i < 8 && i < id.length; i++) {
            uuid[15 - i] ^= id[i];
        }
        // copy into the rom
        StringBuilder uuidBuilder = new StringBuilder();
        int start = 0;
        int[] copy = new int[]{5, 7, 9, 11, 16};
        for (int limit : copy) {
            for (int i = start; i < limit; i++) {
                uuidBuilder.append(Long.toHexString(0xFF & uuid[i]));
            }
            if (limit != 16) {
                uuidBuilder.append("-");
            }
            start = limit;
        }
        return new UUIDImp(uuidBuilder.toString().toUpperCase(Locale.ROOT), timestamp, id);
    }

    enum Context {
        rand;
        static final CompletableFuture<byte[]> nodeSalt = new CompletableFuture<>();

        static {
            try {
                for (InetAddress addr : InetAddress.getAllByName("localhost")) {
                    if (addr.isLoopbackAddress()) {
                        continue;
                    }
                    nodeSalt.complete(addr.getAddress());
                }
                if (!nodeSalt.isDone()) {
                    nodeSalt.complete(InetAddress.getLocalHost().getAddress());
                }
            } catch (Exception e) {
                System.err.println("Fail to get UUID");
                e.printStackTrace(System.err);
            }
        }

        final SecureRandom random = new SecureRandom();
        final AtomicLong id = new AtomicLong(0);

        static byte[] longToBytes(long l) {
            byte[] result = new byte[8];
            for (int i = 7; i >= 0; i--) {
                result[i] = (byte) (l & 0xFF);
                l >>= 8;
            }
            return result;
        }
    }

    record UUIDImp(String uuid, Long timestamp, byte[] id) implements UUID {
        @Override
        public String toString() {
            return uuid;
        }
    }
}
