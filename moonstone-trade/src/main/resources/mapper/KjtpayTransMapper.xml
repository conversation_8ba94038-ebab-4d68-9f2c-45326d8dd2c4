<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright (c) 2013 杭州端点网络科技有限公司
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="KjtpayTrans">
    <resultMap id="kjtpayTransMap" type="KjtpayTrans">
        <id property="id" column="id"/>
        <result property="outerNo" column="outer_no"/>
        <result property="origOuterNo" column="orig_outer_no"/>
        <result property="innerNo" column="inner_no"/>
        <result property="type" column="type"/>
        <result property="paidDate" column="paid_date"/>
        <result property="amount" column="amount"/>
        <result property="rate" column="rate"/>
        <result property="rateFee" column="rate_fee"/>
        <result property="status" column="status"/>
        <result property="orderAt" column="order_at"/>
        <result property="paidAt" column="paid_at"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>


    <sql id="table">
        parana_kjtpay_trans
    </sql>


    <sql id="columns">
        outer_no, orig_outer_no, inner_no, `type`, amount,
        rate, rate_fee, status,order_at, paid_at, created_at, updated_at
    </sql>

    <insert id="create" parameterType="KjtpayTrans" useGeneratedKeys="true" keyProperty="id">
        insert into <include refid="table"/> (<include refid="columns"/>)
        values
        (
          #{outerNo}, #{origOuterNo}, #{innerNo}, #{type}, #{amount},
          #{rate}, #{rateFee}, #{status}, #{orderAt}, #{paidAt},now(), now()
        )
    </insert>

    <select id="load" parameterType="long" resultMap="kjtpayTransMap">
        select id,
        <include refid="columns"/>
        from <include refid="table"/>
        where id = #{id}
    </select>

    <select id="findByInnerNo" parameterType="string" resultMap="kjtpayTransMap">
        select id,
        <include refid="columns"/>
        from <include refid="table"/>
        where inner_no = #{innerNo}
    </select>

    <select id="findByOuterNo" parameterType="string" resultMap="kjtpayTransMap">
        select id,
        <include refid="columns"/>
        from <include refid="table"/>
        where outer_no = #{outerNo}
    </select>

    <select id="findByOrigOuterNo" parameterType="string" resultMap="kjtpayTransMap">
        select id,
        <include refid="columns"/>
        from <include refid="table"/>
        where orig_outer_no = #{origOuterNo}
    </select>

    <select id="findRefundTrans" parameterType="string" resultMap="kjtpayTransMap">
        select id,
        <include refid="columns"/>
        from <include refid="table"/>
        where outer_no = #{batchNo}
    </select>



    <select id="list" parameterType="KjtpayTrans" resultMap="kjtpayTransMap">
        select id, <include refid="columns"/>
        from <include refid="table"/>
            <include refid="condition"/>
    </select>


    <sql id="condition">
        <where>
            <if test="iwAccountLogId != null">
                AND <![CDATA[ iw_account_log_id = #{iwAccountLogId} ]]>
            </if>
        </where>
    </sql>

    <select id="findById" parameterType="long" resultMap="kjtpayTransMap">
        select id,
        <include refid="columns"/>
        from <include refid="table"/>
        WHERE id =#{id}
    </select>


</mapper>