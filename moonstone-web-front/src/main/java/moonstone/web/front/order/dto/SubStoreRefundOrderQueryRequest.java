package moonstone.web.front.order.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class SubStoreRefundOrderQueryRequest implements Serializable {
    @Serial
    private static final long serialVersionUID = 1008337897305600176L;

    /**
     * 商家平台id
     */
    private Long shopId;

    /**
     * REFUND_IN_PROGRESS-退款中，REFUND_SUCCESS-已退款，REFUND_FAIL-退款失败
     *
     * @see moonstone.order.dto.fsm.RefundStatusView
     */
    private String status;

    /**
     * 分页查询，当前页码
     */
    private Integer pageNo;

    /**
     * 分页查询，每页记录数量
     */
    private Integer pageSize;
}
