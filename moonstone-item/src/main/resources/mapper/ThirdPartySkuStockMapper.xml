<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="ThirdPartySkuStock">

    <resultMap id="ThirdPartySkuStockMap" type="ThirdPartySkuStock">
        <id property="id" column="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="third_party_id" property="thirdPartyId"/>
        <result column="outer_sku_id" property="outerSkuId"/>
        <result column="outer_sku_name" property="outerSkuName"/>
        <result column="depot_code" property="depotCode"/>
        <result column="depot_name" property="depotName"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="batch" property="batch"/>
        <result column="effect_period" property="effectPeriod"/>
        <result column="authentic_stock" property="authenticStock"/>
        <result column="defective_stock" property="defectiveStock"/>
        <result column="volume" property="volume"/>
        <result column="packVolume" property="packVolume"/>
        <result column="weight" property="weight"/>
        <result column="status" property="status"/>
        <result column="source_type" property="sourceType"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <sql id="tb">
        parana_third_party_sku_stock
    </sql>

    <sql id="cols_all">
        id,
        <include refid="cols_exclude_id"/>
    </sql>

    <sql id="cols_exclude_id">
        shop_id, third_party_id, outer_sku_id, outer_sku_name, depot_code, depot_name, supplier_name, batch,
        effect_period,
        authentic_stock, defective_stock, volume , `pack_volume`, weight, status, created_at, updated_at,source_type
    </sql>

    <sql id="vals">
        #{shopId}, #{thirdPartyId}, #{outerSkuId}, #{outerSkuName}, #{depotCode}, #{depotName}, #{supplierName},
        #{batch},
        #{effectPeriod},
        #{authenticStock}, #{defectiveStock}, #{volume}, #{packVolume}, #{weight}, #{status}, now(), now(),#{sourceType}
    </sql>

    <sql id="criteria">
        <if test="ids != null">AND id IN
            <foreach collection="ids" open="(" separator="," close=")" item="id">#{id}</foreach>
        </if>
        <if test="shopId != null">AND shop_id = #{shopId}</if>
        <if test="thirdPartyId!=null">AND third_party_id = #{thirdPartyId}</if>
        <if test="outerSkuId != null">AND outer_sku_id = #{outerSkuId}</if>
        <if test="outerSkuName != null">AND outer_sku_name LIKE CONCAT(#{outerSkuName} ,'%')</if>
        <if test="supplierName != null">AND supplier_name LIKE CONCAT(#{supplierName} ,'%')</if>
        <if test="updatedFrom != null">AND <![CDATA[updated_at >= #{updatedFrom}]]> </if>
        <if test="updatedTo != null">AND <![CDATA[updated_at < #{updatedTo}]]> </if>
        <if test="statuses == null and status == null">
            AND `status` != -1
        </if>
        <if test="status != null">AND status = #{status}</if>
        <if test="statuses != null">
            and status in
            <foreach collection="statuses" open="(" close=")" separator="," item="s">
                #{s}
            </foreach>
        </if>
    </sql>

    <sql id="custom_sort_type">
        <if test="sortType != null">
            <if test="sortType == 1">ASC</if>
            <if test="sortType == 2">DESC</if>
        </if>
    </sql>

    <sql id="custom_sort">
        <if test="sortBy != null">
            <if test="sortBy == 'id'">ORDER BY id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'thirdPartyId'">ORDER BY third_party_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'outerSkuId'">ORDER BY outer_sku_id
                <include refid="custom_sort_type"/>
            </if>
            <if test="sortBy == 'updatedAt'">ORDER BY updated_at
                <include refid="custom_sort_type"/>
            </if>
        </if>
    </sql>

    <insert id="create" parameterType="ThirdPartySkuStock" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        (<include refid="vals"/>)
    </insert>

    <insert id="creates" parameterType="list">
        INSERT INTO
        <include refid="tb"/>
        (<include refid="cols_exclude_id"/>)
        VALUES
        <foreach collection="list" item="i" index="index" separator=",">
            (
            #{i.thirdPartyId}, #{i.outerSkuId}, #{i.outerSkuName}, #{i.depotCode}, #{i.depotName}, #{i.batch},
            #{i.effectPeriod}, #{i.authenticStock}, #{i.defectiveStock}, #{i.volume}, #{i.packVolume}, #{i.weight},
            #{i.status}, now(), now()
            )
        </foreach>
    </insert>

    <select id="findDepotCode" parameterType="map" resultType="String">
      SELECT
      depot_code
      FROM
      <include refid="tb"/>
      <where>
        third_party_id = #{systemId}
        AND
        outer_sku_id = #{outerSkuId}
        AND `status` > 0
      </where>
      limit 1
      </select>

    <select id="findById" parameterType="long" resultMap="ThirdPartySkuStockMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id = #{id} LIMIT 1
    </select>

    <select id="findByIds" parameterType="list" resultMap="ThirdPartySkuStockMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="findByThirdPartyIdAndOuterSkuId" parameterType="map" resultMap="ThirdPartySkuStockMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            shop_id = #{shopId} AND
            third_party_id = #{thirdPartyId} AND
            outer_sku_id = #{outerSkuId} AND
            status != -1
            <if test="depotName != null">
                AND depot_name like concat(#{depotName},'%')
            </if>
        </where>
    </select>

    <select id="findByParams" parameterType="map" resultMap="ThirdPartySkuStockMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            shop_id = #{shopId} AND
            third_party_id=#{thirdPartyId} AND
            outer_sku_id=#{outerSkuId} AND
            <if test="depot_code != null">
                depot_code=#{depotCode} AND
            </if>
            batch=#{batch}
        </where>
        LIMIT 1
    </select>

    <select id="findByThirdPartyId" parameterType="long" resultMap="ThirdPartySkuStockMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        WHERE third_party_id=#{ThirdPartyId} AND status != -1
    </select>

    <update id="update" parameterType="ThirdPartySkuStock">
        UPDATE
        <include refid="tb"/>
        <set>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="thirdPartyId != null">third_party_id = #{thirdPartyId},</if>
            <if test="outerSkuId != null">outer_sku_id = #{outerSkuId},</if>
            <if test="outerSkuName != null">outer_sku_name = #{outerSkuName},</if>
            <if test="depotCode != null">depot_code = #{depotCode},</if>
            <if test="depotName != null">depot_name = #{depotName},</if>
            <if test="supplierName != null">supplier_name = #{supplierName},</if>
            <if test="batch != null">batch = #{batch},</if>
            <if test="effectPeriod != null">effect_period = #{effectPeriod},</if>
            <if test="authenticStock != null">authentic_stock = #{authenticStock},</if>
            <if test="defectiveStock != null">defective_stock = #{defectiveStock},</if>
            <if test="volume != null">volume = #{volume},</if>
            <if test="packVolume != null">packVolume = #{packVolume},</if>
            <if test="weight != null">weight = #{weight},</if>
            <if test="status!=null">status=#{status},</if>
            <if test="source_type!=null ">`source_type` = #{sourceType},</if>
            updated_at = now()
        </set>
        WHERE id = #{id}
    </update>

    <update id="updateStatus" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE id = #{id} and status != #{status}
    </update>

    <update id="updateStatusByThirdPartyAndSkuId" parameterType="map">
        UPDATE
        <include refid="tb"/>
        SET status = #{status}, updated_at = now()
        WHERE
        shop_id = #{shopId} AND
        third_party_id = #{thirdPartyId} AND outer_sku_id = #{outerSkuId} and status != #{status}
    </update>

    <delete id="delete" parameterType="long">
        DELETE FROM
        <include refid="tb"/>
        WHERE id = #{id}
    </delete>

    <update id="deletes" parameterType="list">
        UPDATE
        <include refid="tb"/>
        SET status=-1
        WHERE id IN
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="count" parameterType="map" resultType="long">
        SELECT COUNT(1)
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
    </select>

    <select id="paging" parameterType="map" resultMap="ThirdPartySkuStockMap">
        SELECT
        <include refid="cols_all"/>
        FROM
        <include refid="tb"/>
        <where>
            <include refid="criteria"/>
        </where>
        <include refid="custom_sort"/>
        LIMIT #{offset}, #{limit}
    </select>

</mapper>
