package moonstone.order.service;

import io.terminus.common.model.Response;
import moonstone.common.enums.AccountStatementWithdrawStatusEnum;
import moonstone.order.model.AccountStatementWithdrawRelation;

import java.util.List;

public interface AccountStatementWithdrawRelationWriteService {

    /**
     * 批量新增
     *
     * @param list
     * @return
     */
    Response<Boolean> batchInsert(List<AccountStatementWithdrawRelation> list);

    /**
     * 更新提现状态
     *
     * @param withdrawApplyId
     * @param sourceStatus
     * @return
     */
    Response<Boolean> updateWithdrawStatus(Long withdrawApplyId, AccountStatementWithdrawStatusEnum sourceStatus,
                                           AccountStatementWithdrawStatusEnum targetStatus);
}
