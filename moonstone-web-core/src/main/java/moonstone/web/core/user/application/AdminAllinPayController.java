package moonstone.web.core.user.application;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.api.Result;
import moonstone.web.core.component.pay.allinpay.user.AllinPayAccountBindService;
import moonstone.web.core.component.pay.allinpay.user.req.*;
import moonstone.web.core.component.pay.allinpay.user.res.AllinPayAccountDetailVO;
import moonstone.web.core.component.pay.allinpay.user.res.CardBinVo;
import moonstone.web.core.component.pay.allinpay.user.res.SubStoreInfoVO;
import moonstone.web.op.allinpay.enums.AllinPayRoleTypeEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 通联支付相关接口
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/allinPay")
@ApiModel(value = "通联支付")
public class AdminAllinPayController {

	@Resource
	private AllinPayAccountBindService allinPayAccountBindService;

	@PostMapping("subStoreInfo")
	@ApiOperation(value = "门店开户信息-弃用", notes = "门店开户信息")
	public Result<SubStoreInfoVO> subStoreInfo(@RequestBody SubStoreInfoDTO dto){
		Result<SubStoreInfoVO> result = allinPayAccountBindService.subStoreInfo(dto);
		return result;
	}
	@PostMapping("allinPayAccountDetail")
	@ApiOperation(value = "门店通联账户详情", notes = "门店通联账户详情")
	public Result<AllinPayAccountDetailVO> allinPayAccountDetail(@RequestBody SubStoreInfoDTO dto){
		Result<AllinPayAccountDetailVO> result = allinPayAccountBindService.allinPayAccountDetail(dto);
		return result;
	}

	@PostMapping("switchAvailability")
	@ApiOperation(value = "切换启用的通联账户", notes = "切换启用的通联账户")
	public Result<SubStoreInfoVO> switchAvailability(@RequestBody SwitchAvailabilityDTO dto){
		Result<SubStoreInfoVO> result = allinPayAccountBindService.switchAvailability(dto);
		return result;
	}


	/**
	 * 【支付设置-请求绑定银行卡】绑定 用于 租客、房东、个人组织、企业组织（企业可以绑定个人卡）
	 * @param applyBindBankCardDTO 请求绑卡
	 * @return 成功与否
	 */
	@PostMapping("applyBindBankCard")
	@ApiOperation(value = "银行卡:静默绑定", notes = "【绑定银行卡】绑定")
	public Result<Boolean> applyBindBankCard(@RequestBody @Valid AllinPayApplyBindBankCardDTO applyBindBankCardDTO){
		if(applyBindBankCardDTO != null){
			// 该接口仅作用在企业会员上
			applyBindBankCardDTO.setAllinpayRoleType(AllinPayRoleTypeEnum.COMPANY.getType());
			// 使用提交的门店的userId
		}
		Result<Boolean> result = allinPayAccountBindService.applyBindBankCard(applyBindBankCardDTO);
		return result;
	}
	@PostMapping("unBindBankCard")
	@ApiOperation(value = "银行卡:解绑", notes = "解绑银行卡")
	public Result<Boolean> unBindBankCard(@RequestBody AllinPayUnBindBankCardDTO applyBindBankCardDTO){
		if(applyBindBankCardDTO != null){
			// 该接口仅作用在企业会员上
			applyBindBankCardDTO.setAllinpayRoleType(AllinPayRoleTypeEnum.COMPANY.getType());
			// 使用提交的门店的userId
		}
		Result<Boolean> result = allinPayAccountBindService.unBindBankCard(applyBindBankCardDTO);
		return result;
	}


	@PostMapping("cardBin")
	@ApiOperation(value = "银行卡:查询卡bin", notes = "查询卡bin")
	public Result<CardBinVo> cardBin(@RequestBody @Valid AllinPayCardBinQueryDTO cardBinQueryDTO){
		Result<CardBinVo> result = allinPayAccountBindService.cardBin(cardBinQueryDTO);
		return result;
	}

	@PostMapping("registerCompanyMember")
	@ApiOperation(value = "添加企业", notes = "企业会员开户H5")
	public Result<String> registerCompanyMember(@RequestBody AllinPayRegisterCompanyMemberDTO dto){
		Result<String> result = allinPayAccountBindService.registerCompanyMember(dto);
		return result;
	}

}
