package moonstone.adv.impl.service;

import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.exception.JsonResponseException;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.adv.impl.dao.AdvColumnDao;
import moonstone.adv.impl.dao.AdvDao;
import moonstone.adv.impl.manager.AdvManager;
import moonstone.adv.model.Adv;
import moonstone.adv.model.AdvColumn;
import moonstone.adv.service.AdvWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by CaiZhy on 2018/10/9.
 */
@Slf4j
@Service
@RpcProvider
public class AdvWriteServiceImpl implements AdvWriteService{
    @Autowired
    private AdvDao advDao;

    @Autowired
    private AdvColumnDao advColumnDao;

    @Autowired
    private AdvManager advManager;

    @Override
    public Response<Long> create(Adv adv){
        try {
            AdvColumn advColumn = advColumnDao.findById(adv.getColumnId());
            if (advColumn == null){
                log.error("can not find advColumn by id={}", adv.getColumnId());
                throw new JsonResponseException("adv.column.not.exist");
            }
            adv.setColumnSn(advColumn.getSn());
            if (adv.getStatus() == null) {
                adv.setStatus(0);
            }
            Integer maxSortIndex = advDao.maxSortIndexByColumnIdShopId(adv.getColumnId(), adv.getShopId());
            adv.setSortIndex(maxSortIndex == null ? 1 : maxSortIndex + 1);
            advDao.create(adv);
            return Response.ok(adv.getId());
        } catch (Exception e) {
            log.error("fail to create adv({}), cause: {}", adv, Throwables.getStackTraceAsString(e));
            return Response.fail("adv.create.fail");
        }
    }

    @Override
    public Response<Boolean> update(Adv adv){
        try {
            adv.setSortIndex(null);
            return Response.ok(advDao.update(adv));
        } catch (Exception e) {
            log.error("fail to update adv({}), cause: {}", adv, Throwables.getStackTraceAsString(e));
            return Response.fail("adv.update.fail");
        }
    }

    @Override
    public Response<Boolean> updateStatus(Long id, Integer status){
        try {
            return Response.ok(advDao.updateStatus(id, status));
        } catch (Exception e) {
            log.error("fail to update adv(id={}) status={}, cause: {}", id, status, Throwables.getStackTraceAsString(e));
            return Response.fail("adv.update.fail");
        }
    }

    @Override
    public Response<Boolean> delete(Long id){
        try {
            return Response.ok(advDao.delete(id));
        } catch (Exception e) {
            log.error("fail to delete adv(id={}), cause: {}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("adv.delete.fail");
        }
    }

    @Override
    public Response<Boolean> move(Long advId, int direction) {
        try {
            Adv adv = advDao.findById(advId);
            if (adv == null) {
                log.warn("adv(id={}) not found, can not move direction={}", advId, direction);
                return Response.fail("adv.not.found");
            }
            List<Adv> brothers = advDao.findByColumnIdShopId(adv.getColumnId(), adv.getShopId());
            if (brothers.isEmpty()) {
                return Response.ok(Boolean.FALSE);
            }
            Collections.sort(brothers);
            // init index
            Map<Long, Integer> toIndex = Maps.newHashMap();
            for (int i=0; i<brothers.size(); ++i) {
                Adv b = brothers.get(i);
                int idx = i + 1;
                toIndex.put(b.getId(), idx);
            }
            // exchange index
            for (int i=0; i<brothers.size(); ++i) {
                Adv b = brothers.get(i);
                int idx = i + 1;
                if (Objects.equals(b.getId(), advId)) {
                    Adv n;
                    if (i + 1 < brothers.size() && direction == 1) {
                        // move down
                        n = brothers.get(i + 1);
                        toIndex.put(n.getId(), idx);
                        toIndex.put(b.getId(), idx + 1);
                    }
                    if (i > 0 && direction == -1) {
                        // move up
                        n = brothers.get(i - 1);
                        toIndex.put(n.getId(), idx);
                        toIndex.put(b.getId(), idx - 1);
                    }
                    break;
                }
            }
            // calculate delta updates
            List<Adv> toUpdates = Lists.newArrayList();
            for (int i=0; i<brothers.size(); ++i) {
                Adv b = brothers.get(i);
                int idx = toIndex.get(b.getId());
                if (!Objects.equals(b.getSortIndex(), idx)) {
                    toUpdates.add(toUpdateIndex(b.getId(), idx));
                }
            }
            if (!toUpdates.isEmpty()) {
                advManager.batchUpdate(toUpdates);
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("move adv(id={}) failed, direction={}, cause:{}",
                    advId, direction, Throwables.getStackTraceAsString(e));
            return Response.fail("adv.move.fail");
        }
    }

    private Adv toUpdateIndex(Long id, Integer idx) {
        Adv c = new Adv();
        c.setId(id);
        c.setSortIndex(idx);
        return c;
    }
}
