package moonstone.weDistributionApplication.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import io.terminus.common.utils.Splitters;
import lombok.extern.slf4j.Slf4j;
import moonstone.common.utils.LogUtil;
import moonstone.user.model.User;
import moonstone.user.service.UserReadService;
import moonstone.weDistributionApplication.impl.dao.WeDistributionApplicationDao;
import moonstone.weDistributionApplication.model.WeDistributionApplication;
import moonstone.weDistributionApplication.service.WeDistributionApplicationReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by CaiZhy on 2018/12/3.
 */
@Slf4j
@Service
@RpcProvider
public class WeDistributionApplicationReadServiceImpl implements WeDistributionApplicationReadService {
    @Autowired
    private WeDistributionApplicationDao weDistributionApplicationDao;
    @Autowired
    private UserReadService<User> userUserReadService;

    @Override
    public Response<WeDistributionApplication> findById(Long id) {
        try {
            WeDistributionApplication weDistributionApplication = weDistributionApplicationDao.findById(id);
            return Response.ok(weDistributionApplication);
        } catch (Exception e) {
            log.error("fail to find weDistribution application by id={}, cause: {}", id, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.find.fail");
        }
    }

    @Override
    public Response<List<WeDistributionApplication>> findByWeShopId(Long weShopId) {
        try {
            List<WeDistributionApplication> weDistributionApplications = weDistributionApplicationDao.findByWeShopId(weShopId);
            return Response.ok(weDistributionApplications);
        } catch (Exception e) {
            log.error("fail to find weDistribution applications by weShopId={}, cause: {}", weShopId, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.find.fail");
        }
    }

    @Override
    public Response<List<WeDistributionApplication>> findByWeShopIdAuditing(Long weShopId) {
        try {
            List<WeDistributionApplication> weDistributionApplications = weDistributionApplicationDao.findByWeShopIdAuditing(weShopId);
            return Response.ok(weDistributionApplications);
        } catch (Exception e) {
            log.error("fail to find auditing weDistribution applications by weShopId={}, cause: {}", weShopId, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.find.fail");
        }
    }

    @Override
    public Response<List<WeDistributionApplication>> findByUserId(Long userId) {
        try {
            List<WeDistributionApplication> weDistributionApplications = weDistributionApplicationDao.findByUserId(userId);
            return Response.ok(weDistributionApplications);
        } catch (Exception e) {
            log.error("fail to find weDistribution applications by userId={}, cause: {}", userId, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.find.fail");
        }
    }

    @Override
    public Response<List<WeDistributionApplication>> findByRecommendShopId(Long recommendShopId) {
        try {
            List<WeDistributionApplication> weDistributionApplications = weDistributionApplicationDao.findByRecommendShopId(recommendShopId);
            return Response.ok(weDistributionApplications);
        } catch (Exception e) {
            log.error("fail to find weDistribution applications by recommendShopId={}, cause: {}", recommendShopId, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.find.fail");
        }
    }

    @Override
    public Response<List<WeDistributionApplication>> findByRecommendUserId(Long recommendUserId) {
        try {
            List<WeDistributionApplication> weDistributionApplications = weDistributionApplicationDao.findByRecommendUserId(recommendUserId);
            return Response.ok(weDistributionApplications);
        } catch (Exception e) {
            log.error("fail to find weDistribution applications by recommendUserId={}, cause: {}", recommendUserId, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.find.fail");
        }
    }

    @Override
    public Response<WeDistributionApplication> findByWeShopIdRecommendAuditing(Long weShopId, Integer recommendType,
                                                                               Long recommendShopId, Long recommendUserId) {
        try {
            WeDistributionApplication weDistributionApplication =
                    weDistributionApplicationDao.findByWeShopIdRecommendAuditing(weShopId, recommendType, recommendShopId, recommendUserId);
            return Response.ok(weDistributionApplication);
        } catch (Exception e) {
            log.error("fail to find auditing weDistribution application by weShopId={}, recommendType={}, recommendShopId={}, recommendUserId={}, cause: {}",
                    weShopId, recommendType, recommendShopId, recommendUserId, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.find.fail");
        }
    }

    @Override
    public Response<Paging<WeDistributionApplication>> paging(List<Long> ids,
                                                              Long weShopId,
                                                              List<Long> userIds,
                                                              String mobile,
                                                              Long shopId,
                                                              Integer recommendType,
                                                              Long recommendShopId,
                                                              Long recommendUserId,
                                                              Integer status,
                                                              String statuses,
                                                              Integer pageNo,
                                                              Integer pageSize) {
        try {
            PageInfo pageInfo = new PageInfo(pageNo, pageSize);
            Map<String, Object> criteria = new HashMap<>();
            if (!ObjectUtils.isEmpty(mobile)) {
                Response<List<Long>> userIdList = userUserReadService.findIdsLikeMobile(mobile);
                if (userIdList.isSuccess()) {
                    if (userIds == null) userIds = new ArrayList<>();
                    userIds.addAll(userIdList.getResult());
                } else {
                    log.error("{} not found by mobile[{}]", LogUtil.getClassMethodName(), mobile);
                }
            }
            if (ids != null && !CollectionUtils.isEmpty(ids)) {
                criteria.put("ids", ids);
            }
            if (weShopId != null) {
                criteria.put("weShopId", weShopId);
            }
            if (!CollectionUtils.isEmpty(userIds)) {
                criteria.put("userIds", userIds);
            }
            if (shopId != null) {
                criteria.put("shopId", shopId);
            }
            if (recommendType != null) {
                criteria.put("recommendType", recommendType);
            }
            if (recommendShopId != null) {
                criteria.put("recommendShopId", recommendShopId);
            }
            if (recommendUserId != null) {
                criteria.put("recommendUserId", recommendUserId);
            }
            if (StringUtils.hasText(statuses)) {
                criteria.put("statuses", Splitters.COMMA.split(statuses));
            } else if (status != null) {
                criteria.put("status", status);
            }
            Paging<WeDistributionApplication> weDistributionApplicationPaging =
                    weDistributionApplicationDao.paging(pageInfo.getOffset(), pageInfo.getLimit(), criteria);
            return Response.ok(weDistributionApplicationPaging);
        } catch (Exception e) {
            log.error("fail to paging weDistribution applications by ids={}, weShopId={}, userIds={}, recommendType={}, recommendShopId={}, " +
                            "recommendUserId={}, status={}, statuses={}, pageNo={}, pageSize={}, cause: {}",
                    ids, weShopId, userIds, recommendType, recommendShopId, recommendUserId,
                    status, statuses, pageNo, pageSize, Throwables.getStackTraceAsString(e));
            return Response.fail("weDistributionApplication.page.fail");
        }
    }
}
