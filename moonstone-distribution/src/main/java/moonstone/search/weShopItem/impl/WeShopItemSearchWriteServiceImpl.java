package moonstone.search.weShopItem.impl;

import com.google.common.base.Throwables;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.Response;
import io.terminus.search.api.IndexExecutor;
import io.terminus.search.api.model.IndexTask;
import lombok.extern.slf4j.Slf4j;
import moonstone.item.impl.dao.ItemAttributeDao;
import moonstone.item.impl.dao.ItemDao;
import moonstone.item.model.ItemAttribute;
import moonstone.search.dto.IndexedWeShopItem;
import moonstone.search.weShopItem.WeShopItemSearchWriteService;
import moonstone.weShop.impl.dao.WeShopItemDao;
import moonstone.weShop.model.WeShopItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Author:  CaiZhy
 * Date:    2018/12/24
 */
@Slf4j
@Service
@RpcProvider
public class WeShopItemSearchWriteServiceImpl implements WeShopItemSearchWriteService {

    private final IndexExecutor indexExecutor;

    private final IndexedWeShopItemFactory indexedWeShopItemFactory;

    private final WeShopItemDao weShopItemDao;

    private final ItemDao itemDao;

    private final ItemAttributeDao itemAttributeDao;

    private final IndexedWeShopItemIndexAction indexedWeShopItemIndexAction;

    private final IndexedWeShopItemGuarder indexedWeShopItemGuarder;

    @Autowired
    public WeShopItemSearchWriteServiceImpl(IndexExecutor indexExecutor,
                                            IndexedWeShopItemFactory indexedWeShopItemFactory,
                                            WeShopItemDao weShopItemDao,
                                            ItemDao itemDao,
                                            ItemAttributeDao itemAttributeDao,
                                            IndexedWeShopItemIndexAction indexedWeShopItemIndexAction,
                                            IndexedWeShopItemGuarder indexedWeShopItemGuarder) {
        this.indexExecutor = indexExecutor;
        this.indexedWeShopItemFactory = indexedWeShopItemFactory;
        this.weShopItemDao = weShopItemDao;
        this.itemDao = itemDao;
        this.itemAttributeDao = itemAttributeDao;
        this.indexedWeShopItemIndexAction = indexedWeShopItemIndexAction;
        this.indexedWeShopItemGuarder = indexedWeShopItemGuarder;
    }

    /**
     * 索引微分销商品
     *
     * @param weShopItemId 微分销商品id
     * @return 是否调用成功
     */
    @Override
    public Response<Boolean> index(Long weShopItemId) {
        try {
            WeShopItem weShopItem = weShopItemDao.findById(weShopItemId);
            ItemAttribute itemAttribute = itemAttributeDao.findByItemId(weShopItem.getItemId());
            IndexedWeShopItem indexedWeShopItem = indexedWeShopItemFactory.create(weShopItem, itemAttribute);
            indexExecutor.submit(indexedWeShopItemIndexAction.indexTask(indexedWeShopItem));
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to index weShopItem(id={}), cause:{}",
                    weShopItemId, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.index.fail");
        }
    }

    /**
     * 删除微分销商品
     *
     * @param weShopItemId 微分销商品id
     * @return 是否调用成功
     */
    @Override
    public Response<Boolean> delete(Long weShopItemId) {
        try {
            indexExecutor.submit(indexedWeShopItemIndexAction.deleteTask(weShopItemId));
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to delete weShopItem(id={}) from index, cause:{}",
                    weShopItemId, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.index.fail");
        }
    }

    /**
     * 通过商品id删除微分销商品
     *
     * @param itemId 商品id
     * @return 是否调用成功
     */
    @Override
    public Response<Boolean> deleteByItem(Long itemId) {
        try {
            List<WeShopItem> weShopItems = weShopItemDao.findByItemId(itemId);
            for (WeShopItem weShopItem : weShopItems) {
                indexExecutor.submit(indexedWeShopItemIndexAction.deleteTask(weShopItem.getItemId()));
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to delete weShopItem from index by itemId={}, cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.index.fail");
        }
    }

    /**
     * 索引或者删除微分销商品
     *
     * @param weShopItemId 微分销商品id
     * @return 是否调用成功
     */
    @Override
    public Response<Boolean> update(Long weShopItemId) {
        try {
            WeShopItem weShopItem = weShopItemDao.findById(weShopItemId);
            if (Objects.nonNull(weShopItem) && indexedWeShopItemGuarder.indexable(weShopItem)) {
                ItemAttribute itemAttribute = itemAttributeDao.findByItemId(weShopItem.getItemId());
                IndexedWeShopItem indexedWeShopItem = indexedWeShopItemFactory.create(weShopItem, itemAttribute);
                indexExecutor.submit(indexedWeShopItemIndexAction.indexTask(indexedWeShopItem));
            } else {
                indexExecutor.submit(indexedWeShopItemIndexAction.deleteTask(weShopItemId));
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update weShopItem(id={}) from index, cause:{}",
                    weShopItemId, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.index.fail");
        }
    }

    /**
     * 根据商品id索引或者删除微分销商品
     *
     * @param itemId 商品id
     * @return 是否调用成功
     */
    @Override
    public Response<Boolean> updateByItem(Long itemId) {
        try {
            List<WeShopItem> weShopItems = weShopItemDao.findByItemId(itemId);
            for (WeShopItem weShopItem : weShopItems) {
                if (indexedWeShopItemGuarder.indexable(weShopItem)) {
                    ItemAttribute itemAttribute = itemAttributeDao.findByItemId(weShopItem.getItemId());
                    IndexedWeShopItem indexedWeShopItem = indexedWeShopItemFactory.create(weShopItem, itemAttribute);
                    indexExecutor.submit(indexedWeShopItemIndexAction.indexTask(indexedWeShopItem));
                } else {
                    indexExecutor.submit(indexedWeShopItemIndexAction.deleteTask(weShopItem.getId()));
                }
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("failed to update weShopItem from index by itemId={}, cause:{}",
                    itemId, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.index.fail");
        }
    }

    @Override
    public Response<Boolean> updateByWeShopId(Long weShopId) {
        try {
            Long lastId = weShopItemDao.maxIdByShopId(weShopId) + 1;
            while (true) {
                List<WeShopItem> weShopItems = weShopItemDao.listByShopId(weShopId, lastId, 200);
                if (weShopItems.isEmpty()) {
                    break;
                }

                doIndex(weShopItems);
                lastId = Iterables.getLast(weShopItems).getId();
            }
            return Response.ok(Boolean.TRUE);
        } catch (Exception e) {
            log.error("fail to update weShopItem by weShopId={},cause:{}",
                    weShopId, Throwables.getStackTraceAsString(e));
            return Response.fail("weShopItem.index.fail");
        }
    }

    private void doIndex(List<WeShopItem> weShopItems) {
        List<Long> itemIds = Lists.transform(weShopItems, WeShopItem::getItemId);

        List<ItemAttribute> itemAttributes = itemAttributeDao.findByItemIds(itemIds);

        Map<Long, ItemAttribute> ItemAttributebyItemIdIndex = Maps.uniqueIndex(itemAttributes, ItemAttribute::getItemId);


        for (WeShopItem weShopItem : weShopItems) {
            try {
                if (indexedWeShopItemGuarder.indexable(weShopItem)) {
                    IndexedWeShopItem indexedWeShopItem = indexedWeShopItemFactory.create(weShopItem, ItemAttributebyItemIdIndex.get(weShopItem.getItemId()));
                    IndexTask indexTask = indexedWeShopItemIndexAction.indexTask(indexedWeShopItem);
                    indexExecutor.submit(indexTask);
                } else {
                    IndexTask indexTask = indexedWeShopItemIndexAction.deleteTask(weShopItem.getId());
                    indexExecutor.submit(indexTask);
                }
            } catch (Exception e) {
                log.error("failed to index weShopItem(id={}),cause:{}", weShopItem.getId(), Throwables.getStackTraceAsString(e));
            }
        }
    }
}
