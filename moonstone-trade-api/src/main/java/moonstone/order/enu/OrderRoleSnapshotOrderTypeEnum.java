package moonstone.order.enu;

/**
 * 单据的角色快照信息 - 单据类型枚举
 */
public enum OrderRoleSnapshotOrderTypeEnum {

    SHOP_ORDER(1, "主订单"),
    WITHDRAW_APPLY(2, "提现申请单"),
    ACCOUNT_STATEMENT(3, "账单"),
    ;

    private final Integer code;
    private final String descriptions;

    OrderRoleSnapshotOrderTypeEnum(Integer code, String descriptions) {
        this.code = code;
        this.descriptions = descriptions;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescriptions() {
        return descriptions;
    }

    public static OrderRoleSnapshotOrderTypeEnum parse(Integer code) {
        for (var current : OrderRoleSnapshotOrderTypeEnum.values()) {
            if (current.getCode().equals(code)) {
                return current;
            }
        }

        return null;
    }
}
