package moonstone.order.enu;

public enum FundsTransferPaymentStatusEnum {
    NOT_PAID(1, "未支付"),
    PAYING(2, "支付中"),
    PAID(3, "已支付"),
    FAILED(4, "支付失败"),
    ;

    private final Integer code;
    private final String description;

    FundsTransferPaymentStatusEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
