package moonstone.web.front.item.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import moonstone.item.dto.ViewedItem;
import moonstone.web.front.item.search.vo.SearchItemProfitView;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ViewedItemForLevelDistribution extends ViewedItem {
    /**
     * profit of the seller will take, the profit is create by level distribution
     */
    SearchItemProfitView profit;

    /**
     * stock name of these item, gain from sku, require ThirdPartySkuStock Relation
     */
    List<String> stockName;
    /**
     * proxyName of that level distribution mode
     */
    String proxyName;

    public static ViewedItemForLevelDistribution from(ViewedItem viewedItem, List<String> stockName, String proxyName) {
        if (Objects.isNull(viewedItem)) {
            return null;
        }
        ViewedItemForLevelDistribution profitView = new ViewedItemForLevelDistribution();
        BeanUtils.copyProperties(viewedItem, profitView);
        profitView.stockName = stockName;
        profitView.proxyName = proxyName;
        return profitView;
    }
}
