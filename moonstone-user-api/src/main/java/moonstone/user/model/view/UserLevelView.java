package moonstone.user.model.view;

import lombok.Getter;
import moonstone.user.model.UserRelationEntity;


/**
 * 上级关联, 用于用户判断上下级, 高度复用的层次关系
 */
@Getter
public class UserLevelView {
    UserRelationEntity relation;

    public UserLevelView(UserRelationEntity entity) {
        this.relation = entity;
        relation.setType(UserRelationEntity.UserRelationType.SUPER.getType());
    }

    public Long getShopId() {
        return relation.getAdditionRelationA();
    }

    public void setShopId(Long shopId) {
        relation.setAdditionRelationA(shopId);
    }

    public Long getUserId() {
        return relation.getUserId();
    }

    public void setUserId(Long userId) {
        relation.setUserId(userId);
    }

    public Long getSupperUserId() {
        return relation.getRelationId();
    }

    public void setSupperUserId(Long supperUserId) {
        relation.setRelationId(supperUserId);
    }

    public void setLevel(Long level){
        relation.setAdditionRelationB(level);
    }

    public Long getLevel(){
        return relation.getAdditionRelationB();
    }
}
