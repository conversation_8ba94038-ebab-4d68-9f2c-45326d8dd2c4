package moonstone.web.front.open.constant;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Component
@PropertySource(value = {"classpath:/thirdPartyExpress.properties"})
@ConfigurationProperties(prefix = "third-party-express")
public class ThirdPartyExpress {
    private Map<String, String> Y800;
}
