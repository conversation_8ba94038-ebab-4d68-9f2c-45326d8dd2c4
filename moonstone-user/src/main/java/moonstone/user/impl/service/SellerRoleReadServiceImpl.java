package moonstone.user.impl.service;

import com.google.common.base.Throwables;
import io.terminus.boot.rpc.common.annotation.RpcProvider;
import io.terminus.common.model.PageInfo;
import io.terminus.common.model.Paging;
import io.terminus.common.model.Response;
import lombok.extern.slf4j.Slf4j;
import moonstone.user.impl.dao.SubSellerRoleDao;
import moonstone.user.model.SubSellerRole;
import moonstone.user.service.SellerRoleReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RpcProvider
public class SellerRoleReadServiceImpl implements SellerRoleReadService {

    private final SubSellerRoleDao subSellerRoleDao;

    @Autowired
    public SellerRoleReadServiceImpl(SubSellerRoleDao subSellerRoleDao) {
        this.subSellerRoleDao = subSellerRoleDao;
    }

    @Override
    public Response<SubSellerRole> findById(Long id) {
        try {
            return Response.ok(subSellerRoleDao.findById(id));
        } catch (Exception e) {
            log.error("find seller role by id={} failed, cause:{}",
                    id, Throwables.getStackTraceAsString(e));
            return Response.fail("seller.role.find.fail");
        }
    }

    @Override
    public Response<List<SubSellerRole>> findByIds(List<Long> ids) {
        try {
            return Response.ok(subSellerRoleDao.findByIds(ids));
        } catch (Exception e) {
            log.error("find seller role by ids={} failed, cause:{}",
                    ids, Throwables.getStackTraceAsString(e));
            return Response.fail("seller.role.find.fail");
        }
    }

    @Override
    public Response<List<SubSellerRole>> findByMasterUserIdAndStatus(Long masterUserId, Integer status) {
        try {
            return Response.ok(subSellerRoleDao.findByMasterUserIdAndStatus(masterUserId, status));
        } catch (Exception e) {
            log.error("find seller roles by masterUserId={} failed, cause:{}",
                    masterUserId, Throwables.getStackTraceAsString(e));
            return Response.fail("seller.role.find.fail");
        }
    }

    @Override
    public Response<List<SubSellerRole>> findByMasterUserIdAndName(Long masterUserId, String name) {
        try {
            return Response.ok(subSellerRoleDao.findByMasterUserIdAndName(masterUserId, name));
        } catch (Exception e) {
            log.error("find seller roles by masterUserId={} and name={} failed, cause:{}",
                    masterUserId, name, Throwables.getStackTraceAsString(e));
            return Response.fail("seller.role.find.fail");
        }
    }

    @Override
    public Response<List<SubSellerRole>> findByMasterUserIdAndNames(Long masterUserId, List<String> nameList) {
        try {
            return Response.ok(subSellerRoleDao.findByMasterUserIdAndNames(masterUserId, nameList));
        } catch (Exception e) {
            log.error("find seller roles by masterUserId={} and name={} failed, cause:{}",
                    masterUserId, nameList, Throwables.getStackTraceAsString(e));
            return Response.fail("seller.role.find.fail");
        }
    }

    @Override
    public Response<Paging<SubSellerRole>> pagination(Long masterUserId, Integer status, Integer pageNo, Integer size) {
        try {
            PageInfo page = new PageInfo(pageNo, size);
            SubSellerRole criteria = new SubSellerRole();
            criteria.setMasterUserId(masterUserId);
            criteria.setStatus(status);
            return Response.ok(subSellerRoleDao.paging(page.getOffset(), page.getLimit(), criteria));
        } catch (Exception e) {
            log.error("paging seller roles failed, masterUserId={}, status={}, pageNo={}, size={}, cause:{}",
                    masterUserId, status, pageNo, size, Throwables.getStackTraceAsString(e));
            return Response.fail("seller.role.paging.fail");
        }
    }

    @Override
    public Response<SubSellerRole> findByUserIdAndType(Long userId, Integer type) {
        try {

            return Response.ok(subSellerRoleDao.findByUserIdAndType(userId, type));
        } catch (Exception e) {
            log.error("paging seller roles failed, userId={}, type={} , cause:{}",
                    userId, type, Throwables.getStackTraceAsString(e));
            return Response.fail("seller.role.paging.fail");
        }
    }

    @Override
    public Response<SubSellerRole> findByUserId(Long userId) {
        try {

            return Response.ok(subSellerRoleDao.findByUserId(userId));
        } catch (Exception e) {
            log.error("paging seller roles failed, userId={},  cause:{}",
                    userId, Throwables.getStackTraceAsString(e));
            return Response.fail("seller.role.paging.fail");
        }
    }
}
