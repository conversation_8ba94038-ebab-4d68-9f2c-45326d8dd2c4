package moonstone.wxOpen.service;

import me.chanjar.weixin.open.bean.message.WxOpenXmlMessage;
import me.chanjar.weixin.open.bean.result.WxOpenAuthorizerInfoResult;
import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;

/**
 * Created by CaiZhy on 2018/10/30.
 */
public interface WxOpenParanaComponentService {
    boolean checkSignature(String timestamp, String nonce, String signature);

    String decryptToXml(String encryptedXml, String timestamp, String nonce, String msgSignature);

    String encryptFromXml(String plainXml);

    String route(WxOpenXmlMessage wxMessage) throws Exception;

    String getComponentAccessToken(boolean forceRefresh) throws Exception;

    String getAuthorizerAccessToken(String appId, boolean forceRefresh) throws Exception;

    String getPreAuthUrl(String redirectURI) throws Exception;

    String getPreAuthUrl(String redirectURI, String authType, String bizAppid) throws Exception;

    WxOpenQueryAuthResult getQueryAuth(String authorizationCode) throws Exception;

    WxOpenAuthorizerInfoResult getAuthorizerInfo(String authorizerAppid) throws Exception;

    String modifyDomain(String authorizerAppid) throws Exception;
}
