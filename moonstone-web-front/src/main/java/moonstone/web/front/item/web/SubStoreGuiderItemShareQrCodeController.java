package moonstone.web.front.item.web;

import com.alibaba.fastjson.JSON;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import moonstone.cache.ItemCacheHolder;
import moonstone.cache.SkuCacheHolder;
import moonstone.common.enums.BondedType;
import moonstone.common.utils.Translate;
import moonstone.common.utils.UserUtil;
import moonstone.item.model.Item;
import moonstone.item.model.Sku;
import moonstone.item.model.SkuCustom;
import moonstone.item.service.SkuCustomReadService;
import moonstone.shop.model.SubStoreTStoreGuider;
import moonstone.shop.service.SubStoreTStoreGuiderReadService;
import moonstone.shopWxa.model.ShopWxa;
import moonstone.web.core.component.cache.ShopWxaCacheHolder;
import moonstone.web.core.component.qrCode.ShareImageUtil;
import moonstone.web.core.component.wx.WxAccessTokenCacheHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/api/subStore/item/share")
@AllArgsConstructor
public class SubStoreGuiderItemShareQrCodeController {
    SkuCustomReadService skuCustomReadService;
    ItemCacheHolder itemCacheHolder;
    SkuCacheHolder skuCacheHolder;
    SubStoreTStoreGuiderReadService subStoreTStoreGuiderReadService;
    WxAccessTokenCacheHolder wxAccessTokenCacheHolder;
    ShopWxaCacheHolder shopWxaCacheHolder;

    @GetMapping
    public void generateShareQrCode(Long skuId, Long projectId, HttpServletResponse response) throws IOException {
        Long userId = UserUtil.getUserId();

        Sku sku = skuCacheHolder.findSkuById(skuId);
        Long itemId = sku.getItemId();
        Item item = itemCacheHolder.findItemById(itemId);
        Long shopId = item.getShopId();
        Long subStoreId = subStoreTStoreGuiderReadService.findByStoreGuiderUserId(userId, shopId).getResult().stream().filter(valid -> valid.getStatus() > 0)
                .findFirst().map(SubStoreTStoreGuider::getSubStoreId).orElse(null);


        BufferedImage image = ShareImageUtil.createItemShareImageForGongXiao(item.getMainImage_(), fetchWeiXinQrCode(projectId, userId, subStoreId, skuId, item.getShopId()), item.getName(),
                BigDecimal.valueOf(sku.getPrice()).divide(new BigDecimal("100"),2,BigDecimal.ROUND_UP),
                BondedType.GENERAL_TRADE.getCode().equals(item.getIsBonded()) ? "中国"
                        : Optional.ofNullable(skuCustomReadService.findBySkuId(sku.getId())).map(SkuCustom::getCustomOrigin).orElse("海外优品")
                , "扫描或长按识别二维码");
        response.setHeader("Cache-Control", "no-cache, must-revalidate");
        response.setContentType("image/png");
        ImageIO.write(image,"png", response.getOutputStream());
    }

    private BufferedImage fetchWeiXinQrCode(Long projectId, Long userId, Long subStoreId, Long skuId, Long shopId) throws IOException {
        ShopWxa shopWxa = shopWxaCacheHolder.findShopWxaByProjectId(projectId);
        String accessToken = wxAccessTokenCacheHolder.getAccessToken(shopWxa.getAppId(), shopWxa.getAppSecret()).take();
        String getWXJumpImageApiUrl = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token=" + accessToken;
        String scene = "s=" + subStoreId + ",g=" + userId + ",p=" + shopId + ",u=" + skuId;
        Map<String, String> args =
                ImmutableMap.of("page", "pages/goods_detail", "scene", scene, "width", "280");
        HttpRequest request = HttpRequest.post(getWXJumpImageApiUrl).contentType("application/json", "utf-8").send(JSON.toJSONBytes(args));
        if (request.ok()) {
            ByteArrayOutputStream buff = new ByteArrayOutputStream();
            InputStream stream =request.stream();
            for (int i = stream.read(); i >= 0; i = stream.read()) {
                buff.write(i);
            }
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(buff.toByteArray()));
            if (image == null){
                log.error("Fail to fetch the image for App -> {}", buff);
            }
            return image;
        }
        throw Translate.exceptionOf("获取微信分享图片失败");
    }
}
